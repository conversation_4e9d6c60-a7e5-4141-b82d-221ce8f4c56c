# UserCustomEntity系统快速开始指南

## 📋 概述

UserCustomEntity系统现在已经完全配置好，包含完整的entity.yml配置文件。本指南将帮助您快速开始使用idc1适配功能。

## 🚀 快速开始

### 1. 文件结构确认

确保以下文件已正确创建：

```
DeathZombieV4/
├── src/main/java/org/Ver_zhzh/customZombie/UserCustomEntity/
│   ├── UserCustomEntity.java                    ✅ 主要实体管理类
│   ├── EntityOverrideConfig.java               ✅ 配置覆盖类
│   ├── AdvancedEntitySkillHandler.java         ✅ 技能处理器
│   ├── DualEntitySystemManager.java            ✅ 双系统管理器
│   ├── UserCustomEntityIntegration.java        ✅ 系统集成类
│   ├── UserCustomEntityCommand.java            ✅ 命令处理器
│   └── UserCustomEntityTest.java               ✅ 测试类
└── src/main/resources/
    └── entity.yml                              ✅ 完整配置文件
```

### 2. 配置文件说明

**entity.yml** 现在包含：

- ✅ **系统总开关配置** - 控制整个系统的启用状态
- ✅ **idc1完整配置** - 变异僵尸01的详细设置
- ✅ **idc2-idc25预留配置** - 为后续适配准备
- ✅ **性能优化配置** - 系统性能相关设置
- ✅ **兼容性配置** - 与原有系统的兼容设置
- ✅ **调试配置** - 详细的调试和日志选项

### 3. idc1配置详情

当前idc1（变异僵尸01）配置：

```yaml
idc1:
  enabled: true                    # ✅ 已启用
  health_override: 120.0          # 生命值120
  damage_override: 10.0           # 伤害10
  speed_multiplier: 1.3           # 1.3倍速度
  entity_type_override: "ZOMBIFIED_PIGLIN"  # 僵尸猪人形态
  
  # 装备：锋利2+火焰附加1的铁剑 + 全套保护1皮革装备
  weapon_override: "IRON_SWORD"
  weapon_enchantments:
    SHARPNESS: 2
    FIRE_ASPECT: 1
    
  # 特殊能力：剧毒攻击、自愈、火焰抗性等
  special_abilities:
    poison_enabled: true          # 剧毒攻击
    poison_chance: 0.8           # 80%概率
    regeneration_enabled: true    # 自愈能力
    fire_resistance: true        # 火焰抗性
```

## 🔧 集成到主插件

### 1. 在主插件类中添加

```java
public class DeathZombieV4 extends JavaPlugin {
    private UserCustomEntityIntegration entityIntegration;
    
    @Override
    public void onEnable() {
        // 初始化UserCustomEntity系统
        entityIntegration = new UserCustomEntityIntegration(this);
        
        // 获取原有的实体生成器实例
        Object originalSpawner = getOriginalEntitySpawner(); // 您需要实现这个方法
        
        // 初始化系统
        if (entityIntegration.initialize(originalSpawner)) {
            getLogger().info("UserCustomEntity系统初始化成功");
            
            // 注册命令
            getCommand("uce").setExecutor(new UserCustomEntityCommand(entityIntegration));
        } else {
            getLogger().severe("UserCustomEntity系统初始化失败");
        }
    }
    
    // 提供公共接口供其他类使用
    public UserCustomEntityIntegration getEntityIntegration() {
        return entityIntegration;
    }
}
```

### 2. 在plugin.yml中添加命令

```yaml
commands:
  uce:
    description: UserCustomEntity system commands
    usage: /uce <subcommand>
    permission: deathzombie.admin
```

## 🧪 测试idc1功能

### 1. 使用命令测试

```bash
# 查看系统状态
/uce status

# 重载配置
/uce reload

# 运行完整测试
/uce test

# 测试idc1生成
/uce test idc1

# 生成idc1实体
/uce spawn idc1

# 查看idc1配置信息
/uce info idc1

# 列出支持的实体
/uce list
```

### 2. 编程方式测试

```java
// 获取集成实例
UserCustomEntityIntegration integration = plugin.getEntityIntegration();

// 生成idc1实体
Location location = player.getLocation();
LivingEntity entity = integration.spawnCustomEntity(location, "idc1");

if (entity != null) {
    player.sendMessage("§a成功生成idc1实体！");
    
    // 验证属性
    player.sendMessage("§7生命值: " + entity.getHealth() + "/" + entity.getMaxHealth());
    player.sendMessage("§7名称: " + entity.getCustomName());
    player.sendMessage("§7类型: " + entity.getType().name());
} else {
    player.sendMessage("§c生成失败！");
}
```

## 🔍 验证功能

### 1. 基础属性验证

- ✅ 实体类型：ZOMBIFIED_PIGLIN
- ✅ 生命值：120.0
- ✅ 名称：带特效的强化变异僵尸
- ✅ 装备：锋利2铁剑 + 保护1皮革套装

### 2. 特殊能力验证

- ✅ 剧毒攻击：攻击玩家时80%概率施加2级剧毒4秒
- ✅ 自愈能力：每5秒自动回复生命值
- ✅ 火焰抗性：免疫火焰伤害
- ✅ 速度增强：1.3倍移动速度

### 3. 元数据验证

- ✅ userCustomEntity：标记为用户自定义实体
- ✅ idcZombieEntity：标记为IDC僵尸实体
- ✅ poisonAttacker：标记为剧毒攻击者
- ✅ gameEntity：标记为游戏实体

## 🛠️ 自定义配置

### 1. 修改idc1属性

编辑 `entity.yml` 中的idc1配置：

```yaml
idc1:
  enabled: true
  health_override: 150.0          # 修改生命值
  damage_override: 12.0           # 修改伤害
  speed_multiplier: 1.5           # 修改速度倍数
  custom_name_override: "§4§l我的自定义僵尸"  # 修改名称
  
  # 修改特殊能力
  special_abilities:
    poison_level: 2               # 提升剧毒等级到3级
    poison_chance: 1.0            # 100%剧毒概率
    critical_chance: 0.25         # 25%暴击率
```

### 2. 重载配置

```bash
/uce reload
```

### 3. 测试修改效果

```bash
/uce test idc1
```

## 📊 系统监控

### 1. 查看系统状态

```bash
/uce status
```

输出示例：
```
双实体系统状态:
- 初始化状态: true
- 用户自定义系统启用: true
- 默认系统启用: false
- 优先级策略: user_first
- 调试模式: true
- 已加载配置数量: 1
```

### 2. 查看实体配置

```bash
/uce info idc1
```

### 3. 查看日志

检查控制台输出，调试模式下会显示详细的生成和配置信息。

## 🚨 故障排除

### 1. 实体生成失败

**问题**：`/uce spawn idc1` 返回失败

**解决方案**：
1. 检查 `entity.yml` 中 `idc1.enabled` 是否为 `true`
2. 执行 `/uce reload` 重载配置
3. 检查控制台错误日志
4. 验证实体类型 `ZOMBIFIED_PIGLIN` 是否在当前版本中可用

### 2. 配置不生效

**问题**：修改配置后没有效果

**解决方案**：
1. 确保配置文件格式正确（YAML语法）
2. 执行 `/uce reload` 重载配置
3. 检查 `system_settings.use_user_custom_settings` 是否为 `true`

### 3. 权限问题

**问题**：无法使用命令

**解决方案**：
1. 确保有 `deathzombie.admin` 权限
2. 检查权限插件配置

## 🎯 下一步计划

1. **测试idc1功能** - 验证所有特性正常工作
2. **适配idc2** - 按照相同模式适配变异僵尸02
3. **集成到Web界面** - 在zombie-spawn-vue.js中添加支持
4. **性能优化** - 根据测试结果优化性能

## 📞 技术支持

如果遇到问题：

1. 查看控制台日志
2. 启用调试模式（`debug_mode: true`）
3. 使用测试命令验证功能
4. 检查配置文件格式

---

**恭喜！** 🎉 您现在拥有了一个完整的UserCustomEntity系统，可以开始测试和使用idc1的自定义功能了！
