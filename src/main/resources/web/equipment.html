<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeathZombie - 初始装备设置</title>
    <link rel="stylesheet" href="equipment.css">
</head>
<body>
    <div class="container">
        <h1>DeathZombie - 初始装备设置</h1>
        <div id="game-selector">
            <label for="game-select">选择游戏:</label>
            <select id="game-select"></select>
            <button id="load-game">加载</button>
        </div>

        <div id="equipment-container" class="hidden">
            <h2>初始装备设置 - <span id="game-name"></span></h2>

            <div class="inventory-grid">
                <div class="inventory-row">
                    <div class="slot" data-slot="1">
                        <img src="https://mc-heads.net/item/iron_sword" alt="剑槽" class="item-image">
                        <div class="slot-label">剑槽</div>
                    </div>
                    <div class="slot" data-slot="2">
                        <img src="https://mc-heads.net/item/bow" alt="枪槽" class="item-image">
                        <div class="slot-label">枪槽</div>
                    </div>
                    <div class="slot" data-slot="3">
                        <img src="https://mc-heads.net/item/barrier" alt="空槽" class="item-image">
                        <div class="slot-label">空槽</div>
                    </div>
                </div>
                <div class="inventory-row">
                    <div class="slot" data-slot="4">
                        <img src="https://mc-heads.net/item/barrier" alt="空槽" class="item-image">
                        <div class="slot-label">空槽</div>
                    </div>
                    <div class="slot" data-slot="5">
                        <img src="https://mc-heads.net/item/barrier" alt="空槽" class="item-image">
                        <div class="slot-label">空槽</div>
                    </div>
                    <div class="slot" data-slot="6">
                        <img src="https://mc-heads.net/item/barrier" alt="空槽" class="item-image">
                        <div class="slot-label">空槽</div>
                    </div>
                </div>
                <div class="inventory-row">
                    <div class="slot" data-slot="7">
                        <img src="https://mc-heads.net/item/potion" alt="物品槽" class="item-image">
                        <div class="slot-label">物品槽</div>
                    </div>
                    <div class="slot" data-slot="8">
                        <img src="https://mc-heads.net/item/potion" alt="物品槽" class="item-image">
                        <div class="slot-label">物品槽</div>
                    </div>
                    <div class="slot" data-slot="9">
                        <img src="https://mc-heads.net/item/potion" alt="物品槽" class="item-image">
                        <div class="slot-label">物品槽</div>
                    </div>
                </div>
            </div>

            <div id="item-selector" class="hidden">
                <h3>选择物品 - <span id="selected-slot"></span></h3>
                <div class="item-grid">
                    <!-- 物品将通过JavaScript动态添加 -->
                </div>
                <button id="remove-item">移除物品</button>
                <button id="cancel-selection">取消</button>
            </div>

            <div class="button-row">
                <button id="save-equipment">保存装备设置</button>
                <button id="back-to-main">返回主页</button>
            </div>
        </div>
    </div>

    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <script src="equipment.js"></script>
</body>
</html>
