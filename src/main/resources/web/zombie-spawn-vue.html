<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器 - 回合出怪设置</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- 引入图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- 引入Google字体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap">
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="css/zombie-spawn-vue.css">
    <!-- 引入字体重置样式 (必须在最后) -->
    <link rel="stylesheet" href="css/font-reset.css">
</head>
<body>
    <!-- 调试信息面板 (默认隐藏，按Ctrl+Shift+D显示) -->
    <div id="debug-info" style="display: none; position: fixed; bottom: 0; right: 0; width: 400px; height: 300px; background: rgba(0,0,0,0.8); color: #00ff00; padding: 10px; overflow: auto; z-index: 10000; font-family: monospace; font-size: 12px;">
        <h3>调试信息</h3>
        <div>按Ctrl+Shift+D隐藏此面板</div>
        <hr>
    </div>

    <!-- 错误信息 -->
    <div id="error-container" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.3); z-index: 10001;">
        <h3 style="color: #e74c3c;">出错了</h3>
        <p id="error-message"></p>
        <button onclick="location.reload()">刷新页面</button>
    </div>

    <!-- 主应用容器 -->
    <div id="app">
        <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">{{ loadingMessage }}</div>
        </div>

        <!-- 备用内容，当Vue加载失败时显示 -->
        <noscript>
            <div style="text-align: center; padding: 50px;">
                <h2>僵尸末日 - 回合出怪设置</h2>
                <p>请启用JavaScript以使用此应用程序</p>
            </div>
        </noscript>

        <!-- 主界面 -->
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <span class="fancy-letter">DZ</span>
                        <span>僵尸末日网页编辑器</span>
                    </div>
                </div>

                <div class="sidebar-nav">
                    <div class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="bi bi-house"></i>
                            <span>主页</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="equipment-vue.html" class="nav-link">
                            <i class="bi bi-shield"></i>
                            <span>初始装备设置</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="monster-preset-vue.html" class="nav-link">
                            <i class="bi bi-collection"></i>
                            <span>怪物预设管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="bi bi-bug"></i>
                            <span>回合出怪设置</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 顶部导航栏 -->
                <div class="top-navbar">
                    <div class="page-title">
                        <i class="bi bi-bug"></i>
                        <span>僵尸末日网页编辑器 - 回合出怪设置</span>
                    </div>
                    <div class="navbar-actions">
                        <button v-if="selectedGame" class="btn btn-success me-2" @click="saveSpawnSettings">
                            <i class="bi bi-save"></i>
                            <span>保存设置</span>
                        </button>
                        <button class="btn btn-outline-danger" @click="logout">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </button>
                    </div>
                </div>

                <!-- 主内容区 -->
                <div class="dashboard-header">
                    <h1 class="dashboard-title">回合出怪设置</h1>
                    <div class="dashboard-actions">
                        <div class="game-selector">
                            <select v-model="selectedGame" @change="loadGameSpawnSettings" class="form-select">
                                <option value="">选择游戏</option>
                                <option v-for="game in games" :key="game" :value="game">{{ game }}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 游戏选择提示 -->
                <div v-if="!selectedGame" class="dashboard-card animate-slide-up">
                    <div class="card-body text-center p-5">
                        <i class="bi bi-arrow-up-circle display-1 text-muted mb-3"></i>
                        <h3>请选择一个游戏</h3>
                        <p class="text-muted">从上方下拉菜单中选择一个游戏来配置回合出怪设置</p>
                    </div>
                </div>

                <!-- 回合模式设置 -->
                <div v-if="selectedGame" class="dashboard-card animate-slide-up">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-sliders"></i>
                            <span>回合模式设置</span>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline-primary" @click="updateRounds">
                                <i class="bi bi-arrow-repeat"></i>
                                刷新回合数
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <i class="bi bi-info-circle"></i>
                            <span>回合模式用于设置每个回合的怪物生成规则。每个回合的每个生成点可以配置不同的怪物类型和数量。</span>
                        </div>

                        <!-- 回合选择器 -->
                        <div class="round-selector">
                            <div class="round-selector-header">
                                <h3>选择回合</h3>
                                <span class="round-count">共 {{ totalRounds }} 回合</span>
                            </div>
                            <div class="round-buttons">
                                <button v-for="i in totalRounds" :key="i"
                                        class="round-button"
                                        :class="{ 'active': selectedRound === i }"
                                        @click="selectRound(i)">
                                    {{ i }}
                                </button>
                            </div>
                        </div>

                        <!-- 批量设置 -->
                        <div class="batch-settings">
                            <div class="batch-settings-header">
                                <h3>批量设置</h3>
                            </div>
                            <div class="batch-settings-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>起始回合</label>
                                        <input type="number" v-model="batchSettings.startRound" min="1" :max="totalRounds" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label>结束回合</label>
                                        <input type="number" v-model="batchSettings.endRound" min="1" :max="totalRounds" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label>生成点</label>
                                        <select v-model="batchSettings.spawnPoint" class="form-control">
                                            <option value="">选择生成点</option>
                                            <option v-for="spawn in spawnPoints" :key="spawn" :value="spawn">
                                                {{ spawn }} - {{ getSpawnPointInfo(spawn).world }} {{ formatCoordinates(getSpawnPointInfo(spawn).x, getSpawnPointInfo(spawn).y, getSpawnPointInfo(spawn).z) }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>怪物类型</label>
                                        <select v-model="batchSettings.monsterType" class="form-control">
                                            <option value="zombie">普通僵尸</option>
                                            <option value="entity">特殊实体</option>
                                            <option value="npc">NPC</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>怪物ID</label>
                                        <select v-model="batchSettings.monsterId" class="form-control">
                                            <option v-for="monster in getMonsterPresets(batchSettings.monsterType)"
                                                    :key="monster.id"
                                                    :value="monster.id">
                                                {{ monster.name }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>数量</label>
                                        <input type="text" v-model="batchSettings.count" class="form-control" placeholder="数字或random">
                                    </div>
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button class="btn btn-primary btn-block" @click="applyBatchSettings">应用批量设置</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 回合详情 -->
                        <div v-if="selectedRound" class="round-details">
                            <div class="round-details-header">
                                <h3>第 {{ selectedRound }} 回合设置</h3>
                                <button class="btn btn-outline-primary" @click="openPresetManager">
                                    <i class="bi bi-collection"></i>
                                    预设管理
                                </button>
                            </div>

                            <!-- 生成点列表 -->
                            <div class="spawn-points-container">
                                <div v-for="(spawnPoint, index) in spawnPoints" :key="index" class="spawn-point-item">
                                    <div class="spawn-point-header">
                                        <div class="spawn-point-info" @click="toggleSpawnPoint(spawnPoint)">
                                            <h4>{{ spawnPoint }}</h4>
                                            <div class="spawn-point-details">
                                                <span class="spawn-detail-item">
                                                    <i class="bi bi-globe"></i>
                                                    {{ getSpawnPointInfo(spawnPoint).world }}
                                                </span>
                                                <span class="spawn-detail-item">
                                                    <i class="bi bi-geo-alt"></i>
                                                    {{ formatCoordinates(getSpawnPointInfo(spawnPoint).x, getSpawnPointInfo(spawnPoint).y, getSpawnPointInfo(spawnPoint).z) }}
                                                </span>
                                                <span class="spawn-detail-item" :class="getSpawnPointInfo(spawnPoint).enabled ? 'enabled' : 'disabled'">
                                                    <i :class="getSpawnPointInfo(spawnPoint).enabled ? 'bi bi-check-circle-fill' : 'bi bi-x-circle-fill'"></i>
                                                    {{ getSpawnPointInfo(spawnPoint).enabled ? '已启用' : '已禁用' }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="spawn-point-actions">
                                            <button class="btn btn-sm btn-outline-success" @click.stop="openApplyPresetModal(selectedRound, spawnPoint)" title="应用预设">
                                                <i class="bi bi-collection"></i>
                                            </button>
                                            <i :class="expandedSpawnPoints[spawnPoint] ? 'bi bi-chevron-up' : 'bi bi-chevron-down'" @click="toggleSpawnPoint(spawnPoint)"></i>
                                        </div>
                                    </div>

                                    <!-- 生成点的怪物配置 -->
                                    <div v-if="expandedSpawnPoints[spawnPoint]" class="spawn-point-config">
                                        <div v-for="(config, configKey) in getSpawnPointConfigs(selectedRound, spawnPoint)" :key="configKey" class="monster-config">
                                            <div class="monster-config-header">
                                                <span>{{ configKey }}</span>
                                                <div class="monster-config-actions">
                                                    <button class="btn btn-sm btn-danger" @click="removeMonsterConfig(selectedRound, spawnPoint, configKey)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="monster-config-body">
                                                <div class="form-group">
                                                    <label>怪物类型</label>
                                                    <select v-model="config.monsterType" class="form-control">
                                                        <option value="zombie">普通僵尸</option>
                                                        <option value="entity">特殊实体</option>
                                                        <option value="npc">NPC</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label>怪物ID</label>
                                                    <select v-model="config.monsterId" class="form-control">
                                                        <option v-for="monster in getMonsterPresets(config.monsterType)"
                                                                :key="monster.id"
                                                                :value="monster.id">
                                                            {{ monster.name }}
                                                        </option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label>数量</label>
                                                    <input type="text" v-model="config.count" class="form-control" placeholder="数字或random">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 添加新的怪物配置 -->
                                        <div class="add-monster-config">
                                            <button class="btn btn-outline-primary btn-block" @click="addMonsterConfig(selectedRound, spawnPoint)">
                                                <i class="bi bi-plus-circle"></i> 添加怪物配置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预设应用模态框 -->
        <div v-if="presetManager.showApplyModal" class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h2>应用怪物预设</h2>
                    <button class="btn-close" @click="presetManager.showApplyModal = false">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>选择要应用到第{{ presetManager.selectedRound }}回合{{ presetManager.selectedSpawnPoint }}的预设：</p>

                    <!-- 用户预设列表 -->
                    <div class="user-presets">
                        <div v-if="USER_PRESETS.length === 0" class="no-presets" style="text-align: center; padding: 30px; color: #6b7280;">
                            <i class="bi bi-collection" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <p>您还没有创建任何预设</p>
                            <a href="monster-preset-vue.html" class="btn btn-primary mt-3">
                                <i class="bi bi-plus-circle"></i> 前往创建预设
                            </a>
                        </div>

                        <div v-else class="preset-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
                            <div v-for="(preset, index) in USER_PRESETS" :key="index"
                                 class="preset-card"
                                 style="border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; cursor: pointer;"
                                 @click="applyPresetToRound(preset)">
                                <div class="preset-card-header" style="background: #f9fafb; padding: 12px 15px; border-bottom: 1px solid #e5e7eb;">
                                    <h5 style="margin: 0; font-weight: 600;">{{ preset.name }}</h5>
                                </div>
                                <div class="preset-card-body" style="padding: 15px;">
                                    <p v-if="preset.description" style="color: #6b7280; margin-bottom: 10px;">{{ preset.description }}</p>
                                    <div class="preset-monsters">
                                        <div v-for="(monster, mIndex) in preset.monsters" :key="mIndex"
                                             class="preset-monster-item"
                                             style="padding: 8px; background: #f3f4f6; border-radius: 4px; margin-bottom: 5px; font-size: 0.875rem;">
                                            <span>{{ monster.monsterType === 'zombie' ? '僵尸' : (monster.monsterType === 'entity' ? '实体' : '感染者') }}: {{ getMonsterName(monster.monsterType, monster.monsterId) }}</span>
                                            <span style="float: right;">数量: {{ monster.count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="presetManager.showApplyModal = false">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入Vue脚本 -->
    <script src="js/zombie-spawn-vue.js"></script>
</body>
</html>
