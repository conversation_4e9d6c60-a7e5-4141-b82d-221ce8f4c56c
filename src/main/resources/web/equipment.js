// 身份验证相关函数
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        console.log('未找到身份验证令牌，需要登录');
        // 如果当前不在登录页面，则重定向到登录页面
        if (!window.location.href.includes('login.html')) {
            window.location.href = 'login.html';
        }
        return false;
    }

    console.log('找到身份验证令牌');
    return true;
}

// 带身份验证的fetch请求
function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('auth_token');

    if (!token) {
        throw new Error('未登录，无法发送请求');
    }

    // 合并选项
    const authOptions = {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        }
    };

    return fetch(url, authOptions)
        .then(response => {
            // 如果返回401未授权，可能是令牌过期
            if (response.status === 401) {
                localStorage.removeItem('auth_token');
                window.location.href = 'login.html?error=session_expired';
                throw new Error('会话已过期，请重新登录');
            }

            return response;
        })
        .catch(error => {
            console.error('请求出错:', error);
            throw error;
        });
}

document.addEventListener('DOMContentLoaded', function() {
    // 检查身份验证状态
    if (!checkAuth()) {
        return;
    }

    // 元素引用
    const gameSelect = document.getElementById('game-select');
    const loadGameBtn = document.getElementById('load-game');
    const equipmentContainer = document.getElementById('equipment-container');
    const gameNameSpan = document.getElementById('game-name');
    const itemSelector = document.getElementById('item-selector');
    const selectedSlotSpan = document.getElementById('selected-slot');
    const itemGrid = document.querySelector('.item-grid');
    const removeItemBtn = document.getElementById('remove-item');
    const cancelSelectionBtn = document.getElementById('cancel-selection');
    const saveEquipmentBtn = document.getElementById('save-equipment');
    const backToMainBtn = document.getElementById('back-to-main');

    // 状态变量
    let currentGame = '';
    let currentSlot = null;
    let equipmentData = {};

    // 物品数据 - 使用gameKit.yml中的ID
    const items = [
        // 武器类型 (id1-id24)
        { id: 'id1', name: '手枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id2', name: '步枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id3', name: '霰弹枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id4', name: '机枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id5', name: '火箭筒', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id6', name: '电击枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id7', name: '狙击步枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id8', name: '冷冻枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id9', name: '雷击枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id10', name: '压强枪', image: 'https://mc-heads.net/item/wooden_hoe' },
        { id: 'id11', name: '突击步枪', image: 'https://mc-heads.net/item/wooden_hoe' },

        // 物品类型 (id38-id67)
        { id: 'id38', name: '速度buff1药水', image: 'https://mc-heads.net/item/potion' },
        { id: 'id39', name: '跳跃buff1药水', image: 'https://mc-heads.net/item/potion' },
        { id: 'id40', name: '手枪弹药', image: 'https://mc-heads.net/item/iron_nugget' },
        { id: 'id41', name: '步枪弹药', image: 'https://mc-heads.net/item/iron_nugget' },
        { id: 'id42', name: '霰弹枪弹药', image: 'https://mc-heads.net/item/iron_nugget' },
        { id: 'id50', name: '金苹果', image: 'https://mc-heads.net/item/golden_apple' },
        { id: 'id65', name: '熟牛排', image: 'https://mc-heads.net/item/cooked_beef' },
        { id: 'id66', name: '治疗药水(1级)', image: 'https://mc-heads.net/item/potion' },

        // 特殊物品 (id67-id70)
        { id: 'id67', name: '武器槽位', image: 'https://mc-heads.net/item/barrier' },
        { id: 'id68', name: '空物品槽', image: 'https://mc-heads.net/item/barrier' },
        { id: 'id69', name: '锁定槽位', image: 'https://mc-heads.net/item/barrier' },
        { id: 'id70', name: '铁剑', image: 'https://mc-heads.net/item/iron_sword' }
    ];

    // 初始化
    loadGames();

    // 事件监听器
    loadGameBtn.addEventListener('click', loadGameEquipment);
    document.querySelectorAll('.slot').forEach(slot => {
        slot.addEventListener('click', () => selectSlot(slot));
    });
    removeItemBtn.addEventListener('click', removeItemFromSlot);
    cancelSelectionBtn.addEventListener('click', cancelSelection);
    saveEquipmentBtn.addEventListener('click', saveEquipment);
    backToMainBtn.addEventListener('click', () => window.location.href = 'index.html');

    // 加载游戏列表
    function loadGames() {
        fetchWithAuth('/api/games')
            .then(response => response.json())
            .then(data => {
                gameSelect.innerHTML = '';
                data.games.forEach(game => {
                    const option = document.createElement('option');
                    option.value = game.name;
                    option.textContent = game.name;
                    gameSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('加载游戏列表失败:', error);
                alert('加载游戏列表失败，请刷新页面重试');
            });
    }

    // 加载游戏装备
    function loadGameEquipment() {
        currentGame = gameSelect.value;
        if (!currentGame) {
            alert('请选择一个游戏');
            return;
        }

        fetchWithAuth(`/api/game?name=${currentGame}`)
            .then(response => response.json())
            .then(data => {
                gameNameSpan.textContent = currentGame;
                equipmentData = data.initialEquipment || {};

                // 重置所有槽位
                document.querySelectorAll('.slot').forEach(slot => {
                    const slotNumber = slot.getAttribute('data-slot');
                    const itemId = equipmentData[slotNumber];

                    // 移除之前的物品图片
                    const defaultImage = slot.querySelector('.item-image');

                    if (itemId) {
                        // 如果有物品，更新图片
                        const item = items.find(i => i.id === itemId);
                        if (item) {
                            defaultImage.src = item.image;
                            defaultImage.alt = item.name;
                        } else {
                            // 如果找不到预定义的物品，尝试使用MC-Heads API
                            const mcHeadsUrl = `https://mc-heads.net/item/${itemId}`;
                            defaultImage.src = mcHeadsUrl;
                            defaultImage.alt = itemId;
                        }
                        slot.classList.add('slot-with-item');
                        slot.setAttribute('data-item-id', itemId);
                    } else {
                        // 如果没有物品，恢复默认图片
                        if (slotNumber === '1') {
                            defaultImage.src = 'https://mc-heads.net/item/iron_sword';
                            defaultImage.alt = '剑槽';
                            // 设置默认值为id70（铁剑）
                            equipmentData[slotNumber] = 'id70';
                        } else if (slotNumber === '2') {
                            defaultImage.src = 'https://mc-heads.net/item/wooden_hoe';
                            defaultImage.alt = '枪槽';
                            // 设置默认值为id1（手枪）
                            equipmentData[slotNumber] = 'id1';
                        } else if (slotNumber === '7' || slotNumber === '8' || slotNumber === '9') {
                            defaultImage.src = 'https://mc-heads.net/item/potion';
                            defaultImage.alt = '物品槽';
                            // 设置默认值为id68（空物品槽）
                            equipmentData[slotNumber] = 'id68';
                        } else {
                            defaultImage.src = 'https://mc-heads.net/item/barrier';
                            defaultImage.alt = '空槽';
                            // 设置默认值为id69（锁定槽位）
                            equipmentData[slotNumber] = 'id69';
                        }
                        slot.classList.remove('slot-with-item');
                        slot.removeAttribute('data-item-id');
                    }
                });

                equipmentContainer.classList.remove('hidden');
            })
            .catch(error => {
                console.error('加载游戏装备失败:', error);
                alert('加载游戏装备失败，请重试');
            });
    }

    // 选择槽位
    function selectSlot(slot) {
        // 移除之前的选择
        document.querySelectorAll('.slot').forEach(s => s.classList.remove('selected'));

        // 选中当前槽位
        slot.classList.add('selected');
        currentSlot = slot.getAttribute('data-slot');
        selectedSlotSpan.textContent = `槽位 ${currentSlot}`;

        // 生成物品选择器
        generateItemGrid();

        // 显示物品选择器
        itemSelector.classList.remove('hidden');
    }

    // 生成物品选择器
    function generateItemGrid() {
        itemGrid.innerHTML = '';

        // 根据槽位筛选物品
        let filteredItems = items;
        if (currentSlot === '1') {
            // 剑槽显示铁剑(id70)和武器类物品(id1-id24)
            filteredItems = items.filter(item =>
                item.id.startsWith('id') && (
                    (parseInt(item.id.substring(2)) >= 1 && parseInt(item.id.substring(2)) <= 24) ||
                    parseInt(item.id.substring(2)) === 70
                )
            );
            // 确保铁剑(id70)排在最前面
            filteredItems.sort((a, b) => {
                if (a.id === 'id70') return -1;
                if (b.id === 'id70') return 1;
                return 0;
            });
        } else if (currentSlot === '2') {
            // 枪槽只显示武器类物品 (id1-id24)，但优先显示枪类武器 (id1-id24)，不包括铁剑
            filteredItems = items.filter(item =>
                item.id.startsWith('id') &&
                parseInt(item.id.substring(2)) >= 1 &&
                parseInt(item.id.substring(2)) <= 24
            );
        } else if (currentSlot >= '7' && currentSlot <= '9') {
            // 物品槽显示消耗品 (id38-id67)
            filteredItems = items.filter(item =>
                item.id.startsWith('id') &&
                parseInt(item.id.substring(2)) >= 38 &&
                parseInt(item.id.substring(2)) <= 67
            );
        }

        // 创建物品元素
        filteredItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'item';
            itemElement.setAttribute('data-id', item.id);

            const img = document.createElement('img');
            img.src = item.image;
            img.alt = item.name;

            itemElement.appendChild(img);
            itemElement.addEventListener('click', () => selectItem(item));

            // 添加悬停提示
            itemElement.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'item-tooltip';
                tooltip.textContent = `${item.name} (${item.id})`;
                tooltip.style.top = `${e.pageY - 30}px`;
                tooltip.style.left = `${e.pageX + 10}px`;
                document.body.appendChild(tooltip);
                itemElement.tooltip = tooltip;
            });

            itemElement.addEventListener('mousemove', (e) => {
                if (itemElement.tooltip) {
                    itemElement.tooltip.style.top = `${e.pageY - 30}px`;
                    itemElement.tooltip.style.left = `${e.pageX + 10}px`;
                }
            });

            itemElement.addEventListener('mouseleave', () => {
                if (itemElement.tooltip) {
                    itemElement.tooltip.remove();
                    itemElement.tooltip = null;
                }
            });

            itemGrid.appendChild(itemElement);
        });
    }

    // 选择物品
    function selectItem(item) {
        if (!currentSlot) return;

        // 更新数据
        equipmentData[currentSlot] = item.id;

        // 更新UI
        const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
        const img = slot.querySelector('.item-image');
        img.src = item.image;
        img.alt = item.name;
        slot.classList.add('slot-with-item');
        slot.setAttribute('data-item-id', item.id);

        // 隐藏物品选择器
        itemSelector.classList.add('hidden');
        slot.classList.remove('selected');
        currentSlot = null;
    }

    // 从槽位移除物品
    function removeItemFromSlot() {
        if (!currentSlot) return;

        // 更新UI
        const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
        const img = slot.querySelector('.item-image');

        // 恢复默认图片和设置默认值
        if (currentSlot === '1') {
            img.src = 'https://mc-heads.net/item/iron_sword';
            img.alt = '剑槽';
            // 设置默认值为id70（铁剑）
            equipmentData[currentSlot] = 'id70';
        } else if (currentSlot === '2') {
            img.src = 'https://mc-heads.net/item/wooden_hoe';
            img.alt = '枪槽';
            // 设置默认值为id1（手枪）
            equipmentData[currentSlot] = 'id1';
        } else if (currentSlot === '7' || currentSlot === '8' || currentSlot === '9') {
            img.src = 'https://mc-heads.net/item/potion';
            img.alt = '物品槽';
            // 设置默认值为id68（空物品槽）
            equipmentData[currentSlot] = 'id68';
        } else {
            img.src = 'https://mc-heads.net/item/barrier';
            img.alt = '空槽';
            // 设置默认值为id69（锁定槽位）
            equipmentData[currentSlot] = 'id69';
        }

        slot.classList.remove('slot-with-item');
        slot.removeAttribute('data-item-id');

        // 隐藏物品选择器
        itemSelector.classList.add('hidden');
        slot.classList.remove('selected');
        currentSlot = null;
    }

    // 取消选择
    function cancelSelection() {
        if (!currentSlot) return;

        const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
        slot.classList.remove('selected');
        currentSlot = null;

        // 隐藏物品选择器
        itemSelector.classList.add('hidden');
    }

    // 保存装备设置
    function saveEquipment() {
        if (!currentGame) {
            alert('请先选择一个游戏');
            return;
        }

        // 准备请求数据
        const requestData = {
            name: currentGame,
            initialEquipment: equipmentData
        };

        // 发送保存请求
        fetchWithAuth('/api/save-game', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('初始装备设置已保存');
            } else {
                alert('保存失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('保存装备设置失败:', error);
            alert('保存装备设置失败，请重试');
        });
    }
});
