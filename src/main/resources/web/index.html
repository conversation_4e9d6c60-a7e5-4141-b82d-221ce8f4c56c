<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入字体重置样式 (必须在最后) -->
    <link rel="stylesheet" href="css/font-reset.css">
    <!-- 添加兼容性脚本 -->
    <script>
        // 确保所有浏览器支持closest方法 (IE兼容)
        if (!Element.prototype.matches) {
            Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
        }
        if (!Element.prototype.closest) {
            Element.prototype.closest = function(s) {
                var el = this;
                do {
                    if (el.matches(s)) return el;
                    el = el.parentElement || el.parentNode;
                } while (el !== null && el.nodeType === 1);
                return null;
            };
        }
    </script>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载僵尸末日网页编辑器</div>
    </div>

    <!-- 浮动保存按钮 -->
    <button id="floating-save-btn" class="btn btn-primary btn-floating" style="display: none;">
        <i class="bi bi-check-circle me-1"></i>保存
    </button>

    <div class="container">
        <header class="d-flex justify-content-between align-items-center my-4 animate-left">
            <h1 class="text-center"><span class="fancy-letter">DZ</span> 僵尸末日网页编辑器</h1>
            <button id="logout-btn" class="btn btn-outline-danger">
                <i class="bi bi-box-arrow-right me-1"></i>退出登录
            </button>
        </header>

        <div class="alert alert-info animate-left delay-1" role="alert">
            <i class="bi bi-info-circle-fill me-2"></i>通过此界面，您可以方便地管理僵尸末日游戏的各项设置。
        </div>

        <div class="alert alert-success animate-left delay-2" role="alert">
            <i class="bi bi-gear-fill me-2"></i>快速链接:
            <a href="equipment-vue.html" class="btn btn-sm btn-outline-primary ms-2">初始装备设置</a>
            <a href="zombie-spawn-vue.html" class="btn btn-sm btn-outline-primary ms-2">回合出怪设置</a>
            <a href="monster-preset-vue.html" class="btn btn-sm btn-outline-primary ms-2">怪物预设管理</a>
        </div>

        <div class="row">
            <div class="col-md-4 animate-left delay-2">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-list-ul me-2"></i>游戏列表
                    </div>
                    <div class="card-body game-list-container">
                        <div id="game-list" class="list-group"></div>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <button id="reload-config-btn" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-repeat me-1"></i>刷新配置
                        </button>
                        <button id="create-game-btn" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>创建游戏
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-8 animate-right delay-3">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-gear-fill me-2"></i>游戏配置
                    </div>
                    <div class="card-body">
                        <div id="game-details">
                            <div class="alert alert-secondary" role="alert">
                                <i class="bi bi-arrow-left-circle me-2"></i>选择一个游戏以查看详情
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建游戏模态框 -->
    <div class="modal fade" id="create-game-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-plus-circle"></i> 创建新游戏</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="create-game-form">
                        <div class="mb-3">
                            <label for="game-name" class="form-label">游戏名称</label>
                            <input type="text" class="form-control" id="game-name" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-create-game">
                        <i class="bi bi-check-circle me-2"></i>创建
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加门模态框 -->
    <div class="modal fade" id="add-door-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-door-closed"></i> 添加新门</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-door-form">
                        <div class="mb-3">
                            <label for="door-name" class="form-label">门名称</label>
                            <input type="text" class="form-control" id="door-name" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="door-locked" checked>
                                <label class="form-check-label" for="door-locked">需要解锁</label>
                            </div>
                        </div>
                        <div class="mb-3" id="price-container">
                            <label for="door-price" class="form-label">解锁价格</label>
                            <input type="number" class="form-control" id="door-price" min="0" value="500">
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn region-selector-btn d-grid gap-2" id="get-region-btn">
                                <i class="bi bi-magic"></i>从调试棒选择的区域读取
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-add-door">
                        <i class="bi bi-check-circle me-2"></i>添加
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑门模态框 -->
    <div class="modal fade" id="edit-door-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-door-open"></i> 编辑门设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-door-form">
                        <input type="hidden" id="edit-door-index">
                        <div class="mb-3">
                            <label for="edit-door-name" class="form-label">门名称</label>
                            <input type="text" class="form-control" id="edit-door-name" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="edit-door-locked">
                                <label class="form-check-label" for="edit-door-locked">需要解锁</label>
                            </div>
                        </div>
                        <div class="mb-3" id="edit-price-container">
                            <label for="edit-door-price" class="form-label">解锁价格</label>
                            <input type="number" class="form-control" id="edit-door-price" min="0" value="500">
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn region-selector-btn d-grid gap-2" id="edit-get-region-btn">
                                <i class="bi bi-magic"></i>从调试棒选择的区域读取
                            </button>
                        </div>
                        <!-- 区域信息容器 -->
                        <div id="edit-region-info-container" style="display: none;">
                            <!-- 区域信息将在JavaScript中动态填充 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger me-auto" id="delete-door-btn">
                        <i class="bi bi-trash me-2"></i>删除
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-edit-door">
                        <i class="bi bi-check-circle me-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 回合模式设置模态框 -->
    <div class="modal fade" id="round-mode-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-sliders"></i> 回合模式设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="round-mode-form">
                        <input type="hidden" id="edit-round-number">
                        <input type="hidden" id="edit-spawn-name">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="round-mode-round" class="form-label">回合数</label>
                                <select class="form-select" id="round-mode-round" required>
                                    <!-- 回合数选项会在JS中动态生成 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="round-mode-spawn" class="form-label">生成点</label>
                                <select class="form-select" id="round-mode-spawn" required>
                                    <!-- 生成点选项会在JS中动态生成 -->
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="round-mode-monster-type" class="form-label">怪物类型</label>
                                <select class="form-select" id="round-mode-monster-type" required>
                                    <option value="zombie">僵尸</option>
                                    <option value="entity">实体</option>
                                    <option value="npc">感染者(npc)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="round-mode-monster-id" class="form-label">怪物ID</label>
                                <select class="form-select" id="round-mode-monster-id" required>
                                    <!-- 怪物ID选项会在JS中根据类型动态生成 -->
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="round-mode-count" class="form-label">生成数量</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="round-mode-count" required placeholder="数字或random">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        选项
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="#" data-value="random">随机数量</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="1">1个</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="3">3个</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="5">5个</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="10">10个</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div id="monster-info-container" class="alert alert-info">
                            <!-- 怪物信息会在JS中动态生成 -->
                            <p>请选择怪物类型和ID以查看详细信息</p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger me-auto" id="delete-round-mode-btn">
                        <i class="bi bi-trash me-2"></i>移除此设置
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-round-mode" onclick="event.preventDefault(); event.stopPropagation(); saveRoundMode(); return false;">
                        <i class="bi bi-check-circle me-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加窗户模态框 -->
    <div class="modal fade" id="add-window-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-window"></i> 添加窗户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-window-form">
                        <div class="mb-3">
                            <button type="button" class="btn region-selector-btn d-grid gap-2" id="get-window-region-btn">
                                <i class="bi bi-magic"></i>从调试棒选择的区域读取
                            </button>
                        </div>
                        <div id="window-region-info" class="alert alert-info d-none">
                            <!-- 区域信息将在JS中动态填充 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-add-window">
                        <i class="bi bi-check-circle me-2"></i>添加
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑窗户模态框 -->
    <div class="modal fade" id="edit-window-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-window"></i> 编辑窗户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-window-form">
                        <input type="hidden" id="edit-window-index">
                        <div class="mb-3">
                            <button type="button" class="btn region-selector-btn d-grid gap-2" id="edit-window-region-btn">
                                <i class="bi bi-magic"></i>从调试棒选择的区域读取
                            </button>
                        </div>
                        <div id="edit-window-region-info" class="alert alert-info">
                            <!-- 区域信息将在JS中动态填充 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger me-auto" id="delete-window-btn">
                        <i class="bi bi-trash me-2"></i>删除
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="confirm-edit-window">
                        <i class="bi bi-check-circle me-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <script src="main.js"></script>
</body>
</html>