/**
 * 身份验证模块
 * 处理登录、登出和身份验证状态检查
 */

// 检查用户是否已登录
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        console.log('未找到身份验证令牌，需要登录');
        // 如果当前不在登录页面，则重定向到登录页面
        if (!window.location.href.includes('login.html')) {
            window.location.href = 'login.html';
        }
        return false;
    }
    
    console.log('找到身份验证令牌');
    return true;
}

// 使用密码登录
async function login(password) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 登录成功，保存令牌
            localStorage.setItem('auth_token', data.token);
            return { success: true };
        } else {
            // 登录失败
            return { success: false, message: data.message || '登录失败' };
        }
    } catch (error) {
        console.error('登录请求出错:', error);
        return { success: false, message: '网络错误，请稍后重试' };
    }
}

// 登出
function logout() {
    localStorage.removeItem('auth_token');
    window.location.href = 'login.html';
}

// 带身份验证的fetch请求
async function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
        throw new Error('未登录，无法发送请求');
    }
    
    // 合并选项
    const authOptions = {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        }
    };
    
    try {
        const response = await fetch(url, authOptions);
        
        // 如果返回401未授权，可能是令牌过期
        if (response.status === 401) {
            localStorage.removeItem('auth_token');
            window.location.href = 'login.html?error=session_expired';
            throw new Error('会话已过期，请重新登录');
        }
        
        return response;
    } catch (error) {
        console.error('请求出错:', error);
        throw error;
    }
}

// 在页面加载时检查身份验证状态
document.addEventListener('DOMContentLoaded', function() {
    // 如果在登录页面，不需要检查身份验证
    if (window.location.href.includes('login.html')) {
        return;
    }
    
    // 检查身份验证状态
    checkAuth();
});

// 导出函数，使其可以在其他脚本中使用
window.checkAuth = checkAuth;
window.login = login;
window.logout = logout;
window.fetchWithAuth = fetchWithAuth;
