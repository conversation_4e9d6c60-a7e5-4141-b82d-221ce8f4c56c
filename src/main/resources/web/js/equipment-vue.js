/**
 * 死亡僵尸生存 - 初始装备设置 Vue应用
 */
const { createApp, ref, computed, onMounted } = Vue;

// 检查身份验证状态
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        // 没有令牌，重定向到登录页面
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 添加身份验证头到fetch请求
function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        // 没有令牌，重定向到登录页面
        window.location.href = 'login.html';
        return Promise.reject(new Error('未授权'));
    }

    // 确保headers存在
    if (!options.headers) {
        options.headers = {};
    }

    // 添加Authorization头
    options.headers['Authorization'] = `Bearer ${token}`;

    return fetch(url, options)
        .then(response => {
            if (response.status === 401) {
                // 令牌无效或过期，重定向到登录页面
                localStorage.removeItem('auth_token');
                window.location.href = 'login.html';
                throw new Error('未授权');
            }
            return response;
        });
}

createApp({
    setup() {
        // 检查身份验证状态
        if (!checkAuth()) {
            return;
        }

        const loading = ref(true);
        const loadingMessage = ref('正在加载...');
        const games = ref([]);
        const selectedGame = ref('');
        const equipment = ref({});
        const armorEquipment = ref({
            upper: null,
            lower: null
        });
        const selectedSlot = ref(null);
        const selectedArmorType = ref(null);
        const searchQuery = ref('');
        const filterType = ref('');
        const filterIdRange = ref('');
        const dragSourceSlot = ref(null);
        const dragItemId = ref(null);

        // 折叠功能相关状态
        const sectionExpanded = ref({
            weapons: true,
            armor: true,
            items: true
        });
        const allSectionsExpanded = ref(true);

        // Shoot插件物品名称映射
        const shootItemNames = ref({
            wp: {
                id1: '手枪',
                id2: '制式步枪',
                id3: '霰弹枪',
                id4: '重型机枪',
                id5: '火箭筒',
                id6: '电击枪',
                id7: '狙击步枪',
                id8: '冷冻枪',
                id9: '雷击枪',
                id10: '压强枪',
                id11: '突击步枪',
                id12: '冲锋枪',
                id13: '等离子枪',
                id14: '死神收割者',
                id15: '毁灭者',
                id16: '超级激光炮',
                id17: '黑洞吞噬者',
                id18: '音波步枪',
                id19: '能量脉冲枪',
                id20: '彩虹喷射器',
                id21: '传送枪',
                id22: '星辉主宰者',
                id23: '虚空星尘使者',
                id24: '循声炮'
            },
            ar: {
                id1: '皮革套装(上)',
                id2: '皮革套装(下)',
                id3: '锁链套装(上)',
                id4: '锁链套装(下)',
                id5: '铁套装(上)',
                id6: '铁套装(下)',
                id7: '钻石套装(上)',
                id8: '钻石套装(下)',
                id9: '附魔钻石套装1号',
                id10: '附魔钻石套装1号(下)',
                id11: '附魔钻石套装2号(上)',
                id12: '附魔钻石套装2号(下)'
            },
            it: {
                id1: '速度buff1药水',
                id2: '跳跃buff1药水',
                id3: '手枪弹药',
                id4: '步枪弹药',
                id5: '霰弹枪弹药',
                id6: '机枪弹药',
                id7: '火箭筒弹药',
                id8: '电击枪弹药',
                id9: '狙击步枪弹药',
                id10: '冰冻枪弹药',
                id11: '雷击枪弹药',
                id12: '压强枪弹药',
                id13: '金苹果',
                id14: '武器凭证',
                id15: '压强枪弹药',
                id16: '等离子枪弹药',
                id17: '死神收割者弹药',
                id18: '毁灭者弹药',
                id19: '超级激光炮弹药',
                id20: '黑洞吞噬者弹药',
                id21: '音轨步枪弹药',
                id22: '能量脉冲枪弹药',
                id23: '彩虹喷射器弹药',
                id24: '传送枪弹药',
                id25: '星辉主宰者弹药',
                id26: '虚空星尘弹药',
                id27: '循声炮弹药',
                id28: '熟牛排',
                id29: '治疗药水(1级)'
            }
        });

        // 物品列表 - 包含Minecraft原版物品和Shoot插件物品
        const items = ref([
            // 武器类
            { id: 'WOODEN_SWORD', name: '木剑' },
            { id: 'STONE_SWORD', name: '石剑' },
            { id: 'IRON_SWORD', name: '铁剑' },
            { id: 'GOLDEN_SWORD', name: '金剑' },
            { id: 'DIAMOND_SWORD', name: '钻石剑' },
            { id: 'NETHERITE_SWORD', name: '下界合金剑' },

            // 斧类
            { id: 'WOODEN_AXE', name: '木斧' },
            { id: 'STONE_AXE', name: '石斧' },
            { id: 'IRON_AXE', name: '铁斧' },
            { id: 'GOLDEN_AXE', name: '金斧' },
            { id: 'DIAMOND_AXE', name: '钻石斧' },
            { id: 'NETHERITE_AXE', name: '下界合金斧' },

            // 工具类
            { id: 'IRON_PICKAXE', name: '铁镐' },
            { id: 'IRON_SHOVEL', name: '铁锹' },
            { id: 'SHEARS', name: '剪刀' },

            // 远程武器
            { id: 'BOW', name: '弓' },
            { id: 'CROSSBOW', name: '弩' },
            { id: 'TRIDENT', name: '三叉戟' },

            // 防具类
            { id: 'SHIELD', name: '盾牌' },
            { id: 'LEATHER_HELMET', name: '皮革头盔' },
            { id: 'LEATHER_CHESTPLATE', name: '皮革胸甲' },
            { id: 'LEATHER_LEGGINGS', name: '皮革护腿' },
            { id: 'LEATHER_BOOTS', name: '皮革靴子' },
            { id: 'CHAINMAIL_HELMET', name: '锁链头盔' },
            { id: 'CHAINMAIL_CHESTPLATE', name: '锁链胸甲' },
            { id: 'CHAINMAIL_LEGGINGS', name: '锁链护腿' },
            { id: 'CHAINMAIL_BOOTS', name: '锁链靴子' },
            { id: 'IRON_HELMET', name: '铁头盔' },
            { id: 'IRON_CHESTPLATE', name: '铁胸甲' },
            { id: 'IRON_LEGGINGS', name: '铁护腿' },
            { id: 'IRON_BOOTS', name: '铁靴子' },
            { id: 'GOLDEN_HELMET', name: '金头盔' },
            { id: 'GOLDEN_CHESTPLATE', name: '金胸甲' },
            { id: 'GOLDEN_LEGGINGS', name: '金护腿' },
            { id: 'GOLDEN_BOOTS', name: '金靴子' },
            { id: 'DIAMOND_HELMET', name: '钻石头盔' },
            { id: 'DIAMOND_CHESTPLATE', name: '钻石胸甲' },
            { id: 'DIAMOND_LEGGINGS', name: '钻石护腿' },
            { id: 'DIAMOND_BOOTS', name: '钻石靴子' },
            { id: 'NETHERITE_HELMET', name: '下界合金头盔' },
            { id: 'NETHERITE_CHESTPLATE', name: '下界合金胸甲' },
            { id: 'NETHERITE_LEGGINGS', name: '下界合金护腿' },
            { id: 'NETHERITE_BOOTS', name: '下界合金靴子' },
            { id: 'TURTLE_HELMET', name: '海龟壳' },
            { id: 'ELYTRA', name: '鞘翅' },

            // 食物类
            { id: 'APPLE', name: '苹果' },
            { id: 'GOLDEN_APPLE', name: '金苹果' },
            { id: 'ENCHANTED_GOLDEN_APPLE', name: '附魔金苹果' },
            { id: 'BREAD', name: '面包' },
            { id: 'COOKED_BEEF', name: '熟牛肉' },
            { id: 'COOKED_CHICKEN', name: '熟鸡肉' },
            { id: 'COOKED_PORKCHOP', name: '熟猪排' },
            { id: 'COOKED_MUTTON', name: '熟羊肉' },
            { id: 'COOKED_RABBIT', name: '熟兔肉' },
            { id: 'COOKED_COD', name: '熟鳕鱼' },
            { id: 'COOKED_SALMON', name: '熟鲑鱼' },
            { id: 'CAKE', name: '蛋糕' },
            { id: 'COOKIE', name: '曲奇' },
            { id: 'MELON_SLICE', name: '西瓜片' },
            { id: 'DRIED_KELP', name: '干海带' },
            { id: 'CARROT', name: '胡萝卜' },
            { id: 'GOLDEN_CARROT', name: '金胡萝卜' },
            { id: 'POTATO', name: '马铃薯' },
            { id: 'BAKED_POTATO', name: '烤马铃薯' },
            { id: 'PUMPKIN_PIE', name: '南瓜派' },

            // 药水类
            { id: 'POTION', name: '药水' },
            { id: 'SPLASH_POTION', name: '喷溅药水' },
            { id: 'LINGERING_POTION', name: '滞留药水' },

            // 箭矢类
            { id: 'ARROW', name: '箭' },
            { id: 'SPECTRAL_ARROW', name: '光灵箭' },
            { id: 'TIPPED_ARROW', name: '药箭' },

            // 材料类
            { id: 'GUNPOWDER', name: '火药' },
            { id: 'REDSTONE', name: '红石' },
            { id: 'GLOWSTONE_DUST', name: '荧石粉' },
            { id: 'BLAZE_POWDER', name: '烈焰粉' },
            { id: 'MAGMA_CREAM', name: '岩浆膏' },
            { id: 'FERMENTED_SPIDER_EYE', name: '发酵蜘蛛眼' },
            { id: 'GLISTERING_MELON_SLICE', name: '金西瓜片' },
            { id: 'RABBIT_FOOT', name: '兔子脚' },
            { id: 'DRAGON_BREATH', name: '龙息' },
            { id: 'PHANTOM_MEMBRANE', name: '幻翼膜' },

            // 特殊物品
            { id: 'TOTEM_OF_UNDYING', name: '不死图腾' },
            { id: 'EXPERIENCE_BOTTLE', name: '附魔之瓶' },
            { id: 'ENDER_PEARL', name: '末影珍珠' },
            { id: 'ENDER_EYE', name: '末影之眼' },
            { id: 'FIREWORK_ROCKET', name: '烟花火箭' },
            { id: 'FIRE_CHARGE', name: '火焰弹' },
            { id: 'FLINT_AND_STEEL', name: '打火石' },
            { id: 'COMPASS', name: '指南针' },
            { id: 'CLOCK', name: '时钟' },
            { id: 'SPYGLASS', name: '望远镜' },
            { id: 'RECOVERY_COMPASS', name: '恢复指南针' },
            { id: 'BUNDLE', name: '束口袋' },
            { id: 'LEAD', name: '拴绳' },
            { id: 'NAME_TAG', name: '命名牌' },
            { id: 'FISHING_ROD', name: '钓鱼竿' },
            { id: 'CARROT_ON_A_STICK', name: '胡萝卜钓竿' },
            { id: 'WARPED_FUNGUS_ON_A_STICK', name: '诡异菌钓竿' },

            // 染料类
            { id: 'WHITE_DYE', name: '白色染料' },
            { id: 'ORANGE_DYE', name: '橙色染料' },
            { id: 'MAGENTA_DYE', name: '品红色染料' },
            { id: 'LIGHT_BLUE_DYE', name: '淡蓝色染料' },
            { id: 'YELLOW_DYE', name: '黄色染料' },
            { id: 'LIME_DYE', name: '黄绿色染料' },
            { id: 'PINK_DYE', name: '粉红色染料' },
            { id: 'GRAY_DYE', name: '灰色染料' },
            { id: 'LIGHT_GRAY_DYE', name: '淡灰色染料' },
            { id: 'CYAN_DYE', name: '青色染料' },
            { id: 'PURPLE_DYE', name: '紫色染料' },
            { id: 'BLUE_DYE', name: '蓝色染料' },
            { id: 'BROWN_DYE', name: '棕色染料' },
            { id: 'GREEN_DYE', name: '绿色染料' },
            { id: 'RED_DYE', name: '红色染料' },
            { id: 'BLACK_DYE', name: '黑色染料' },

            // Shoot插件枪支
            { id: 'SHOOT_PISTOL', name: '手枪' },
            { id: 'SHOOT_RIFLE', name: '步枪' },
            { id: 'SHOOT_SHOTGUN', name: '霰弹枪' },
            { id: 'SHOOT_SMG', name: '冲锋枪' },
            { id: 'SHOOT_SNIPER', name: '狙击枪' },
            { id: 'SHOOT_MACHINE_GUN', name: '机关枪' },
            { id: 'SHOOT_ROCKET_LAUNCHER', name: '火箭筒' },
            { id: 'SHOOT_GRENADE_LAUNCHER', name: '榴弹发射器' },

            // Shoot插件弹药
            { id: 'SHOOT_PISTOL_AMMO', name: '手枪弹药' },
            { id: 'SHOOT_RIFLE_AMMO', name: '步枪弹药' },
            { id: 'SHOOT_SHOTGUN_AMMO', name: '霰弹枪弹药' },
            { id: 'SHOOT_SMG_AMMO', name: '冲锋枪弹药' },
            { id: 'SHOOT_SNIPER_AMMO', name: '狙击枪弹药' },
            { id: 'SHOOT_MACHINE_GUN_AMMO', name: '机关枪弹药' },
            { id: 'SHOOT_ROCKET', name: '火箭弹' },
            { id: 'SHOOT_GRENADE', name: '榴弹' },

            // Shoot插件配件
            { id: 'SHOOT_SCOPE', name: '瞄准镜' },
            { id: 'SHOOT_SILENCER', name: '消音器' },
            { id: 'SHOOT_EXTENDED_MAG', name: '扩容弹匣' },
            { id: 'SHOOT_GRIP', name: '握把' },
            { id: 'SHOOT_LASER_SIGHT', name: '激光瞄准器' },

            // Shoot插件特殊物品
            { id: 'SHOOT_MEDKIT', name: '医疗包' },
            { id: 'SHOOT_BANDAGE', name: '绷带' },
            { id: 'SHOOT_ADRENALINE', name: '肾上腺素' },
            { id: 'SHOOT_GRENADE_FRAG', name: '破片手榴弹' },
            { id: 'SHOOT_GRENADE_SMOKE', name: '烟雾弹' },
            { id: 'SHOOT_GRENADE_FLASH', name: '闪光弹' },
            { id: 'SHOOT_GRENADE_MOLOTOV', name: '燃烧瓶' },
            { id: 'SHOOT_ARMOR_LIGHT', name: '轻型护甲' },
            { id: 'SHOOT_ARMOR_MEDIUM', name: '中型护甲' },
            { id: 'SHOOT_ARMOR_HEAVY', name: '重型护甲' },
            { id: 'SHOOT_HELMET_LEVEL1', name: '一级头盔' },
            { id: 'SHOOT_HELMET_LEVEL2', name: '二级头盔' },
            { id: 'SHOOT_HELMET_LEVEL3', name: '三级头盔' },

            // 数字ID映射 - 武器类 (id1-id24)
            { id: 'id1', name: '手枪 (id1)' },
            { id: 'id2', name: '制式步枪 (id2)' },
            { id: 'id3', name: '霰弹枪 (id3)' },
            { id: 'id4', name: '重型机枪 (id4)' },
            { id: 'id5', name: '火箭筒 (id5)' },
            { id: 'id6', name: '电击枪 (id6)' },
            { id: 'id7', name: '狙击步枪 (id7)' },
            { id: 'id8', name: '冷冻枪 (id8)' },
            { id: 'id9', name: '雷击枪 (id9)' },
            { id: 'id10', name: '压强枪 (id10)' },
            { id: 'id11', name: '突击步枪 (id11)' },
            { id: 'id12', name: '冲锋枪 (id12)' },
            { id: 'id13', name: '等离子枪 (id13)' },
            { id: 'id14', name: '死神收割者 (id14)' },
            { id: 'id15', name: '毁灭者 (id15)' },
            { id: 'id16', name: '超级激光炮 (id16)' },
            { id: 'id17', name: '黑洞吞噬者 (id17)' },
            { id: 'id18', name: '音波步枪 (id18)' },
            { id: 'id19', name: '能量脉冲枪 (id19)' },
            { id: 'id20', name: '彩虹喷射器 (id20)' },
            { id: 'id21', name: '传送枪 (id21)' },
            { id: 'id22', name: '星辉主宰者 (id22)' },
            { id: 'id23', name: '虚空星尘使者 (id23)' },
            { id: 'id24', name: '循声炮 (id24)' },

            // 数字ID映射 - 护甲类 (id25-id37)
            { id: 'id25', name: '皮革套装(上) (id25)' },
            { id: 'id26', name: '皮革套装(下) (id26)' },
            { id: 'id27', name: '锁链套装(上) (id27)' },
            { id: 'id28', name: '锁链套装(下) (id28)' },
            { id: 'id29', name: '铁套装(上) (id29)' },
            { id: 'id30', name: '铁套装(下) (id30)' },
            { id: 'id31', name: '钻石套装(上) (id31)' },
            { id: 'id32', name: '钻石套装(下) (id32)' },
            { id: 'id33', name: '附魔钻石套装1号 (id33)' },
            { id: 'id34', name: '附魔钻石套装1号(下) (id34)' },
            { id: 'id35', name: '附魔钻石套装2号(上) (id35)' },
            { id: 'id36', name: '附魔钻石套装2号(下) (id36)' },

            // 数字ID映射 - 物品类 (id38-id67)
            { id: 'id38', name: '速度buff1药水 (id38)' },
            { id: 'id39', name: '跳跃buff1药水 (id39)' },
            { id: 'id40', name: '手枪弹药 (id40)' },
            { id: 'id41', name: '步枪弹药 (id41)' },
            { id: 'id42', name: '霰弹枪弹药 (id42)' },
            { id: 'id43', name: '机枪弹药 (id43)' },
            { id: 'id44', name: '火箭筒弹药 (id44)' },
            { id: 'id45', name: '电击枪弹药 (id45)' },
            { id: 'id46', name: '狙击步枪弹药 (id46)' },
            { id: 'id47', name: '冰冻枪弹药 (id47)' },
            { id: 'id48', name: '雷击枪弹药 (id48)' },
            { id: 'id49', name: '压强枪弹药 (id49)' },
            { id: 'id50', name: '金苹果 (id50)' },
            { id: 'id51', name: '武器凭证 (id51)' },
            { id: 'id52', name: '压强枪弹药 (id52)' },
            { id: 'id53', name: '等离子枪弹药 (id53)' },
            { id: 'id54', name: '死神收割者弹药 (id54)' },
            { id: 'id55', name: '毁灭者弹药 (id55)' },
            { id: 'id56', name: '超级激光炮弹药 (id56)' },
            { id: 'id57', name: '黑洞吞噬者弹药 (id57)' },
            { id: 'id58', name: '音轨步枪弹药 (id58)' },
            { id: 'id59', name: '能量脉冲枪弹药 (id59)' },
            { id: 'id60', name: '彩虹喷射器弹药 (id60)' },
            { id: 'id61', name: '传送枪弹药 (id61)' },
            { id: 'id62', name: '星辉主宰者弹药 (id62)' },
            { id: 'id63', name: '虚空星尘弹药 (id63)' },
            { id: 'id64', name: '循声炮弹药 (id64)' },
            { id: 'id65', name: '熟牛排 (id65)' },
            { id: 'id66', name: '治疗药水(1级) (id66)' },

            // 数字ID映射 - 特殊槽位 (id67-id70)
            { id: 'id67', name: '空武器槽位 (id67)' },
            { id: 'id68', name: '空道具槽位 (id68)' },
            { id: 'id69', name: '锁定槽位 (id69)' },
            { id: 'id70', name: '铁剑 (id70)' }
        ]);

        // 物品分类
        const itemCategories = [
            { id: 'id_mapping', name: '数字ID映射', filter: (item) =>
                ((!isNaN(parseInt(item.id)) && parseInt(item.id) >= 1 && parseInt(item.id) <= 70) ||
                (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                parseInt(item.id.substring(2)) >= 1 && parseInt(item.id.substring(2)) <= 70))
            },
            { id: 'special_slots', name: '特殊槽位', filter: (item) =>
                ((!isNaN(parseInt(item.id)) && parseInt(item.id) >= 67 && parseInt(item.id) <= 70) ||
                (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                parseInt(item.id.substring(2)) >= 67 && parseInt(item.id.substring(2)) <= 70))
            },
            { id: 'weapons', name: '武器', filter: (item) =>
                item.id.includes('SWORD') ||
                item.id.includes('AXE') ||
                ['BOW', 'CROSSBOW', 'TRIDENT'].includes(item.id)
            },
            { id: 'shoot_weapons', name: 'Shoot插件武器', filter: (item) =>
                item.id.startsWith('SHOOT_') &&
                !item.id.includes('AMMO') &&
                !item.id.includes('SCOPE') &&
                !item.id.includes('SILENCER') &&
                !item.id.includes('MAG') &&
                !item.id.includes('GRIP') &&
                !item.id.includes('SIGHT') &&
                !item.id.includes('MEDKIT') &&
                !item.id.includes('BANDAGE') &&
                !item.id.includes('ADRENALINE') &&
                !item.id.includes('GRENADE') &&
                !item.id.includes('ARMOR') &&
                !item.id.includes('HELMET')
            },
            { id: 'ammo', name: '弹药', filter: (item) =>
                item.id.includes('ARROW') ||
                item.id.includes('AMMO') ||
                item.id === 'SHOOT_ROCKET' ||
                item.id === 'SHOOT_GRENADE'
            },
            { id: 'armor', name: '护甲', filter: (item) =>
                item.id.includes('HELMET') ||
                item.id.includes('CHESTPLATE') ||
                item.id.includes('LEGGINGS') ||
                item.id.includes('BOOTS') ||
                item.id === 'SHIELD' ||
                item.id === 'ELYTRA' ||
                item.id.includes('ARMOR')
            },
            { id: 'food', name: '食物', filter: (item) =>
                ['APPLE', 'GOLDEN_APPLE', 'ENCHANTED_GOLDEN_APPLE', 'BREAD', 'COOKED_BEEF',
                'COOKED_CHICKEN', 'COOKED_PORKCHOP', 'COOKED_MUTTON', 'COOKED_RABBIT',
                'COOKED_COD', 'COOKED_SALMON', 'CAKE', 'COOKIE', 'MELON_SLICE',
                'DRIED_KELP', 'CARROT', 'GOLDEN_CARROT', 'POTATO', 'BAKED_POTATO',
                'PUMPKIN_PIE'].includes(item.id)
            },
            { id: 'potions', name: '药水', filter: (item) =>
                item.id.includes('POTION') ||
                item.id === 'SHOOT_MEDKIT' ||
                item.id === 'SHOOT_BANDAGE' ||
                item.id === 'SHOOT_ADRENALINE'
            },
            { id: 'grenades', name: '手榴弹', filter: (item) =>
                item.id.includes('GRENADE')
            },
            { id: 'attachments', name: '配件', filter: (item) =>
                item.id.includes('SCOPE') ||
                item.id.includes('SILENCER') ||
                item.id.includes('MAG') ||
                item.id.includes('GRIP') ||
                item.id.includes('SIGHT')
            },
            { id: 'materials', name: '材料', filter: (item) =>
                ['GUNPOWDER', 'REDSTONE', 'GLOWSTONE_DUST', 'BLAZE_POWDER',
                'MAGMA_CREAM', 'FERMENTED_SPIDER_EYE', 'GLISTERING_MELON_SLICE',
                'RABBIT_FOOT', 'DRAGON_BREATH', 'PHANTOM_MEMBRANE'].includes(item.id)
            },
            { id: 'dyes', name: '染料', filter: (item) =>
                item.id.includes('DYE')
            },
            { id: 'other', name: '其他', filter: (item) =>
                !['weapons', 'shoot_weapons', 'ammo', 'armor', 'food', 'potions',
                'grenades', 'attachments', 'materials', 'dyes'].some(catId =>
                    itemCategories.find(cat => cat.id === catId).filter(item)
                )
            }
        ];

        // 过滤物品列表
        const filteredItems = computed(() => {
            // 应用基本过滤条件
            let filteredList = items.value;

            // 应用搜索查询
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                filteredList = filteredList.filter(item =>
                    item.name.toLowerCase().includes(query) ||
                    item.id.toLowerCase().includes(query)
                );
            }

            // 应用物品类型过滤
            if (filterType.value) {
                switch (filterType.value) {
                    case 'weapon':
                        filteredList = filteredList.filter(item =>
                            item.id.includes('SWORD') ||
                            item.id.includes('AXE') ||
                            item.id.startsWith('SHOOT_') && !item.id.includes('AMMO') ||
                            (!isNaN(parseInt(item.id)) && parseInt(item.id) >= 1 && parseInt(item.id) <= 24) ||
                            (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                            parseInt(item.id.substring(2)) >= 1 && parseInt(item.id.substring(2)) <= 24)
                        );
                        break;
                    case 'armor':
                        // 优先显示数字ID映射的护甲，如果没有选择ID范围过滤
                        if (!filterIdRange.value) {
                            filteredList = filteredList.filter(item =>
                                (!isNaN(parseInt(item.id)) && parseInt(item.id) >= 25 && parseInt(item.id) <= 37) ||
                                (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                                parseInt(item.id.substring(2)) >= 25 && parseInt(item.id.substring(2)) <= 37) ||
                                (
                                    (item.id.includes('HELMET') ||
                                    item.id.includes('CHESTPLATE') ||
                                    item.id.includes('LEGGINGS') ||
                                    item.id.includes('BOOTS') ||
                                    item.id.includes('ARMOR') ||
                                    item.id === 'SHIELD' ||
                                    item.id === 'ELYTRA') &&
                                    // 排除数字ID的物品，避免重复
                                    isNaN(parseInt(item.id))
                                )
                            );
                        } else {
                            // 如果选择了ID范围过滤，则按照ID范围过滤的逻辑处理
                            filteredList = filteredList.filter(item =>
                                (!isNaN(parseInt(item.id)) && parseInt(item.id) >= 25 && parseInt(item.id) <= 37) ||
                                (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                                parseInt(item.id.substring(2)) >= 25 && parseInt(item.id.substring(2)) <= 37) ||
                                (item.id.includes('HELMET') ||
                                item.id.includes('CHESTPLATE') ||
                                item.id.includes('LEGGINGS') ||
                                item.id.includes('BOOTS') ||
                                item.id.includes('ARMOR') ||
                                item.id === 'SHIELD' ||
                                item.id === 'ELYTRA')
                            );
                        }
                        break;
                    case 'item':
                        filteredList = filteredList.filter(item =>
                            item.id.includes('APPLE') ||
                            item.id.includes('POTION') ||
                            item.id.includes('BREAD') ||
                            item.id.includes('COOKED') ||
                            (!isNaN(parseInt(item.id)) && parseInt(item.id) >= 38 && parseInt(item.id) <= 66) ||
                            (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                            parseInt(item.id.substring(2)) >= 38 && parseInt(item.id.substring(2)) <= 66)
                        );
                        break;
                    case 'special':
                        filteredList = filteredList.filter(item =>
                            (!isNaN(parseInt(item.id)) && parseInt(item.id) >= 67 && parseInt(item.id) <= 70) ||
                            (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2))) &&
                            parseInt(item.id.substring(2)) >= 67 && parseInt(item.id.substring(2)) <= 70)
                        );
                        break;
                    case 'ammo':
                        filteredList = filteredList.filter(item =>
                            item.id.includes('ARROW') ||
                            item.id.includes('AMMO') ||
                            item.id.includes('ROCKET') ||
                            item.id.includes('GRENADE')
                        );
                        break;
                }
            }

            // 应用ID范围过滤
            if (filterIdRange.value) {
                const [min, max] = filterIdRange.value.split('-').map(Number);
                filteredList = filteredList.filter(item => {
                    // 处理数字ID
                    if (!isNaN(parseInt(item.id))) {
                        const numId = parseInt(item.id);
                        return numId >= min && numId <= max;
                    }

                    // 处理带有数字的ID (如 "id5")
                    const match = item.id.match(/id(\d+)/);
                    if (match) {
                        const numId = parseInt(match[1]);
                        return numId >= min && numId <= max;
                    }

                    // 根据ID范围过滤物品类型
                    if (min === 1 && max === 24) {
                        // 武器类
                        return item.id.includes('SWORD') ||
                               item.id.includes('AXE') ||
                               (item.id.startsWith('SHOOT_') && !item.id.includes('AMMO'));
                    } else if (min === 25 && max === 37) {
                        // 护甲类 - 只显示数字ID映射的护甲，避免重复
                        if (!isNaN(parseInt(item.id))) {
                            const numId = parseInt(item.id);
                            return numId >= 25 && numId <= 37;
                        }
                        // 处理id格式的ID
                        if (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2)))) {
                            const numId = parseInt(item.id.substring(2));
                            return numId >= 25 && numId <= 37;
                        }
                        // 对于非数字ID的物品，不在此范围显示
                        return false;
                    } else if (min === 38 && max === 66) {
                        // 物品类
                        return item.id.includes('APPLE') ||
                               item.id.includes('POTION') ||
                               item.id.includes('BREAD') ||
                               item.id.includes('COOKED');
                    } else if (min === 67 && max === 70) {
                        // 特殊槽位
                        if (!isNaN(parseInt(item.id))) {
                            const numId = parseInt(item.id);
                            return numId >= 67 && numId <= 70;
                        }
                        // 处理id格式的ID
                        if (item.id.startsWith('id') && !isNaN(parseInt(item.id.substring(2)))) {
                            const numId = parseInt(item.id.substring(2));
                            return numId >= 67 && numId <= 70;
                        }
                        return false;
                    }

                    return false;
                });
            }

            // 如果没有搜索查询，按分类组织物品
            if (!searchQuery.value) {
                const result = [];
                itemCategories.forEach(category => {
                    const categoryItems = filteredList.filter(category.filter);
                    if (categoryItems.length > 0) {
                        // 添加分类标题
                        result.push({ id: `category_${category.id}`, name: category.name, isCategory: true });
                        // 添加分类下的物品
                        result.push(...categoryItems);
                    }
                });
                return result;
            }

            return filteredList;
        });

        // 注意：以下计算属性暂未使用，但保留以备将来使用
        /*
        // 计算属性：是否包含装备
        const hasArmor = computed(() => {
            const armorItems = [
                'LEATHER_HELMET', 'LEATHER_CHESTPLATE', 'LEATHER_LEGGINGS', 'LEATHER_BOOTS',
                'CHAINMAIL_HELMET', 'CHAINMAIL_CHESTPLATE', 'CHAINMAIL_LEGGINGS', 'CHAINMAIL_BOOTS',
                'IRON_HELMET', 'IRON_CHESTPLATE', 'IRON_LEGGINGS', 'IRON_BOOTS',
                'GOLDEN_HELMET', 'GOLDEN_CHESTPLATE', 'GOLDEN_LEGGINGS', 'GOLDEN_BOOTS',
                'DIAMOND_HELMET', 'DIAMOND_CHESTPLATE', 'DIAMOND_LEGGINGS', 'DIAMOND_BOOTS',
                'NETHERITE_HELMET', 'NETHERITE_CHESTPLATE', 'NETHERITE_LEGGINGS', 'NETHERITE_BOOTS',
                'TURTLE_HELMET', 'ELYTRA'
            ];

            return Object.values(equipment.value).some(itemId =>
                armorItems.includes(itemId)
            );
        });

        // 计算属性：是否包含武器
        const hasWeapon = computed(() => {
            const weaponItems = [
                'WOODEN_SWORD', 'STONE_SWORD', 'IRON_SWORD', 'GOLDEN_SWORD', 'DIAMOND_SWORD', 'NETHERITE_SWORD',
                'WOODEN_AXE', 'STONE_AXE', 'IRON_AXE', 'GOLDEN_AXE', 'DIAMOND_AXE', 'NETHERITE_AXE',
                'BOW', 'CROSSBOW', 'TRIDENT'
            ];

            return Object.values(equipment.value).some(itemId =>
                weaponItems.includes(itemId)
            );
        });

        // 计算属性：是否包含食物
        const hasFood = computed(() => {
            const foodItems = [
                'APPLE', 'GOLDEN_APPLE', 'ENCHANTED_GOLDEN_APPLE', 'BREAD', 'COOKED_BEEF',
                'COOKED_CHICKEN', 'COOKED_PORKCHOP', 'COOKED_MUTTON', 'COOKED_RABBIT',
                'COOKED_COD', 'COOKED_SALMON', 'CAKE', 'COOKIE', 'MELON_SLICE',
                'DRIED_KELP', 'CARROT', 'GOLDEN_CARROT', 'POTATO', 'BAKED_POTATO',
                'PUMPKIN_PIE', 'RABBIT_STEW', 'BEETROOT', 'BEETROOT_SOUP', 'SUSPICIOUS_STEW',
                'SWEET_BERRIES', 'GLOW_BERRIES', 'HONEY_BOTTLE'
            ];

            return Object.values(equipment.value).some(itemId =>
                foodItems.includes(itemId)
            );
        });
        */

        // 加载游戏列表
        const loadGames = async () => {
            loading.value = true;
            loadingMessage.value = '正在加载游戏列表...';
            try {
                const response = await fetchWithAuth('/api/games');
                const data = await response.json();
                if (data && data.games) {
                    games.value = Array.isArray(data.games)
                        ? data.games.map(game => typeof game === 'string' ? game : game.name)
                        : Object.keys(data.games);
                }
                return true; // 返回成功标志
            } catch (error) {
                console.error('加载游戏列表失败:', error);
                alert('加载游戏列表失败，请检查网络连接或刷新页面重试。');
                return false; // 返回失败标志
            } finally {
                loading.value = false;
            }
        };

        // 加载游戏装备
        const loadGameEquipment = async () => {
            if (!selectedGame.value) return;

            loading.value = true;
            loadingMessage.value = `正在加载 ${selectedGame.value} 的装备设置...`;

            try {
                const response = await fetchWithAuth(`/api/equipment?name=${encodeURIComponent(selectedGame.value)}`);
                const data = await response.json();

                if (data && data.success) {
                    // 直接使用服务器返回的装备数据，不进行槽位转换
                    // 因为服务器已经返回了正确的槽位格式（1-36）
                    const newEquipment = {};
                    if (data.initialEquipment) {
                        Object.keys(data.initialEquipment).forEach(key => {
                            // 直接使用服务器返回的键值对
                            const slot = parseInt(key);
                            newEquipment[slot] = data.initialEquipment[key];
                            console.log(`加载槽位 ${slot} 的物品: ${data.initialEquipment[key]}`);
                        });
                    }
                    equipment.value = newEquipment;

                    // 加载护甲设置
                    if (data.armorEquipment) {
                        // 检查是否是旧格式，如果是则转换为新格式
                        if (data.armorEquipment.helmet !== undefined ||
                            data.armorEquipment.chestplate !== undefined ||
                            data.armorEquipment.leggings !== undefined ||
                            data.armorEquipment.boots !== undefined) {
                            // 旧格式转换为新格式
                            // 上套装使用胸甲或头盔的值
                            const upper = data.armorEquipment.chestplate || data.armorEquipment.helmet;
                            // 下套装使用护腿或靴子的值
                            const lower = data.armorEquipment.leggings || data.armorEquipment.boots;

                            armorEquipment.value = {
                                upper: upper,
                                lower: lower
                            };
                        } else {
                            // 已经是新格式
                            armorEquipment.value = data.armorEquipment;
                        }
                    } else {
                        // 初始化空护甲设置
                        armorEquipment.value = {
                            upper: null,
                            lower: null
                        };
                    }

                    // 为未设置的武器槽位设置默认的淡灰色染料
                    // 槽位1默认为武器，槽位2-9默认为空白但需要设置淡灰色染料
                    for (let i = 2; i <= 9; i++) {
                        if (!equipment.value[i]) {
                            equipment.value[i] = 'LIGHT_GRAY_DYE';
                        }
                    }
                } else {
                    console.warn('加载装备设置返回错误:', data);
                    equipment.value = {};

                    // 初始化空护甲设置
                    armorEquipment.value = {
                        upper: null,
                        lower: null
                    };

                    // 初始化默认装备
                    for (let i = 2; i <= 9; i++) {
                        equipment.value[i] = 'LIGHT_GRAY_DYE';
                    }
                }
            } catch (error) {
                console.error('加载装备设置失败:', error);
                alert('加载装备设置失败，请检查网络连接或刷新页面重试。');
                equipment.value = {};

                // 初始化空护甲设置
                armorEquipment.value = {
                    upper: null,
                    lower: null
                };

                // 初始化默认装备
                for (let i = 2; i <= 9; i++) {
                    equipment.value[i] = 'LIGHT_GRAY_DYE';
                }
            } finally {
                loading.value = false;
            }
        };

        // 保存装备设置
        const saveEquipment = async () => {
            if (!selectedGame.value) {
                alert('请先选择一个游戏');
                return;
            }

            loading.value = true;
            loadingMessage.value = '正在保存装备设置...';

            try {
                // 过滤装备数据，确保只包含有效的物品ID
                const filteredEquipment = {};

                // 遍历装备数据
                Object.keys(equipment.value).forEach(slotKey => {
                    const slot = parseInt(slotKey);
                    const itemId = equipment.value[slotKey];

                    // 确保槽位是有效的数字且在1-36范围内
                    if (!isNaN(slot) && slot >= 1 && slot <= 36) {
                        // 直接使用槽位编号，不进行转换
                        filteredEquipment[slot] = itemId;
                        console.log(`保存槽位 ${slot} 的物品: ${itemId}`);
                    } else {
                        console.warn(`跳过无效的槽位: ${slotKey}`);
                    }
                });

                console.log('发送装备数据:', {
                    name: selectedGame.value,
                    initialEquipment: filteredEquipment,
                    armorEquipment: armorEquipment.value
                });

                const response = await fetchWithAuth('/api/save-game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: selectedGame.value,
                        initialEquipment: filteredEquipment,
                        armorEquipment: armorEquipment.value
                    })
                });

                const data = await response.json();
                console.log('服务器响应:', data);

                if (data && data.success) {
                    alert('装备设置保存成功！');
                } else {
                    alert(`保存失败: ${data.message || '未知错误'}`);
                }
            } catch (error) {
                console.error('保存装备设置失败:', error);
                alert('保存装备设置失败，请检查网络连接或刷新页面重试。');
            } finally {
                loading.value = false;
            }
        };

        // 滚动到物品选择器
        const scrollToItemSelector = () => {
            // 使用setTimeout确保DOM已更新
            setTimeout(() => {
                const itemSelectorCard = document.querySelector('.item-selector-card');
                if (itemSelectorCard) {
                    itemSelectorCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        };

        // 切换单个部分的折叠状态
        const toggleSection = (section) => {
            sectionExpanded.value[section] = !sectionExpanded.value[section];
            // 更新全部展开/折叠状态
            allSectionsExpanded.value = Object.values(sectionExpanded.value).every(value => value);
        };

        // 切换所有部分的折叠状态
        const toggleAllSections = () => {
            allSectionsExpanded.value = !allSectionsExpanded.value;
            // 更新所有部分的状态
            Object.keys(sectionExpanded.value).forEach(key => {
                sectionExpanded.value[key] = allSectionsExpanded.value;
            });
        };

        // 退出登录
        const logout = () => {
            // 清除令牌
            localStorage.removeItem('auth_token');
            // 重定向到登录页面
            window.location.href = 'login.html';
        };

        // 从参考部分拖动物品
        const dragReferenceItem = (event, itemId) => {
            // 确保itemId是正确的格式（如id1, id2等）
            let formattedItemId = itemId;

            // 如果是纯数字，转换为idx格式
            if (!isNaN(parseInt(itemId)) && !itemId.startsWith('id')) {
                formattedItemId = 'id' + itemId;
            }

            // 设置拖动数据 - 使用与其他拖动函数相同的JSON格式
            event.dataTransfer.setData('text/plain', JSON.stringify({
                sourceType: 'reference',
                itemId: formattedItemId
            }));
            event.dataTransfer.effectAllowed = 'copy';

            // 添加拖动样式
            const sequenceItem = event.target.closest('.sequence-item');
            if (sequenceItem) {
                sequenceItem.classList.add('dragging');
            }

            // 设置拖动图像
            const img = new Image();
            img.src = getItemImageUrl(formattedItemId);
            event.dataTransfer.setDragImage(img, 20, 20);

            // 记录拖动的物品ID
            dragItemId.value = formattedItemId;
            dragSourceSlot.value = null; // 不是从槽位拖动

            console.log('开始拖动参考物品:', formattedItemId);
        };

        // 选择槽位
        const selectSlot = (slotIndex) => {
            selectedSlot.value = slotIndex;
            selectedArmorType.value = null; // 取消护甲选择
            // 重置搜索和过滤条件
            searchQuery.value = '';
            filterType.value = '';
            filterIdRange.value = '';

            // 滚动到物品选择器
            scrollToItemSelector();
        };

        // 选择护甲槽位
        const selectArmorSlot = (armorType) => {
            selectedArmorType.value = armorType;
            selectedSlot.value = null; // 取消物品栏选择
            // 设置过滤条件为护甲类型
            searchQuery.value = '';
            filterType.value = 'armor';
            filterIdRange.value = '25-37';

            // 滚动到物品选择器
            scrollToItemSelector();
        };

        // 选择物品
        const selectItem = (itemId) => {
            if (selectedSlot.value !== null) {
                equipment.value = {
                    ...equipment.value,
                    [selectedSlot.value]: itemId
                };
            } else if (selectedArmorType.value !== null) {
                armorEquipment.value = {
                    ...armorEquipment.value,
                    [selectedArmorType.value]: itemId
                };
            }
        };

        // 清除槽位
        const clearSlot = () => {
            if (selectedSlot.value !== null) {
                const newEquipment = { ...equipment.value };
                delete newEquipment[selectedSlot.value];
                equipment.value = newEquipment;
            } else if (selectedArmorType.value !== null) {
                const newArmorEquipment = { ...armorEquipment.value };
                newArmorEquipment[selectedArmorType.value] = null;
                armorEquipment.value = newArmorEquipment;
            }
        };

        // 清除护甲槽位
        const clearArmorSlot = (armorType) => {
            const newArmorEquipment = { ...armorEquipment.value };
            newArmorEquipment[armorType] = null;
            armorEquipment.value = newArmorEquipment;
        };

        // 拖放相关函数

        // 开始拖动物品
        const dragItem = (event, slotIndex, itemId) => {
            // 设置拖动数据
            event.dataTransfer.setData('text/plain', JSON.stringify({
                sourceType: 'slot',
                slotIndex,
                itemId
            }));

            // 记录拖动源
            dragSourceSlot.value = slotIndex;
            dragItemId.value = itemId;

            // 设置拖动效果
            event.dataTransfer.effectAllowed = 'move';

            // 添加拖动样式
            event.target.classList.add('dragging');
        };

        // 开始拖动新物品
        const dragNewItem = (event, itemId) => {
            // 设置拖动数据
            event.dataTransfer.setData('text/plain', JSON.stringify({
                sourceType: 'new',
                itemId
            }));

            // 记录拖动源
            dragSourceSlot.value = null;
            dragItemId.value = itemId;

            // 设置拖动效果
            event.dataTransfer.effectAllowed = 'copy';

            // 添加拖动样式
            event.target.classList.add('dragging');
        };

        // 结束拖动
        const dragEnd = () => {
            // 移除拖动样式
            document.querySelectorAll('.dragging').forEach(el => {
                el.classList.remove('dragging');
            });

            // 移除高亮样式
            document.querySelectorAll('.drag-over').forEach(el => {
                el.classList.remove('drag-over');
            });

            // 重置拖动源
            dragSourceSlot.value = null;
            dragItemId.value = null;
        };

        // 高亮目标槽位
        const highlightSlot = (slotIndex) => {
            const slot = document.querySelector(`.inventory-slot:nth-child(${slotIndex})`);
            if (slot) {
                slot.classList.add('drag-over');
            }
        };

        // 取消高亮目标槽位
        const unhighlightSlot = (slotIndex) => {
            const slot = document.querySelector(`.inventory-slot:nth-child(${slotIndex})`);
            if (slot) {
                slot.classList.remove('drag-over');
            }
        };

        // 放置物品到物品栏
        const dropItem = (event, targetSlot) => {
            try {
                // 获取拖动数据
                const dataText = event.dataTransfer.getData('text/plain');
                console.log('拖放数据:', dataText);

                // 尝试解析JSON数据
                try {
                    const data = JSON.parse(dataText);
                    console.log('解析的JSON数据:', data);

                    if (data.sourceType === 'reference') {
                        // 从参考部分拖动的物品
                        let itemId = data.itemId;

                        // 确保itemId是正确的格式（如id1, id2等）
                        if (!isNaN(parseInt(itemId)) && !itemId.startsWith('id')) {
                            itemId = 'id' + itemId;
                        }

                        console.log('从参考部分拖动物品:', itemId);

                        // 更新装备数据
                        equipment.value = {
                            ...equipment.value,
                            [targetSlot]: itemId
                        };
                    } else if (data.sourceType === 'slot') {
                        // 从一个槽位拖到另一个槽位（交换物品）
                        const sourceSlot = data.slotIndex;
                        const itemId = data.itemId;

                        if (sourceSlot === targetSlot) {
                            // 拖回原位，不做任何操作
                            return;
                        }

                        // 获取目标槽位的物品
                        const targetItemId = equipment.value[targetSlot];

                        // 更新装备数据
                        const newEquipment = { ...equipment.value };

                        if (targetItemId) {
                            // 如果目标槽位有物品，交换物品
                            newEquipment[sourceSlot] = targetItemId;
                        } else {
                            // 如果目标槽位没有物品，移除源槽位的物品
                            delete newEquipment[sourceSlot];
                        }

                        // 设置目标槽位的物品
                        newEquipment[targetSlot] = itemId;

                        // 更新装备数据
                        equipment.value = newEquipment;
                    } else if (data.sourceType === 'new') {
                        // 从物品列表拖到槽位
                        const itemId = data.itemId;

                        // 更新装备数据
                        equipment.value = {
                            ...equipment.value,
                            [targetSlot]: itemId
                        };
                    }
                } catch (jsonError) {
                    console.error('JSON解析失败:', jsonError, dataText);

                    // 检查是否是纯数字ID（兼容旧版本）
                    if (/^\d+$/.test(dataText)) {
                        // 从参考部分拖动的物品ID，转换为idx格式
                        const itemId = 'id' + dataText;
                        console.log('从参考部分拖动物品(旧格式转换为idx格式):', itemId);

                        // 更新装备数据
                        equipment.value = {
                            ...equipment.value,
                            [targetSlot]: itemId
                        };
                    }
                }
            } catch (error) {
                console.error('拖放操作失败:', error);
            }

            // 移除高亮样式
            unhighlightSlot(targetSlot);
        };

        // 放置物品到护甲槽位
        const dropArmorItem = (event, armorType) => {
            try {
                // 获取拖动数据
                const dataText = event.dataTransfer.getData('text/plain');
                console.log('拖放数据到护甲槽位:', dataText, armorType);

                // 尝试解析JSON数据
                try {
                    const data = JSON.parse(dataText);
                    console.log('解析的JSON数据:', data);

                    if (data.sourceType === 'reference') {
                        // 从参考部分拖动的物品
                        let itemId = data.itemId;

                        // 确保itemId是正确的格式（如id25, id26等）
                        if (!isNaN(parseInt(itemId)) && !itemId.startsWith('id')) {
                            itemId = 'id' + itemId;
                        }

                        console.log('从参考部分拖动物品到护甲槽位:', itemId);

                        // 检查是否是护甲类物品 (id25-id37)
                        // 从itemId中提取数字部分
                        let numId;
                        if (itemId.startsWith('id')) {
                            numId = parseInt(itemId.substring(2));
                        } else {
                            numId = parseInt(itemId);
                        }

                        if (numId >= 25 && numId <= 37) {
                            // 更新护甲数据
                            armorEquipment.value = {
                                ...armorEquipment.value,
                                [armorType]: itemId
                            };
                        } else {
                            console.warn('非护甲类物品不能放置到护甲槽位');
                        }
                    } else if (data.sourceType === 'new') {
                        // 从物品列表拖到槽位
                        const itemId = data.itemId;

                        // 检查是否是护甲类物品
                        if (itemId.includes('HELMET') ||
                            itemId.includes('CHESTPLATE') ||
                            itemId.includes('LEGGINGS') ||
                            itemId.includes('BOOTS') ||
                            (parseInt(itemId) >= 25 && parseInt(itemId) <= 37)) {

                            // 更新护甲数据
                            armorEquipment.value = {
                                ...armorEquipment.value,
                                [armorType]: itemId
                            };
                        } else {
                            console.warn('非护甲类物品不能放置到护甲槽位');
                        }
                    }
                } catch (jsonError) {
                    console.error('JSON解析失败:', jsonError, dataText);

                    // 检查是否是纯数字ID（兼容旧版本）
                    if (/^\d+$/.test(dataText)) {
                        // 从参考部分拖动的物品ID，转换为idx格式
                        const numId = parseInt(dataText);
                        const itemId = 'id' + numId;
                        console.log('从参考部分拖动物品(旧格式转换为idx格式)到护甲槽位:', itemId);

                        // 检查是否是护甲类物品 (id25-id37)
                        if (numId >= 25 && numId <= 37) {
                            // 更新护甲数据
                            armorEquipment.value = {
                                ...armorEquipment.value,
                                [armorType]: itemId
                            };
                        } else {
                            console.warn('非护甲类物品不能放置到护甲槽位');
                        }
                    }
                }
            } catch (error) {
                console.error('拖放操作到护甲槽位失败:', error);
            }
        };

        // 获取物品显示名称
        const getItemDisplayName = (itemId) => {
            if (!itemId) return '';

            // 查找物品
            const item = items.value.find(i => i.id === itemId);
            if (item) {
                return item.name;
            }

            // 处理数字ID
            if (!isNaN(parseInt(itemId))) {
                const numId = parseInt(itemId);
                if (numId >= 1 && numId <= 24) {
                    return getItemName('wp', numId);
                } else if (numId >= 25 && numId <= 37) {
                    return getItemName('ar', numId - 24);
                } else if (numId >= 38 && numId <= 67) {
                    return getItemName('it', numId - 37);
                }
            }

            // 处理Shoot插件物品ID格式
            if (itemId.startsWith('wp_') || itemId.startsWith('ar_') || itemId.startsWith('it_')) {
                const parts = itemId.split('_');
                const type = parts[0];
                const id = parts[1];
                return getItemName(type, id);
            }

            return itemId;
        };

        // 获取物品图片URL
        const getItemImageUrl = (itemId) => {
            if (!itemId) return '';

            // 处理Shoot插件物品ID格式
            if (itemId.startsWith('wp_') || itemId.startsWith('ar_') || itemId.startsWith('it_')) {
                const parts = itemId.split('_');
                const type = parts[0];
                // 使用parts[1]而不是存储为变量

                // 根据类型返回不同的默认图标
                switch (type) {
                    case 'wp':
                        // 根据武器ID返回不同的图标
                        const wpId = parseInt(parts[1]);
                        if (wpId === 1) return 'https://minecraftitemids.com/item/64/flint_and_steel.png'; // 手枪
                        if (wpId === 2) return 'https://minecraftitemids.com/item/64/crossbow.png';
                        if (wpId === 3) return 'https://minecraftitemids.com/item/64/bow.png';
                        if (wpId === 4) return 'https://minecraftitemids.com/item/64/trident.png';
                        if (wpId === 5) return 'https://minecraftitemids.com/item/64/fire_charge.png';
                        if (wpId === 6) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                        if (wpId === 7) return 'https://minecraftitemids.com/item/64/spyglass.png';
                        if (wpId === 8) return 'https://minecraftitemids.com/item/64/blue_ice.png';
                        if (wpId === 9) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                        if (wpId === 10) return 'https://minecraftitemids.com/item/64/piston.png';
                        if (wpId === 11) return 'https://minecraftitemids.com/item/64/crossbow.png'; // 突击步枪
                        if (wpId === 12) return 'https://minecraftitemids.com/item/64/repeater.png';
                        return 'https://minecraftitemids.com/item/64/wooden_hoe.png';
                    case 'ar':
                        return 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                    case 'it':
                        // 根据物品ID返回不同的图标
                        const itId = parseInt(parts[1]);
                        if (itId >= 3 && itId <= 12) return 'https://minecraftitemids.com/item/64/gunpowder.png'; // 弹药
                        if (itId === 13) return 'https://minecraftitemids.com/item/64/golden_apple.png'; // 金苹果
                        if (itId === 28) return 'https://minecraftitemids.com/item/64/glistering_melon_slice.png'; // 医疗包
                        return 'https://minecraftitemids.com/item/64/potion.png';
                    default:
                        return 'https://minecraftitemids.com/item/64/barrier.png';
                }
            }

            // 处理数字ID映射
            if (itemId.match(/^id\d+$/)) {
                const idNum = parseInt(itemId.substring(2));
                if (idNum >= 1 && idNum <= 24) {
                    // 武器类 - 根据ID返回不同图标
                    if (idNum === 1) return 'https://minecraftitemids.com/item/64/flint_and_steel.png'; // 手枪
                    if (idNum === 2) return 'https://minecraftitemids.com/item/64/crossbow.png';
                    if (idNum === 3) return 'https://minecraftitemids.com/item/64/bow.png';
                    if (idNum === 4) return 'https://minecraftitemids.com/item/64/trident.png';
                    if (idNum === 5) return 'https://minecraftitemids.com/item/64/fire_charge.png';
                    if (idNum === 6) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                    if (idNum === 7) return 'https://minecraftitemids.com/item/64/spyglass.png';
                    if (idNum === 8) return 'https://minecraftitemids.com/item/64/blue_ice.png';
                    if (idNum === 9) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                    if (idNum === 10) return 'https://minecraftitemids.com/item/64/piston.png';
                    if (idNum === 11) return 'https://minecraftitemids.com/item/64/crossbow.png'; // 突击步枪
                    if (idNum === 12) return 'https://minecraftitemids.com/item/64/repeater.png';
                    if (idNum === 13) return 'https://minecraftitemids.com/item/64/end_rod.png';
                    if (idNum === 14) return 'https://minecraftitemids.com/item/64/netherite_sword.png';
                    if (idNum === 15) return 'https://minecraftitemids.com/item/64/tnt.png';
                    if (idNum === 16) return 'https://minecraftitemids.com/item/64/beacon.png';
                    if (idNum === 17) return 'https://minecraftitemids.com/item/64/end_crystal.png';
                    if (idNum === 18) return 'https://minecraftitemids.com/item/64/note_block.png';
                    if (idNum === 19) return 'https://minecraftitemids.com/item/64/glowstone.png';
                    if (idNum === 20) return 'https://minecraftitemids.com/item/64/prismarine_crystals.png';
                    if (idNum === 21) return 'https://minecraftitemids.com/item/64/ender_pearl.png';
                    if (idNum === 22) return 'https://minecraftitemids.com/item/64/diamond.png';
                    if (idNum === 23) return 'https://minecraftitemids.com/item/64/end_stone.png';
                    if (idNum === 24) return 'https://minecraftitemids.com/item/64/amethyst_shard.png';
                    return 'https://minecraftitemids.com/item/64/wooden_hoe.png';
                } else if (idNum >= 25 && idNum <= 37) {
                    // 护甲类 - 根据ID返回不同图标
                    if (idNum === 25 || idNum === 26) return 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                    if (idNum === 27 || idNum === 28) return 'https://minecraftitemids.com/item/64/chainmail_chestplate.png';
                    if (idNum === 29 || idNum === 30) return 'https://minecraftitemids.com/item/64/iron_chestplate.png';
                    if (idNum === 31 || idNum === 32) return 'https://minecraftitemids.com/item/64/diamond_chestplate.png';
                    if (idNum === 33 || idNum === 34) return 'https://minecraftitemids.com/item/64/diamond_chestplate.png';
                    if (idNum === 35 || idNum === 36) return 'https://minecraftitemids.com/item/64/netherite_chestplate.png';
                    return 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                } else if (idNum >= 38 && idNum <= 66) {
                    // 物品类 - 根据ID返回不同图标
                    if (idNum === 38 || idNum === 39) return 'https://minecraftitemids.com/item/64/potion.png'; // 药水
                    if (idNum >= 40 && idNum <= 49) return 'https://minecraftitemids.com/item/64/gunpowder.png'; // 基础弹药
                    if (idNum === 50) return 'https://minecraftitemids.com/item/64/golden_apple.png'; // 金苹果
                    if (idNum === 51) return 'https://minecraftitemids.com/item/64/paper.png'; // 武器凭证
                    if (idNum === 52) return 'https://minecraftitemids.com/item/64/gunpowder.png'; // 压强枪弹药
                    if (idNum === 53) return 'https://minecraftitemids.com/item/64/blaze_powder.png'; // 等离子枪弹药
                    if (idNum === 54) return 'https://minecraftitemids.com/item/64/dragon_breath.png'; // 死神收割者弹药
                    if (idNum === 55) return 'https://minecraftitemids.com/item/64/tnt.png'; // 毁灭者弹药
                    if (idNum === 56) return 'https://minecraftitemids.com/item/64/nether_star.png'; // 超级激光炮弹药
                    if (idNum === 57) return 'https://minecraftitemids.com/item/64/end_crystal.png'; // 黑洞吞噬者弹药
                    if (idNum === 58) return 'https://minecraftitemids.com/item/64/note_block.png'; // 音轨步枪弹药
                    if (idNum === 59) return 'https://minecraftitemids.com/item/64/glowstone_dust.png'; // 能量脉冲枪弹药
                    if (idNum === 60) return 'https://minecraftitemids.com/item/64/prismarine_crystals.png'; // 彩虹喷射器弹药
                    if (idNum === 61) return 'https://minecraftitemids.com/item/64/ender_pearl.png'; // 传送枪弹药
                    if (idNum === 62) return 'https://minecraftitemids.com/item/64/diamond.png'; // 星辉主宰者弹药
                    if (idNum === 63) return 'https://minecraftitemids.com/item/64/end_stone.png'; // 虚空星尘弹药
                    if (idNum === 64) return 'https://minecraftitemids.com/item/64/amethyst_shard.png'; // 循声炮弹药
                    if (idNum === 65) return 'https://minecraftitemids.com/item/64/cooked_beef.png'; // 熟牛排
                    if (idNum === 66) return 'https://minecraftitemids.com/item/64/potion.png'; // 治疗药水
                    return 'https://minecraftitemids.com/item/64/potion.png';
                } else if (idNum >= 67 && idNum <= 70) {
                    // 特殊槽位 - 根据ID返回不同图标
                    if (idNum === 67) return 'https://minecraftitemids.com/item/64/barrier.png'; // 空武器槽位
                    if (idNum === 68) return 'https://minecraftitemids.com/item/64/structure_void.png'; // 空道具槽位
                    if (idNum === 69) return 'https://minecraftitemids.com/item/64/chain.png'; // 锁定槽位
                    if (idNum === 70) return 'https://minecraftitemids.com/item/64/iron_sword.png'; // 僵尸猎人之剑
                    return 'https://minecraftitemids.com/item/64/barrier.png';
                }
            }

            // 处理数字ID
            if (!isNaN(parseInt(itemId))) {
                const numId = parseInt(itemId);
                if (numId >= 1 && numId <= 24) {
                    // 武器类 - 根据ID返回不同图标
                    if (numId === 1) return 'https://minecraftitemids.com/item/64/flint_and_steel.png'; // 手枪
                    if (numId === 2) return 'https://minecraftitemids.com/item/64/crossbow.png';
                    if (numId === 3) return 'https://minecraftitemids.com/item/64/bow.png';
                    if (numId === 4) return 'https://minecraftitemids.com/item/64/trident.png';
                    if (numId === 5) return 'https://minecraftitemids.com/item/64/fire_charge.png';
                    if (numId === 6) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                    if (numId === 7) return 'https://minecraftitemids.com/item/64/spyglass.png';
                    if (numId === 8) return 'https://minecraftitemids.com/item/64/blue_ice.png';
                    if (numId === 9) return 'https://minecraftitemids.com/item/64/lightning_rod.png';
                    if (numId === 10) return 'https://minecraftitemids.com/item/64/piston.png';
                    if (numId === 11) return 'https://minecraftitemids.com/item/64/crossbow.png'; // 突击步枪
                    if (numId === 12) return 'https://minecraftitemids.com/item/64/repeater.png';
                    if (numId === 13) return 'https://minecraftitemids.com/item/64/end_rod.png';
                    if (numId === 14) return 'https://minecraftitemids.com/item/64/netherite_sword.png';
                    if (numId === 15) return 'https://minecraftitemids.com/item/64/tnt.png';
                    if (numId === 16) return 'https://minecraftitemids.com/item/64/beacon.png';
                    if (numId === 17) return 'https://minecraftitemids.com/item/64/end_crystal.png';
                    if (numId === 18) return 'https://minecraftitemids.com/item/64/note_block.png';
                    if (numId === 19) return 'https://minecraftitemids.com/item/64/glowstone.png';
                    if (numId === 20) return 'https://minecraftitemids.com/item/64/prismarine_crystals.png';
                    if (numId === 21) return 'https://minecraftitemids.com/item/64/ender_pearl.png';
                    if (numId === 22) return 'https://minecraftitemids.com/item/64/diamond.png';
                    if (numId === 23) return 'https://minecraftitemids.com/item/64/end_stone.png';
                    if (numId === 24) return 'https://minecraftitemids.com/item/64/amethyst_shard.png';
                    return 'https://minecraftitemids.com/item/64/wooden_hoe.png';
                } else if (numId >= 25 && numId <= 37) {
                    // 护甲类 - 根据ID返回不同图标
                    if (numId === 25 || numId === 26) return 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                    if (numId === 27 || numId === 28) return 'https://minecraftitemids.com/item/64/chainmail_chestplate.png';
                    if (numId === 29 || numId === 30) return 'https://minecraftitemids.com/item/64/iron_chestplate.png';
                    if (numId === 31 || numId === 32) return 'https://minecraftitemids.com/item/64/diamond_chestplate.png';
                    if (numId === 33 || numId === 34) return 'https://minecraftitemids.com/item/64/diamond_chestplate.png';
                    if (numId === 35 || numId === 36) return 'https://minecraftitemids.com/item/64/netherite_chestplate.png';
                    return 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                } else if (numId >= 38 && numId <= 66) {
                    // 物品类 - 根据ID返回不同图标
                    if (numId === 38 || numId === 39) return 'https://minecraftitemids.com/item/64/potion.png'; // 药水
                    if (numId >= 40 && numId <= 49) return 'https://minecraftitemids.com/item/64/gunpowder.png'; // 基础弹药
                    if (numId === 50) return 'https://minecraftitemids.com/item/64/golden_apple.png'; // 金苹果
                    if (numId === 51) return 'https://minecraftitemids.com/item/64/paper.png'; // 武器凭证
                    if (numId === 52) return 'https://minecraftitemids.com/item/64/gunpowder.png'; // 压强枪弹药
                    if (numId === 53) return 'https://minecraftitemids.com/item/64/blaze_powder.png'; // 等离子枪弹药
                    if (numId === 54) return 'https://minecraftitemids.com/item/64/dragon_breath.png'; // 死神收割者弹药
                    if (numId === 55) return 'https://minecraftitemids.com/item/64/tnt.png'; // 毁灭者弹药
                    if (numId === 56) return 'https://minecraftitemids.com/item/64/nether_star.png'; // 超级激光炮弹药
                    if (numId === 57) return 'https://minecraftitemids.com/item/64/end_crystal.png'; // 黑洞吞噬者弹药
                    if (numId === 58) return 'https://minecraftitemids.com/item/64/note_block.png'; // 音轨步枪弹药
                    if (numId === 59) return 'https://minecraftitemids.com/item/64/glowstone_dust.png'; // 能量脉冲枪弹药
                    if (numId === 60) return 'https://minecraftitemids.com/item/64/prismarine_crystals.png'; // 彩虹喷射器弹药
                    if (numId === 61) return 'https://minecraftitemids.com/item/64/ender_pearl.png'; // 传送枪弹药
                    if (numId === 62) return 'https://minecraftitemids.com/item/64/diamond.png'; // 星辉主宰者弹药
                    if (numId === 63) return 'https://minecraftitemids.com/item/64/end_stone.png'; // 虚空星尘弹药
                    if (numId === 64) return 'https://minecraftitemids.com/item/64/amethyst_shard.png'; // 循声炮弹药
                    if (numId === 65) return 'https://minecraftitemids.com/item/64/cooked_beef.png'; // 熟牛排
                    if (numId === 66) return 'https://minecraftitemids.com/item/64/potion.png'; // 治疗药水
                    return 'https://minecraftitemids.com/item/64/potion.png';
                } else if (numId >= 67 && numId <= 70) {
                    // 特殊槽位 - 根据ID返回不同图标
                    if (numId === 67) return 'https://minecraftitemids.com/item/64/barrier.png'; // 空武器槽位
                    if (numId === 68) return 'https://minecraftitemids.com/item/64/structure_void.png'; // 空道具槽位
                    if (numId === 69) return 'https://minecraftitemids.com/item/64/chain.png'; // 锁定槽位
                    if (numId === 70) return 'https://minecraftitemids.com/item/64/iron_sword.png'; // 僵尸猎人之剑
                    return 'https://minecraftitemids.com/item/64/barrier.png';
                }
            }

            // 使用Minecraft物品ID获取图片
            return `https://minecraftitemids.com/item/64/${itemId.toLowerCase()}.png`;
        };

        // 获取物品名称
        const getItemName = (type, id) => {
            if (!type || !id) return '未知物品';

            // 尝试从Shoot插件物品名称映射中获取
            const idStr = typeof id === 'number' ? `id${id}` : id;
            if (shootItemNames.value[type] && shootItemNames.value[type][idStr]) {
                return shootItemNames.value[type][idStr];
            }

            // 返回默认名称
            switch (type) {
                case 'wp':
                    return `武器 #${id}`;
                case 'ar':
                    return `护甲 #${id}`;
                case 'it':
                    return `物品 #${id}`;
                default:
                    return `${type} ${id}`;
            }
        };

        // 处理图片加载错误
        const handleImageError = (event) => {
            const img = event.target;
            const alt = img.alt || '';
            const itemId = alt.split(' ')[0]; // 尝试从alt文本中提取物品ID

            // 首先尝试根据物品ID获取更准确的替代图标
            if (itemId) {
                // 检查是否为数字ID或id格式
                const numMatch = itemId.match(/^(\d+)$/) || itemId.match(/^id(\d+)$/);
                if (numMatch) {
                    const idNum = parseInt(numMatch[1]);
                    // 根据ID范围选择合适的图标
                    if (idNum >= 1 && idNum <= 24) {
                        // 武器类 - 根据具体ID选择图标
                        if (idNum === 1) { img.src = 'https://minecraftitemids.com/item/64/flint_and_steel.png'; return; } // 手枪
                        if (idNum === 2) { img.src = 'https://minecraftitemids.com/item/64/crossbow.png'; return; }
                        if (idNum === 3) { img.src = 'https://minecraftitemids.com/item/64/bow.png'; return; }
                        if (idNum === 4) { img.src = 'https://minecraftitemids.com/item/64/trident.png'; return; }
                        if (idNum === 5) { img.src = 'https://minecraftitemids.com/item/64/fire_charge.png'; return; }
                        if (idNum === 6) { img.src = 'https://minecraftitemids.com/item/64/lightning_rod.png'; return; }
                        if (idNum === 7) { img.src = 'https://minecraftitemids.com/item/64/spyglass.png'; return; }
                        if (idNum === 8) { img.src = 'https://minecraftitemids.com/item/64/blue_ice.png'; return; }
                        if (idNum === 9) { img.src = 'https://minecraftitemids.com/item/64/lightning_rod.png'; return; }
                        if (idNum === 10) { img.src = 'https://minecraftitemids.com/item/64/piston.png'; return; }
                        if (idNum === 11) { img.src = 'https://minecraftitemids.com/item/64/crossbow.png'; return; } // 突击步枪
                        if (idNum === 12) { img.src = 'https://minecraftitemids.com/item/64/repeater.png'; return; }
                        if (idNum === 13) { img.src = 'https://minecraftitemids.com/item/64/end_rod.png'; return; }
                        if (idNum === 14) { img.src = 'https://minecraftitemids.com/item/64/netherite_sword.png'; return; }
                        if (idNum === 15) { img.src = 'https://minecraftitemids.com/item/64/tnt.png'; return; }
                        if (idNum === 16) { img.src = 'https://minecraftitemids.com/item/64/beacon.png'; return; }
                        if (idNum === 17) { img.src = 'https://minecraftitemids.com/item/64/end_crystal.png'; return; }
                        if (idNum === 18) { img.src = 'https://minecraftitemids.com/item/64/note_block.png'; return; }
                        if (idNum === 19) { img.src = 'https://minecraftitemids.com/item/64/glowstone.png'; return; }
                        if (idNum === 20) { img.src = 'https://minecraftitemids.com/item/64/prismarine_crystals.png'; return; }
                        if (idNum === 21) { img.src = 'https://minecraftitemids.com/item/64/ender_pearl.png'; return; }
                        if (idNum === 22) { img.src = 'https://minecraftitemids.com/item/64/diamond.png'; return; }
                        if (idNum === 23) { img.src = 'https://minecraftitemids.com/item/64/end_stone.png'; return; }
                        if (idNum === 24) { img.src = 'https://minecraftitemids.com/item/64/amethyst_shard.png'; return; }
                        img.src = 'https://minecraftitemids.com/item/64/wooden_hoe.png';
                        return;
                    } else if (idNum >= 25 && idNum <= 37) {
                        // 护甲类 - 根据ID返回不同图标
                        if (idNum === 25 || idNum === 26) { img.src = 'https://minecraftitemids.com/item/64/leather_chestplate.png'; return; }
                        if (idNum === 27 || idNum === 28) { img.src = 'https://minecraftitemids.com/item/64/chainmail_chestplate.png'; return; }
                        if (idNum === 29 || idNum === 30) { img.src = 'https://minecraftitemids.com/item/64/iron_chestplate.png'; return; }
                        if (idNum === 31 || idNum === 32) { img.src = 'https://minecraftitemids.com/item/64/diamond_chestplate.png'; return; }
                        if (idNum === 33 || idNum === 34) { img.src = 'https://minecraftitemids.com/item/64/diamond_chestplate.png'; return; }
                        if (idNum === 35 || idNum === 36) { img.src = 'https://minecraftitemids.com/item/64/netherite_chestplate.png'; return; }
                        img.src = 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                        return;
                    } else if (idNum >= 38 && idNum <= 66) {
                        // 物品类 - 根据ID返回不同图标
                        if (idNum === 38 || idNum === 39) { img.src = 'https://minecraftitemids.com/item/64/potion.png'; return; } // 药水
                        if (idNum >= 40 && idNum <= 49) { img.src = 'https://minecraftitemids.com/item/64/gunpowder.png'; return; } // 基础弹药
                        if (idNum === 50) { img.src = 'https://minecraftitemids.com/item/64/golden_apple.png'; return; } // 金苹果
                        if (idNum === 51) { img.src = 'https://minecraftitemids.com/item/64/paper.png'; return; } // 武器凭证
                        if (idNum === 52) { img.src = 'https://minecraftitemids.com/item/64/gunpowder.png'; return; } // 压强枪弹药
                        if (idNum === 53) { img.src = 'https://minecraftitemids.com/item/64/blaze_powder.png'; return; } // 等离子枪弹药
                        if (idNum === 54) { img.src = 'https://minecraftitemids.com/item/64/dragon_breath.png'; return; } // 死神收割者弹药
                        if (idNum === 55) { img.src = 'https://minecraftitemids.com/item/64/tnt.png'; return; } // 毁灭者弹药
                        if (idNum === 56) { img.src = 'https://minecraftitemids.com/item/64/nether_star.png'; return; } // 超级激光炮弹药
                        if (idNum === 57) { img.src = 'https://minecraftitemids.com/item/64/end_crystal.png'; return; } // 黑洞吞噬者弹药
                        if (idNum === 58) { img.src = 'https://minecraftitemids.com/item/64/note_block.png'; return; } // 音轨步枪弹药
                        if (idNum === 59) { img.src = 'https://minecraftitemids.com/item/64/glowstone_dust.png'; return; } // 能量脉冲枪弹药
                        if (idNum === 60) { img.src = 'https://minecraftitemids.com/item/64/prismarine_crystals.png'; return; } // 彩虹喷射器弹药
                        if (idNum === 61) { img.src = 'https://minecraftitemids.com/item/64/ender_pearl.png'; return; } // 传送枪弹药
                        if (idNum === 62) { img.src = 'https://minecraftitemids.com/item/64/diamond.png'; return; } // 星辉主宰者弹药
                        if (idNum === 63) { img.src = 'https://minecraftitemids.com/item/64/end_stone.png'; return; } // 虚空星尘弹药
                        if (idNum === 64) { img.src = 'https://minecraftitemids.com/item/64/amethyst_shard.png'; return; } // 循声炮弹药
                        if (idNum === 65) { img.src = 'https://minecraftitemids.com/item/64/cooked_beef.png'; return; } // 熟牛排
                        if (idNum === 66) { img.src = 'https://minecraftitemids.com/item/64/potion.png'; return; } // 治疗药水
                        img.src = 'https://minecraftitemids.com/item/64/potion.png';
                        return;
                    } else if (idNum >= 67 && idNum <= 70) {
                        // 特殊槽位 - 根据ID返回不同图标
                        if (idNum === 67) { img.src = 'https://minecraftitemids.com/item/64/barrier.png'; return; } // 空武器槽位
                        if (idNum === 68) { img.src = 'https://minecraftitemids.com/item/64/structure_void.png'; return; } // 空道具槽位
                        if (idNum === 69) { img.src = 'https://minecraftitemids.com/item/64/chain.png'; return; } // 锁定槽位
                        if (idNum === 70) { img.src = 'https://minecraftitemids.com/item/64/iron_sword.png'; return; } // 僵尸猎人之剑
                        img.src = 'https://minecraftitemids.com/item/64/barrier.png';
                        return;
                    }
                }

                // 检查是否为Shoot插件物品ID格式
                if (itemId.startsWith('wp_') || itemId.startsWith('ar_') || itemId.startsWith('it_')) {
                    const parts = itemId.split('_');
                    const type = parts[0];
                    // 使用parts[1]而不是存储为变量

                    switch (type) {
                        case 'wp':
                            img.src = 'https://minecraftitemids.com/item/64/wooden_hoe.png';
                            return;
                        case 'ar':
                            img.src = 'https://minecraftitemids.com/item/64/leather_chestplate.png';
                            return;
                        case 'it':
                            img.src = 'https://minecraftitemids.com/item/64/potion.png';
                            return;
                    }
                }
            }

            // 根据alt文本或父元素类名选择合适的默认图片
            if (alt.includes('武器') || alt.includes('枪') || alt.includes('剑') || alt.includes('斧') ||
                img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.startsWith('id') &&
                parseInt(img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.substring(2)) <= 24) {
                img.src = 'https://minecraftitemids.com/item/64/wooden_hoe.png';
            } else if (alt.includes('护甲') || alt.includes('套装') || alt.includes('头盔') || alt.includes('胸甲') ||
                      alt.includes('护腿') || alt.includes('靴子') ||
                      img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.startsWith('id') &&
                      parseInt(img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.substring(2)) >= 25 &&
                      parseInt(img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.substring(2)) <= 37) {
                img.src = 'https://minecraftitemids.com/item/64/leather_chestplate.png';
            } else if (alt.includes('弹药') || alt.includes('药水') || alt.includes('苹果') ||
                      img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.startsWith('id') &&
                      parseInt(img.closest('.sequence-item')?.querySelector('.sequence-item-id')?.textContent.substring(2)) >= 38) {
                img.src = 'https://minecraftitemids.com/item/64/potion.png';
            } else {
                img.src = 'https://minecraftitemids.com/item/64/barrier.png';
            }
        };

        // 页面加载时获取游戏列表和处理URL参数
        onMounted(() => {
            loadGames().then(() => {
                // 检查URL中是否有game参数
                const urlParams = new URLSearchParams(window.location.search);
                const gameParam = urlParams.get('game');
                if (gameParam && games.value.includes(gameParam)) {
                    selectedGame.value = gameParam;
                    loadGameEquipment();
                }
            });
        });

        return {
            loading,
            loadingMessage,
            games,
            selectedGame,
            equipment,
            armorEquipment,
            selectedSlot,
            selectedArmorType,
            searchQuery,
            filterType,
            filterIdRange,
            items,
            filteredItems,
            shootItemNames,
            sectionExpanded,
            allSectionsExpanded,
            loadGames,
            loadGameEquipment,
            saveEquipment,
            selectSlot,
            selectArmorSlot,
            selectItem,
            clearSlot,
            clearArmorSlot,
            dragItem,
            dragNewItem,
            dragReferenceItem,
            dragEnd,
            highlightSlot,
            unhighlightSlot,
            dropItem,
            dropArmorItem,
            getItemImageUrl,
            getItemName,
            getItemDisplayName,
            handleImageError,
            scrollToItemSelector,
            toggleSection,
            toggleAllSections,
            logout
        };
    }
}).mount('#app');
