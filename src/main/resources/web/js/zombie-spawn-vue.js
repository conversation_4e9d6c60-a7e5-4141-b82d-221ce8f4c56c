// 使用Vue 3的Composition API
const { createApp, ref, reactive, computed, onMounted, watch } = Vue;

// 预定义的怪物类型和描述
const MONSTER_PRESETS = {
    zombies: [
        { id: 'id1', name: '普通僵尸', description: '无特殊功能。20点血，手持木斧，无防具' },
        { id: 'id2', name: '小僵尸', description: '速度2buff。10点血，手持木棍，无防具' },
        { id: 'id3', name: '路障僵尸', description: '无特殊功能，30点血，手持木斧，带着保护附魔1的铁头盔' },
        { id: 'id4', name: '钻斧僵尸', description: '无特殊功能，20点血，手持钻石斧，带着保护附魔1的铁护腿' },
        { id: 'id5', name: '剧毒僵尸', description: '特殊功能:被剧毒僵尸攻击后获得剧毒1buff效果，持续三秒，10点血，穿全身皮革套' },
        { id: 'id6', name: '双生僵尸', description: '特殊功能:每5分钟生成一个普通僵尸，20点血，手持铁剑，穿着铁胸甲' },
        { id: 'id7', name: '骷髅僵尸', description: '特殊功能:可以向玩家发射箭矢，30点血，手持钻石剑，带着铁护腿，穿着铁盔甲' },
        { id: 'id8', name: '武装僵尸', description: '无特殊功能，100点血，手持钻石剑，带着铁套，自带速度1buff' },
        { id: 'id9', name: '肥胖僵尸', description: '无特殊功能，100点血，无武器，无装备，体型是普通僵尸的两倍' },
        { id: 'id10', name: '法师僵尸', description: '特殊功能:每10秒召唤两个双生僵尸，50点血，手持木斧，装备全套连锁套' },
        { id: 'id11', name: '自爆僵尸', description: '特殊功能:靠近玩家后发生自爆，30点血，无武器，带着保护附魔1的皮革头盔' },
        { id: 'id12', name: '毒箭僵尸', description: '特殊功能:每隔3秒射出剧毒箭矢，50点血，手持弓，带着铁护腿，穿着铁盔甲' },
        { id: 'id13', name: '电击僵尸', description: '特殊功能:每3秒对自己10*10的范围内释放电流，50点血，手持木棍，带着铁套' },
        { id: 'id14', name: '冰冻僵尸', description: '特殊功能:每3秒对自己10*10的范围内冻结，70点血，手持钻石，带着锁链套' },
        { id: 'id15', name: '暗影僵尸', description: '特殊功能：隐身效果和隐匿攻击，60点血，手持铁剑，佩戴暗影头盔' },
        { id: 'id16', name: '毁灭僵尸', description: '特殊功能：力量效果持续增加攻击力，150点血，手持钻石剑，全套铁护甲' },
        { id: 'id17', name: '雷霆僵尸', description: '特殊功能：雷电攻击，80点血，手持弓，全套链甲' },
        { id: 'id18', name: '变异科学家', description: '特殊功能：多种高级能力，600点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲' },
        { id: 'id19', name: '变异法师', description: '特殊功能：闪电攻击、失明减速效果和召唤能力，400点血，木棍，全套附魔保护三皮革甲' },
        { id: 'id20', name: '气球僵尸', description: '特殊功能：持续漂浮效果，25点血，手持气球棒' },
        { id: 'id21', name: '迷雾僵尸', description: '特殊功能：生成迷雾降低玩家视野和移动速度，40点血，手持迷雾生成器' },
        { id: 'id22', name: '变异雷霆僵尸', description: '特殊功能：多种强力雷电和召唤能力，800点血，手持弓，全套附魔保护2锁链套' },
        { id: 'id23', name: '终极毁灭僵尸', description: '特殊功能：多种强力能力，1000点血，全套附魔装备' },
        { id: 'id24', name: '变异暗影僵尸', description: '特殊功能：高级隐身和攻击能力，500点血，手持锋利5钻石剑' },
        { id: 'id25', name: '变异博士', description: '终极BOSS：多种强力特殊能力，2048点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲' }
    ],
    entities: [
        { id: 'idc1', name: '变异僵尸01', description: '僵尸猪人形态，每次攻击对玩家施加剧毒效果，100点血，手持附魔锋利1铁剑，全套皮革套' },
        { id: 'idc2', name: '变异僵尸02', description: '骷髅形态，100点血，手持附魔冲击2的弓，带着苦力怕头颅' },
        { id: 'idc3', name: '变异烈焰人', description: '烈焰人形态，特殊能力：发射烈焰粒子和烈焰弹，100点血' },
        { id: 'idc4', name: '变异爬行者', description: '闪电苦力怕形态，特殊能力：闪电伤害，50点血' },
        { id: 'idc5', name: '变异末影螨', description: '末影螨形态，特殊能力：释放电流，30点血' },
        { id: 'idc6', name: '变异蜘蛛', description: '蜘蛛形态，特殊能力：发射烈焰弹，50点血' },
        { id: 'idc7', name: '灾厄卫道士', description: '掠夺者形态，特殊能力：每5秒召唤两个仆从，发射粒子球，540点血，手持下界合金斧' },
        { id: 'idc8', name: '灾厄唤魔者', description: '唤魔者形态，特殊能力：每10秒召唤两个掠夺者，释放DNA螺旋法术，600点血，速度2效果' },
        { id: 'idc9', name: '灾厄劫掠兽', description: '劫掠兽形态，特殊能力：强力冲撞和跳跃，1000点血，跳跃提升3和速度1效果' },
        { id: 'idc10', name: '变异僵尸马', description: '僵尸马形态，特殊能力：每10秒召唤4个武装僵尸，冲撞玩家，300点血，速度5效果' },
        { id: 'idc11', name: '变异岩浆怪', description: '岩浆怪形态，特殊能力：生成火焰粒子环，造成火焰伤害，100点血，速度3效果' },
        { id: 'idc12', name: '变异骷髅', description: '凋零骷髅形态，特殊功能：发射凋零箭，200点血，手持附魔弓' },
        { id: 'idc13', name: '变异僵尸3', description: '溺尸形态，特殊功能：每20秒召唤4个变异岩浆怪，200点血，穿着黑色皮革鞋子' },
        { id: 'idc14', name: '变异僵尸04', description: '僵尸形态，特殊功能：每15秒召唤2个变异僵尸01，150点血，手持铁剑' },
        { id: 'idc15', name: '鲜血猪灵', description: '猪灵形态，特殊功能：每次攻击造成流血效果，250点血，手持金剑' },
        { id: 'idc16', name: '暗影潜影贝', description: '潜影贝形态，特殊功能：隐身效果和打乱玩家物品栏，200点血' },
        { id: 'idc17', name: '变异雪傀儡', description: '雪傀儡形态，特殊功能：发射冰霜弹，造成减速效果，300点血' },
        { id: 'idc18', name: '变异铁傀儡', description: '铁傀儡形态，特殊功能：远程声波攻击和高速方块投射，2000点血' },
        { id: 'idc19', name: '变异僵尸Max', description: '僵尸形态，特殊功能：附魔三叉戟，瞬移能力和隐身周期，2000点血' },
        { id: 'idc20', name: '灵魂坚守者', description: '监守者形态，特殊功能：声波攻击和高速冲撞，1000点血' },
        { id: 'idc21', name: '凋零领主', description: '凋零形态，特殊功能：每4秒随机召唤变异生物，高速发射凋零头颅，黑曜石方块攻击和下界栅栏攻击，6999点血' },
        { id: 'idc22', name: '异变之王', description: '末影龙形态，特殊功能：末影人粒子场，黑曜石方块攻击，下界栅栏攻击，随机召唤怪物，10999点血，速度3效果' }
    ],
    npcs: [
        { id: 'idn1', name: '感染者1史蒂夫', description: '会自动攻击玩家直到自己死亡' },
        { id: 'idn2', name: '感染者2艾利克斯', description: '会自动攻击玩家直到自己死亡' },
        { id: 'idn3', name: '感染者农民', description: '特殊能力：死亡时召唤3个路障僵尸，50点血，武器：木锄，防具：皮革套' },
        { id: 'idn4', name: '感染者居民', description: '特殊能力：死亡时召唤3个普通僵尸，70点血，武器：木剑，防具：皮革套装' },
        { id: 'idn5', name: '感染猪', description: '特殊能力：死亡时对10*10范围内玩家造成剧毒II效果持续30秒，200点血' }
    ]
};

// 用户预设列表（将通过API加载）
const USER_PRESETS = [];

// 自定义日志和错误处理
function debugLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${message}`);

    // 添加到调试面板
    const debugLog = document.getElementById('debug-log');
    if (debugLog) {
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${message}`;
        debugLog.appendChild(logEntry);
        debugLog.scrollTop = debugLog.scrollHeight;
    }
}

function showError(message, error) {
    console.error('错误:', message, error);

    // 显示错误面板
    const errorContainer = document.getElementById('error-container');
    const errorMessage = document.getElementById('error-message');

    if (errorContainer && errorMessage) {
        errorMessage.textContent = message + (error ? ': ' + error.message : '');
        errorContainer.style.display = 'block';
    } else {
        alert('错误: ' + message + (error ? ': ' + error.message : ''));
    }
}

// 添加全局错误处理
window.addEventListener('error', function(event) {
    showError('页面发生错误', { message: event.message + ' at ' + event.filename + ':' + event.lineno });

    // 显示调试面板
    const debugInfo = document.getElementById('debug-info');
    if (debugInfo) {
        debugInfo.style.display = 'block';
    }
});

// 添加键盘快捷键显示调试面板
document.addEventListener('keydown', function(event) {
    // Ctrl+Shift+D 显示调试面板
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }
    }
});

debugLog('开始初始化Vue应用...');

createApp({
    setup() {
        console.log('Vue setup函数开始执行...');

        // 检查身份验证状态
        try {
            debugLog('检查身份验证...');

            // 检查auth.js是否已加载
            if (typeof checkAuth !== 'function') {
                showError('身份验证模块未加载', { message: 'auth.js文件可能未正确加载' });
                return;
            }

            if (!checkAuth()) {
                debugLog('身份验证失败，重定向到登录页面');
                return;
            }
            debugLog('身份验证成功');
        } catch (error) {
            showError('身份验证检查出错', error);
            return;
        }

        // 基础状态
        const loading = ref(true);
        const loadingMessage = ref('正在加载...');
        const games = ref([]);
        const selectedGame = ref('');
        const totalRounds = ref(0);
        const selectedRound = ref(null);
        const spawnPoints = ref([]);
        const roundModes = ref({});
        const expandedSpawnPoints = reactive({});

        // 批量设置状态
        const batchSettings = reactive({
            startRound: 1,
            endRound: 1,
            spawnPoint: '',
            monsterType: 'zombie',
            monsterId: 'id1',
            count: '5'
        });

        // 预设管理状态
        const presetManager = reactive({
            showApplyModal: false,
            selectedRound: null,
            selectedSpawnPoint: null
        });

        // 加载游戏列表
        const loadGames = async () => {
            loading.value = true;
            loadingMessage.value = '正在加载游戏列表...';

            try {
                const response = await fetchWithAuth('/api/games');
                const data = await response.json();

                if (data && data.games) {
                    // 提取游戏名称列表
                    games.value = data.games.map(game => game.name);
                    debugLog('加载了 ' + games.value.length + ' 个游戏');
                    debugLog('游戏列表: ' + JSON.stringify(games.value));
                } else {
                    showError('加载游戏列表失败', { message: JSON.stringify(data) });
                }
            } catch (error) {
                console.error('加载游戏列表出错:', error);
            } finally {
                loading.value = false;
            }
        };

        // 存储完整的生成点数据
        const spawnPointsData = ref({});

        // 加载游戏的出怪设置
        const loadGameSpawnSettings = async () => {
            if (!selectedGame.value) return;

            loading.value = true;
            loadingMessage.value = '正在加载游戏出怪设置...';

            try {
                debugLog(`正在加载游戏 ${selectedGame.value} 的出怪设置...`);
                const response = await fetchWithAuth(`/api/game?name=${encodeURIComponent(selectedGame.value)}`);
                const data = await response.json();

                if (data) {
                    debugLog('加载游戏数据成功');

                    // 设置总回合数
                    totalRounds.value = data.rounds || 0;
                    debugLog(`总回合数: ${totalRounds.value}`);

                    // 加载生成点
                    if (data.zombieSpawns) {
                        // 处理生成点数据
                        if (Array.isArray(data.zombieSpawns)) {
                            // 如果是数组格式，转换为对象格式
                            const spawnsObj = {};
                            data.zombieSpawns.forEach(spawn => {
                                spawnsObj[spawn.name] = {
                                    type: spawn.type || 'id',
                                    world: spawn.world || 'world',
                                    x: spawn.x || 0,
                                    y: spawn.y || 0,
                                    z: spawn.z || 0,
                                    enabled: spawn.enabled || false
                                };
                            });
                            spawnPointsData.value = spawnsObj;
                        } else {
                            // 如果是对象格式，直接使用
                            spawnPointsData.value = data.zombieSpawns;
                        }

                        spawnPoints.value = Object.keys(spawnPointsData.value);
                        debugLog(`加载了 ${spawnPoints.value.length} 个生成点: ${spawnPoints.value.join(', ')}`);

                        // 记录每个生成点的详细信息
                        Object.keys(spawnPointsData.value).forEach(spawnName => {
                            const spawn = spawnPointsData.value[spawnName];
                            debugLog(`生成点 ${spawnName}: 世界=${spawn.world}, 坐标=(${spawn.x}, ${spawn.y}, ${spawn.z}), 启用=${spawn.enabled}`);
                        });

                        // 初始化展开状态
                        spawnPoints.value.forEach(spawn => {
                            expandedSpawnPoints[spawn] = false;
                        });
                    } else {
                        spawnPoints.value = [];
                        spawnPointsData.value = {};
                        debugLog('没有找到生成点数据');
                    }

                    // 加载回合模式
                    if (data.roundModes) {
                        roundModes.value = data.roundModes;
                        debugLog(`加载了回合模式数据: ${Object.keys(data.roundModes).length} 个回合`);

                        // 详细记录每个回合的配置
                        Object.keys(data.roundModes).forEach(roundKey => {
                            const round = data.roundModes[roundKey];
                            debugLog(`回合 ${roundKey} 配置:`);

                            Object.keys(round).forEach(spawnName => {
                                const spawnConfig = round[spawnName];
                                debugLog(`  生成点 ${spawnName}: ${Object.keys(spawnConfig).length} 个配置`);

                                Object.keys(spawnConfig).forEach(configKey => {
                                    const config = spawnConfig[configKey];
                                    debugLog(`    ${configKey}: 类型=${config.monsterType}, ID=${config.monsterId}, 数量=${config.count}`);
                                });
                            });
                        });
                    } else {
                        roundModes.value = {};
                        debugLog('没有找到回合模式数据');
                    }

                    // 默认选择第一个回合
                    if (totalRounds.value > 0) {
                        selectedRound.value = 1;
                        debugLog(`默认选择第 ${selectedRound.value} 回合`);
                    } else {
                        selectedRound.value = null;
                        debugLog('没有可选的回合');
                    }

                    // 显示调试面板以便查看详细信息
                    document.getElementById('debug-info').style.display = 'block';
                } else {
                    showError('加载游戏数据失败', { message: '服务器返回空数据' });
                }
            } catch (error) {
                console.error('加载游戏出怪设置出错:', error);
            } finally {
                loading.value = false;
            }
        };

        // 更新回合数
        const updateRounds = () => {
            if (totalRounds.value < 1) {
                totalRounds.value = 1;
            }

            // 如果当前选择的回合超出了总回合数，重置为第一回合
            if (selectedRound.value > totalRounds.value) {
                selectedRound.value = 1;
            }
        };

        // 选择回合
        const selectRound = (round) => {
            selectedRound.value = round;
        };

        // 切换生成点展开状态
        const toggleSpawnPoint = (spawnPoint) => {
            expandedSpawnPoints[spawnPoint] = !expandedSpawnPoints[spawnPoint];
        };

        // 获取特定回合和生成点的怪物配置
        const getSpawnPointConfigs = (round, spawnPoint) => {
            const roundKey = `round${round}`;
            if (roundModes.value[roundKey] && roundModes.value[roundKey][spawnPoint]) {
                return roundModes.value[roundKey][spawnPoint];
            }
            return {};
        };

        // 添加新的怪物配置
        const addMonsterConfig = (round, spawnPoint) => {
            const roundKey = `round${round}`;

            console.log('添加怪物配置:', { round, spawnPoint, roundKey });

            // 创建深拷贝以确保响应式更新
            const newRoundModes = JSON.parse(JSON.stringify(roundModes.value));

            // 确保回合和生成点的数据结构存在
            if (!newRoundModes[roundKey]) {
                newRoundModes[roundKey] = {};
            }

            if (!newRoundModes[roundKey][spawnPoint]) {
                newRoundModes[roundKey][spawnPoint] = {};
            }

            // 生成新的配置键名
            const configKeys = Object.keys(newRoundModes[roundKey][spawnPoint]);
            let newKey = '刷怪逻辑1';

            if (configKeys.length > 0) {
                // 找到最大的数字并加1
                const numbers = configKeys
                    .filter(key => key.startsWith('刷怪逻辑'))
                    .map(key => parseInt(key.replace('刷怪逻辑', '')) || 0);

                const maxNumber = Math.max(...numbers, 0);
                newKey = `刷怪逻辑${maxNumber + 1}`;
            }

            // 添加新配置
            newRoundModes[roundKey][spawnPoint][newKey] = {
                monsterType: 'zombie',
                monsterId: 'id1',
                count: '5',
                type: 'id',
                number: '1'
            };

            console.log('添加配置:', newKey, '到', roundKey, spawnPoint);

            // 使用深拷贝的新对象替换原对象，确保Vue响应式系统检测到变化
            roundModes.value = newRoundModes;

            console.log('添加后的roundModes:', JSON.stringify(roundModes.value, null, 2));
        };

        // 移除怪物配置
        const removeMonsterConfig = (round, spawnPoint, configKey) => {
            const roundKey = `round${round}`;

            console.log('删除怪物配置:', { round, spawnPoint, configKey, roundKey });
            console.log('删除前的roundModes:', JSON.stringify(roundModes.value, null, 2));

            if (roundModes.value[roundKey] &&
                roundModes.value[roundKey][spawnPoint] &&
                roundModes.value[roundKey][spawnPoint][configKey]) {

                // 创建深拷贝以确保响应式更新
                const newRoundModes = JSON.parse(JSON.stringify(roundModes.value));

                // 删除特定配置
                delete newRoundModes[roundKey][spawnPoint][configKey];
                console.log('已删除配置:', configKey);

                // 如果生成点没有配置了，删除生成点
                if (Object.keys(newRoundModes[roundKey][spawnPoint]).length === 0) {
                    delete newRoundModes[roundKey][spawnPoint];
                    console.log('生成点配置为空，已删除生成点:', spawnPoint);
                }

                // 如果回合没有配置了，删除回合
                if (Object.keys(newRoundModes[roundKey]).length === 0) {
                    delete newRoundModes[roundKey];
                    console.log('回合配置为空，已删除回合:', roundKey);
                }

                // 使用深拷贝的新对象替换原对象，确保Vue响应式系统检测到变化
                roundModes.value = newRoundModes;

                console.log('删除后的roundModes:', JSON.stringify(roundModes.value, null, 2));
                console.log('删除操作完成');
            } else {
                console.warn('要删除的配置不存在:', { roundKey, spawnPoint, configKey });
                console.log('当前roundModes结构:', JSON.stringify(roundModes.value, null, 2));
            }
        };

        // 应用批量设置
        const applyBatchSettings = () => {
            // 验证输入
            if (!batchSettings.spawnPoint) {
                alert('请选择生成点');
                return;
            }

            if (batchSettings.startRound > batchSettings.endRound) {
                alert('起始回合不能大于结束回合');
                return;
            }

            if (batchSettings.startRound < 1 || batchSettings.endRound > totalRounds.value) {
                alert(`回合范围必须在1至${totalRounds.value}之间`);
                return;
            }

            console.log('应用批量设置:', batchSettings);

            // 创建深拷贝以确保响应式更新
            const newRoundModes = JSON.parse(JSON.stringify(roundModes.value));

            // 对每个回合应用设置
            for (let i = batchSettings.startRound; i <= batchSettings.endRound; i++) {
                const roundKey = `round${i}`;

                // 确保回合和生成点的数据结构存在
                if (!newRoundModes[roundKey]) {
                    newRoundModes[roundKey] = {};
                }

                if (!newRoundModes[roundKey][batchSettings.spawnPoint]) {
                    newRoundModes[roundKey][batchSettings.spawnPoint] = {};
                }

                // 生成新的配置键名
                const configKeys = Object.keys(newRoundModes[roundKey][batchSettings.spawnPoint]);
                let newKey = '刷怪逻辑1';

                if (configKeys.length > 0) {
                    // 找到最大的数字并加1
                    const numbers = configKeys
                        .filter(key => key.startsWith('刷怪逻辑'))
                        .map(key => parseInt(key.replace('刷怪逻辑', '')) || 0);

                    const maxNumber = Math.max(...numbers, 0);
                    newKey = `刷怪逻辑${maxNumber + 1}`;
                }

                // 添加新配置
                newRoundModes[roundKey][batchSettings.spawnPoint][newKey] = {
                    monsterType: batchSettings.monsterType,
                    monsterId: batchSettings.monsterId,
                    count: batchSettings.count,
                    type: 'id',
                    number: '1'
                };

                console.log(`为回合${i}添加配置:`, newKey);
            }

            // 使用深拷贝的新对象替换原对象，确保Vue响应式系统检测到变化
            roundModes.value = newRoundModes;

            // 提示成功
            alert(`已为第${batchSettings.startRound}至第${batchSettings.endRound}回合的${batchSettings.spawnPoint}添加怪物配置`);
            console.log('批量设置应用完成');
        };

        // 获取怪物预设列表
        const getMonsterPresets = (type) => {
            if (type === 'zombie') {
                return MONSTER_PRESETS.zombies;
            } else if (type === 'entity') {
                return MONSTER_PRESETS.entities;
            } else if (type === 'npc') {
                return MONSTER_PRESETS.npcs;
            }
            return [];
        };

        // 获取怪物名称
        const getMonsterName = (type, id) => {
            const options = getMonsterPresets(type);
            const monster = options.find(m => m.id === id);
            return monster ? monster.name : `${type}:${id}`;
        };

        // 获取生成点详细信息
        const getSpawnPointInfo = (spawnName) => {
            return spawnPointsData.value[spawnName] || {
                type: 'id',
                world: 'world',
                x: 0,
                y: 0,
                z: 0,
                enabled: false
            };
        };

        // 格式化坐标显示
        const formatCoordinates = (x, y, z) => {
            return `(${Math.round(x * 100) / 100}, ${Math.round(y * 100) / 100}, ${Math.round(z * 100) / 100})`;
        };

        // 加载用户预设
        const loadUserPresets = async () => {
            try {
                debugLog('正在加载用户预设...');
                const response = await fetchWithAuth('/api/monster-presets');
                const data = await response.json();

                if (data && data.success && data.presets) {
                    // 清空并重新填充预设列表
                    USER_PRESETS.length = 0;
                    data.presets.forEach(preset => USER_PRESETS.push(preset));
                    debugLog(`成功加载 ${USER_PRESETS.length} 个用户预设`);
                } else {
                    debugLog('加载用户预设失败或没有预设');
                }
            } catch (error) {
                console.error('加载用户预设出错:', error);
                debugLog('加载用户预设出错: ' + error.message);
            }
        };

        // 打开应用预设模态框
        const openApplyPresetModal = (round, spawnPoint) => {
            if (!round || !spawnPoint) {
                alert('请先选择回合和生成点');
                return;
            }

            presetManager.selectedRound = round;
            presetManager.selectedSpawnPoint = spawnPoint;
            presetManager.showApplyModal = true;

            // 每次打开模态框时都重新加载用户预设，确保数据最新
            loadUserPresets();
            debugLog(`打开应用预设模态框，当前有 ${USER_PRESETS.length} 个预设`);
        };

        // 应用预设到指定回合和生成点
        const applyPresetToRound = (preset) => {
            try {
                debugLog(`开始应用预设: ${preset.name}`);
                const round = presetManager.selectedRound;
                const spawnPoint = presetManager.selectedSpawnPoint;

                if (!round || !spawnPoint) {
                    debugLog('错误: 未选择回合或生成点');
                    return;
                }

                const roundKey = `round${round}`;
                debugLog(`应用到回合: ${roundKey}, 生成点: ${spawnPoint}`);

                // 创建深拷贝以确保响应式更新
                const newRoundModes = JSON.parse(JSON.stringify(roundModes.value));

                // 确保回合和生成点的数据结构存在
                if (!newRoundModes[roundKey]) {
                    newRoundModes[roundKey] = {};
                    debugLog(`创建新的回合配置: ${roundKey}`);
                }

                if (!newRoundModes[roundKey][spawnPoint]) {
                    newRoundModes[roundKey][spawnPoint] = {};
                    debugLog(`创建新的生成点配置: ${spawnPoint}`);
                }

                // 为预设中的每个怪物添加配置
                if (!preset.monsters || !Array.isArray(preset.monsters)) {
                    debugLog('错误: 预设中没有怪物数组或格式不正确');
                    alert('预设格式错误，无法应用');
                    return;
                }

                preset.monsters.forEach((monster, index) => {
                    // 生成配置键名
                    const configKey = `刷怪逻辑${index + 1}`;

                    // 添加配置
                    newRoundModes[roundKey][spawnPoint][configKey] = {
                        monsterType: monster.monsterType,
                        monsterId: monster.monsterId,
                        count: monster.count,
                        type: 'id',
                        number: '1'
                    };

                    debugLog(`添加配置: ${configKey}, 类型=${monster.monsterType}, ID=${monster.monsterId}, 数量=${monster.count}`);
                });

                // 使用深拷贝的新对象替换原对象，确保Vue响应式系统检测到变化
                roundModes.value = newRoundModes;
                debugLog('视图已更新');

                // 关闭应用模态框
                presetManager.showApplyModal = false;

                // 提示成功
                alert(`已将预设"${preset.name}"应用到第${round}回合的${spawnPoint}`);
                debugLog('预设应用成功');
            } catch (error) {
                console.error('应用预设出错:', error);
                debugLog('应用预设出错: ' + error.message);
                alert('应用预设时出错: ' + error.message);
            }
        };

        // 保存出怪设置
        const saveSpawnSettings = async () => {
            if (!selectedGame.value) return;

            loading.value = true;
            loadingMessage.value = '正在保存出怪设置...';

            try {
                // 确保使用最新的roundModes数据，创建深拷贝避免引用问题
                const currentRoundModes = JSON.parse(JSON.stringify(roundModes.value));

                // 准备保存的数据
                const saveData = {
                    name: selectedGame.value,
                    rounds: totalRounds.value,
                    roundModes: currentRoundModes
                };

                console.log('保存数据:', saveData);
                console.log('当前roundModes状态:', JSON.stringify(currentRoundModes, null, 2));

                const response = await fetchWithAuth('/api/zombie-spawn', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(saveData)
                });

                const data = await response.json();

                if (data && data.success) {
                    alert('保存成功！');
                    console.log('保存成功，服务器响应:', data);
                } else {
                    alert('保存失败: ' + (data.message || '未知错误'));
                    console.error('保存失败，服务器响应:', data);
                }
            } catch (error) {
                console.error('保存出怪设置出错:', error);
                alert('保存出错: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // 返回主界面
        const backToMain = () => {
            window.location.href = 'index.html';
        };

        // 退出登录
        const logout = () => {
            localStorage.removeItem('auth_token');
            window.location.href = 'login.html';
        };

        // 初始化
        onMounted(() => {
            loadGames();
            // 加载用户预设
            loadUserPresets();
        });

        return {
            loading,
            loadingMessage,
            games,
            selectedGame,
            totalRounds,
            selectedRound,
            spawnPoints,
            spawnPointsData,
            expandedSpawnPoints,
            batchSettings,
            presetManager,
            MONSTER_PRESETS,
            USER_PRESETS,
            loadGames,
            loadGameSpawnSettings,
            updateRounds,
            selectRound,
            toggleSpawnPoint,
            getSpawnPointConfigs,
            addMonsterConfig,
            removeMonsterConfig,
            applyBatchSettings,
            getMonsterPresets,
            getMonsterName,
            getSpawnPointInfo,
            formatCoordinates,
            loadUserPresets,
            openApplyPresetModal,
            applyPresetToRound,
            saveSpawnSettings,
            backToMain,
            logout
        };
    }
}).mount('#app');
