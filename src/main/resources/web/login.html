<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器 - 登录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        /* 花体字母样式 */
        .fancy-letter {
            font-family: 'Times New Roman', Times, serif;
            font-weight: bold;
            font-size: 3em;
            color: #4f46e5;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            padding: 0 5px;
        }
        .login-header h2 {
            margin-top: 10px;
            color: #343a40;
        }
        .login-form {
            margin-bottom: 20px;
        }
        .login-footer {
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载僵尸末日网页编辑器</div>
    </div>

    <div class="login-container animate-fade-in">
        <div class="login-header">
            <span class="fancy-letter">DZ</span>
            <h2>僵尸末日网页编辑器</h2>
            <p class="text-muted">请输入管理员密码登录</p>
        </div>

        <div class="login-form">
            <div class="alert alert-danger d-none" id="login-error">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <span id="error-message">密码错误，请重试</span>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">管理员密码</label>
                <div class="position-relative">
                    <input type="password" class="form-control" id="password" placeholder="请输入密码" required autocomplete="off" data-lpignore="true">
                    <!-- 移除眼睛按钮，只使用JavaScript切换密码可见性 -->
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary" id="login-btn">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </button>
            </div>
        </div>

        <div class="login-footer">
            <p>DeathZombieV4 Web管理界面</p>
        </div>
    </div>

    <script>
        // 隐藏加载动画
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.getElementById('loading-overlay').style.display = 'none';
            }, 500);
        });

        // 移除密码切换功能，因为浏览器已经提供了这个功能
        // 如果需要，可以在这里添加其他初始化代码

        // 登录按钮点击事件
        document.getElementById('login-btn').addEventListener('click', login);

        // 按下回车键也触发登录
        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });

        // 登录函数
        function login() {
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            const errorAlert = document.getElementById('login-error');
            const errorMessage = document.getElementById('error-message');

            // 禁用登录按钮并显示加载状态
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>登录中...';

            // 隐藏错误信息
            errorAlert.classList.add('d-none');

            // 发送登录请求
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ password: password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 登录成功，保存令牌并跳转到主页
                    localStorage.setItem('auth_token', data.token);
                    window.location.href = 'index.html';
                } else {
                    // 登录失败，显示错误信息
                    errorMessage.textContent = data.message || '登录失败，请重试';
                    errorAlert.classList.remove('d-none');

                    // 恢复登录按钮
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录';
                }
            })
            .catch(error => {
                // 网络错误
                errorMessage.textContent = '网络错误，请稍后重试';
                errorAlert.classList.remove('d-none');

                // 恢复登录按钮
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录';

                console.error('登录请求失败:', error);
            });
        }
    </script>
</body>
</html>
