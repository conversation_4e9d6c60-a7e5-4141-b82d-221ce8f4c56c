body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #ffffff;
    color: #333;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1, h2, h3 {
    color: #8b0000;
    text-align: center;
}

#game-selector {
    margin: 20px 0;
    text-align: center;
}

#game-select {
    padding: 8px;
    margin-right: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

button {
    background-color: #8b0000;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #a52a2a;
}

.hidden {
    display: none;
}

.inventory-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px auto;
    max-width: 400px;
}

.inventory-row {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.slot {
    width: 80px;
    height: 80px;
    background-color: #ddd;
    border: 2px solid #aaa;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: all 0.2s;
}

.slot:hover {
    border-color: #8b0000;
    transform: scale(1.05);
}

.slot.selected {
    border-color: #8b0000;
    background-color: #ffeeee;
}

.item-image {
    max-width: 60px;
    max-height: 60px;
}

.slot-label {
    font-size: 12px;
    margin-top: 4px;
    text-align: center;
}

#item-selector {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin: 15px 0;
}

.item {
    width: 60px;
    height: 60px;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.item:hover {
    border-color: #8b0000;
    transform: scale(1.05);
}

.item img {
    max-width: 50px;
    max-height: 50px;
}

.button-row {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.item-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 100;
    pointer-events: none;
    white-space: nowrap;
}

.slot-with-item {
    position: relative;
}

.slot-with-item::after {
    content: attr(data-item-id);
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
}
