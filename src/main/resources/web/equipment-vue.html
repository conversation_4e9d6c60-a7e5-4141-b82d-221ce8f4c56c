<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器 - 初始装备设置</title>
    <!-- 引入图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- 引入Google字体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap">
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="css/equipment-vue.css">
    <!-- 引入字体重置样式 (必须在最后) -->
    <link rel="stylesheet" href="css/font-reset.css">
</head>
<body>
    <div id="app">
        <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-message">{{ loadingMessage }}</div>
        </div>

        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <span class="fancy-letter">DZ</span>
                        <span>僵尸末日网页编辑器</span>
                    </div>
                </div>

                <div class="sidebar-nav">
                    <div class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="bi bi-house"></i>
                            <span>主页</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="bi bi-shield"></i>
                            <span>初始装备设置</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="monster-preset-vue.html" class="nav-link">
                            <i class="bi bi-collection"></i>
                            <span>怪物预设管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="zombie-spawn-vue.html" class="nav-link">
                            <i class="bi bi-bug"></i>
                            <span>回合出怪设置</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 顶部导航栏 -->
                <div class="top-navbar">
                    <div class="page-title">
                        <i class="bi bi-shield"></i>
                        <span>僵尸末日网页编辑器 - 初始装备设置</span>
                    </div>
                    <div class="navbar-actions">
                        <button class="btn btn-success me-2" @click="saveEquipment">
                            <i class="bi bi-save"></i>
                            <span>保存设置</span>
                        </button>
                        <button class="btn btn-outline-danger" @click="logout">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </button>
                    </div>
                </div>

                <!-- 游戏选择 -->
                <div class="dashboard-card animate-slide-up">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-list-ul me-2"></i>选择游戏
                        </div>
                    </div>
                    <div class="card-body">
                        <select class="form-select" v-model="selectedGame" @change="loadGameEquipment">
                            <option value="">-- 请选择游戏 --</option>
                            <option v-for="game in games" :key="game" :value="game">{{ game }}</option>
                        </select>
                    </div>
                </div>

                <!-- 装备序列参考卡片 -->
                <div v-if="selectedGame" class="dashboard-card animate-slide-up">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-info-circle"></i>
                            <span>初始装备物品序列参考</span>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline" @click="toggleAllSections">
                                <i :class="allSectionsExpanded ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                                {{ allSectionsExpanded ? '全部折叠' : '全部展开' }}
                            </button>
                        </div>
                    </div>
                    <div class="item-sequence-container">
                        <!-- 武器类部分 -->
                        <div class="sequence-section">
                            <div class="sequence-header" @click="toggleSection('weapons')">
                                <h3 class="sequence-title">武器类 (id1~id24)</h3>
                                <i :class="sectionExpanded.weapons ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            </div>
                            <div class="sequence-grid" v-if="sectionExpanded.weapons">
                                <div v-for="i in 24" :key="`wp-${i}`" class="sequence-item">
                                    <div class="sequence-item-id">id{{ i }}</div>
                                    <div class="sequence-item-content">
                                        <img
                                            :src="getItemImageUrl(`wp_id${i}`)"
                                            :alt="`武器 ${i}`"
                                            @error="handleImageError"
                                            draggable="true"
                                            @dragstart="dragReferenceItem($event, `id${i}`)"
                                            @dragend="dragEnd"
                                        >
                                        <span>{{ getItemName('wp', i) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 护甲类部分 -->
                        <div class="sequence-section">
                            <div class="sequence-header" @click="toggleSection('armor')">
                                <h3 class="sequence-title">护甲类 (id25~id37)</h3>
                                <i :class="sectionExpanded.armor ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            </div>
                            <div class="sequence-grid" v-if="sectionExpanded.armor">
                                <div v-for="i in 12" :key="`ar-${i}`" class="sequence-item">
                                    <div class="sequence-item-id">id{{ i + 24 }}</div>
                                    <div class="sequence-item-content">
                                        <img
                                            :src="getItemImageUrl(`ar_id${i}`)"
                                            :alt="`护甲 ${i}`"
                                            @error="handleImageError"
                                            draggable="true"
                                            @dragstart="dragReferenceItem($event, `id${i + 24}`)"
                                            @dragend="dragEnd"
                                        >
                                        <span>{{ getItemName('ar', i) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 物品类部分 -->
                        <div class="sequence-section">
                            <div class="sequence-header" @click="toggleSection('items')">
                                <h3 class="sequence-title">物品类 (id38~id67)</h3>
                                <i :class="sectionExpanded.items ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            </div>
                            <div class="sequence-grid" v-if="sectionExpanded.items">
                                <div v-for="i in 29" :key="`it-${i}`" class="sequence-item">
                                    <div class="sequence-item-id">id{{ i + 37 }}</div>
                                    <div class="sequence-item-content">
                                        <img
                                            :src="getItemImageUrl(`it_id${i}`)"
                                            :alt="`物品 ${i}`"
                                            @error="handleImageError"
                                            draggable="true"
                                            @dragstart="dragReferenceItem($event, `id${i + 37}`)"
                                            @dragend="dragEnd"
                                        >
                                        <span>{{ getItemName('it', i) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 护甲设置 -->
                <div v-if="selectedGame" class="dashboard-card animate-slide-up">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-shield"></i>
                            <span>{{ selectedGame }} - 初始护甲设置</span>
                        </div>
                    </div>
                    <div class="armor-settings-container">
                        <div class="armor-info">
                            <i class="bi bi-info-circle"></i>
                            <span>护甲类型的装备会在游戏开始时直接穿戴在玩家身上，不占用物品栏空间</span>
                        </div>
                        <div class="armor-slots">
                            <div class="armor-slot-group">
                                <div class="armor-slot-label">上套装</div>
                                <div
                                    class="armor-slot"
                                    :class="{'has-item': armorEquipment.upper}"
                                    @click="selectArmorSlot('upper')"
                                    @dragover.prevent
                                    @dragenter.prevent
                                    @drop.prevent="dropArmorItem($event, 'upper')"
                                >
                                    <img
                                        v-if="armorEquipment.upper"
                                        :src="getItemImageUrl(armorEquipment.upper)"
                                        :alt="armorEquipment.upper"
                                        @error="handleImageError"
                                    >
                                    <div v-if="armorEquipment.upper" class="slot-item-tooltip">{{ getItemDisplayName(armorEquipment.upper) }}</div>
                                    <button
                                        v-if="armorEquipment.upper"
                                        class="clear-armor-btn"
                                        @click.stop="clearArmorSlot('upper')"
                                        title="清除上套装"
                                    >
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="armor-slot-group">
                                <div class="armor-slot-label">下套装</div>
                                <div
                                    class="armor-slot"
                                    :class="{'has-item': armorEquipment.lower}"
                                    @click="selectArmorSlot('lower')"
                                    @dragover.prevent
                                    @dragenter.prevent
                                    @drop.prevent="dropArmorItem($event, 'lower')"
                                >
                                    <img
                                        v-if="armorEquipment.lower"
                                        :src="getItemImageUrl(armorEquipment.lower)"
                                        :alt="armorEquipment.lower"
                                        @error="handleImageError"
                                    >
                                    <div v-if="armorEquipment.lower" class="slot-item-tooltip">{{ getItemDisplayName(armorEquipment.lower) }}</div>
                                    <button
                                        v-if="armorEquipment.lower"
                                        class="clear-armor-btn"
                                        @click.stop="clearArmorSlot('lower')"
                                        title="清除下套装"
                                    >
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物品栏 -->
                <div v-if="selectedGame" class="dashboard-card animate-slide-up">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-grid-3x3"></i>
                            <span>{{ selectedGame }} - 初始装备设置</span>
                        </div>
                    </div>
                    <div class="inventory-container">
                        <div class="inventory-grid">
                            <div
                                v-for="i in 36"
                                :key="i"
                                class="inventory-slot"
                                :class="{
                                    'selected': selectedSlot === i,
                                    'hotbar': i <= 9,
                                    'has-item': equipment[i]
                                }"
                                @click="selectSlot(i)"
                                @dragover.prevent
                                @dragenter.prevent="highlightSlot(i)"
                                @dragleave="unhighlightSlot(i)"
                                @drop.prevent="dropItem($event, i)"
                                :data-slot-index="i"
                            >
                                <span class="slot-number">{{ i }}</span>
                                <img
                                    v-if="equipment[i]"
                                    :src="getItemImageUrl(equipment[i])"
                                    :alt="equipment[i]"
                                    @error="handleImageError"
                                    draggable="true"
                                    @dragstart="dragItem($event, i, equipment[i])"
                                    @dragend="dragEnd"
                                    class="draggable-item"
                                >
                                <img v-else-if="i > 1 && i <= 9" :src="getItemImageUrl('LIGHT_GRAY_DYE')" alt="LIGHT_GRAY_DYE" @error="handleImageError">
                                <div v-if="equipment[i]" class="slot-item-tooltip">{{ getItemDisplayName(equipment[i]) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物品选择器 -->
                <div v-if="selectedSlot !== null" class="dashboard-card animate-slide-up item-selector-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="bi bi-gear"></i>
                            <span>设置槽位 {{ selectedSlot }}</span>
                        </div>
                        <button class="btn btn-danger" @click="clearSlot">
                            <i class="bi bi-trash"></i>
                            <span>清除槽位</span>
                        </button>
                    </div>
                    <div class="item-search">
                        <div class="search-input">
                            <i class="bi bi-search"></i>
                            <input
                                type="text"
                                placeholder="搜索物品..."
                                v-model="searchQuery"
                                ref="searchInput"
                                @focus="scrollToItemSelector"
                            >
                        </div>
                        <div class="search-filters">
                            <div class="filter-group">
                                <label>物品类型:</label>
                                <select v-model="filterType">
                                    <option value="">全部类型</option>
                                    <option value="weapon">武器</option>
                                    <option value="armor">护甲</option>
                                    <option value="item">物品</option>
                                    <option value="ammo">弹药</option>
                                    <option value="special">特殊槽位</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>ID范围:</label>
                                <select v-model="filterIdRange">
                                    <option value="">全部ID</option>
                                    <option value="1-24">武器 (id1-id24)</option>
                                    <option value="25-37">护甲 (id25-id37)</option>
                                    <option value="38-66">物品 (id38-id66)</option>
                                    <option value="67-70">特殊槽位 (id67-id70)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="item-selector">
                        <div class="item-grid">
                            <template v-for="item in filteredItems" :key="item.id">
                                <!-- 分类标题 -->
                                <div v-if="item.isCategory" class="item-category">
                                    {{ item.name }}
                                </div>
                                <!-- 物品选项 -->
                                <div
                                    v-else
                                    class="item-option"
                                    @click="selectItem(item.id)"
                                    draggable="true"
                                    @dragstart="dragNewItem($event, item.id)"
                                    @dragend="dragEnd"
                                >
                                    <img :src="getItemImageUrl(item.id)" :alt="item.name" @error="handleImageError">
                                    <div class="item-option-details">
                                        <div class="item-name">{{ item.name }}</div>
                                        <div class="item-id">{{ item.id }}</div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- 引入Bootstrap和Vue应用脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/equipment-vue.js"></script>
</body>
</html>
