# ========================================
# IDZ自定义怪物配置文件
# 版本: 1.0
# 此文件存储用户创建的所有IDZ系列怪物配置
# ========================================

# 配置文件版本
version: "1.0"

# 调试模式（启用后会输出详细日志）
debug_mode: false

# IDZ怪物配置说明
# 每个IDZ怪物都有以下配置结构：
# monsters:
#   idz_monster_id:
#     monster_id: "idz_monster_id"           # 怪物唯一ID（必须以idz开头）
#     display_name: "怪物显示名称"            # 游戏中显示的名称
#     description: "怪物描述"                # 怪物描述信息
#     entity_type: "ZOMBIE"                  # 基础实体类型
#     
#     # 基础属性配置
#     attributes:
#       health: 20.0                         # 生命值
#       movement_speed: 0.23                 # 移动速度
#       attack_damage: 3.0                   # 攻击伤害
#       armor: 0.0                          # 护甲值
#       armor_toughness: 0.0                # 护甲韧性
#       knockback_resistance: 0.0           # 击退抗性
#     
#     # 装备配置
#     equipment:
#       main_hand: "NONE"                    # 主手武器
#       off_hand: "NONE"                     # 副手物品
#       helmet: "NONE"                       # 头盔
#       chestplate: "NONE"                   # 胸甲
#       leggings: "NONE"                     # 护腿
#       boots: "NONE"                        # 靴子
#     
#     # 药水效果配置
#     potion_effects:
#       effect_0:
#         type: "SPEED"                      # 效果类型
#         duration: 600                      # 持续时间（tick）
#         amplifier: 0                       # 效果等级（0=1级）
#         ambient: false                     # 是否为环境效果
#         particles: true                    # 是否显示粒子
#     
#     # 技能配置
#     skills:
#       skill_ids:                           # 技能ID列表
#         - "poison_attack"
#         - "lightning_strike"
#       parameters:                          # 技能参数配置
#         poison_duration: 60
#         lightning_damage: 8.0
#     
#     # 粒子特效配置
#     particles:
#       type: "NONE"                         # 粒子类型
#       count: 10                            # 粒子数量
#       range: 2.0                           # 粒子范围
#       interval: 20                         # 粒子间隔（tick）
#     
#     # 生成配置
#     spawn:
#       natural_spawn: false                 # 是否自然生成
#       spawn_chance: 0.0                    # 生成概率
#       biomes: []                           # 生成生物群系
#     
#     # 行为配置
#     behavior:
#       hostile: true                        # 是否敌对
#       follow_range: 35.0                   # 跟随范围
#       can_pickup_items: false              # 是否可以拾取物品
#       can_break_doors: false               # 是否可以破坏门
#       sunlight_burns: false                # 是否在阳光下燃烧
#     
#     # 时间戳
#     timestamps:
#       created: 1640995200000               # 创建时间
#       last_modified: 1640995200000         # 最后修改时间

# IDZ怪物存储区域
monsters: {}

# ========================================
# 技能模板库配置
# ========================================
skill_templates:
  # 从现有ID系列怪物中提取的技能
  id_series_skills:
    poison_attack:
      name: "剧毒攻击"
      description: "攻击时给予目标剧毒效果"
      source: "id5"
      parameters:
        poison_level: 1
        poison_duration: 60
        poison_chance: 1.0
    
    summon_zombie:
      name: "召唤僵尸"
      description: "定期召唤普通僵尸"
      source: "id6"
      parameters:
        summon_interval: 6000
        summon_count: 1
        summon_range: 5.0
    
    arrow_attack:
      name: "箭矢攻击"
      description: "远程发射箭矢攻击"
      source: "id7"
      parameters:
        arrow_damage: 4.0
        arrow_speed: 1.6
        attack_range: 16.0
    
    explosion_death:
      name: "死亡爆炸"
      description: "死亡时产生爆炸"
      source: "id11"
      parameters:
        explosion_power: 3.0
        explosion_fire: false
        explosion_damage_blocks: false
    
    lightning_attack:
      name: "雷电攻击"
      description: "召唤雷电攻击目标"
      source: "id13"
      parameters:
        lightning_damage: 8.0
        lightning_range: 10.0
        lightning_interval: 100
    
    freeze_attack:
      name: "冰冻攻击"
      description: "攻击时给予目标缓慢效果"
      source: "id14"
      parameters:
        slowness_level: 2
        slowness_duration: 100
        freeze_chance: 0.8
    
    invisibility:
      name: "隐身能力"
      description: "定期进入隐身状态"
      source: "id15"
      parameters:
        invisibility_duration: 200
        invisibility_interval: 1200
        reveal_on_attack: true

  # 从现有IDC系列怪物中提取的技能
  idc_series_skills:
    fire_immunity:
      name: "火焰免疫"
      description: "免疫火焰伤害"
      source: "idc3"
      parameters:
        fire_resistance: true
        lava_immunity: true
    
    explosion_immunity:
      name: "爆炸免疫"
      description: "免疫爆炸伤害"
      source: "idc4"
      parameters:
        explosion_resistance: 1.0
    
    teleport_attack:
      name: "瞬移攻击"
      description: "瞬移到目标身边进行攻击"
      source: "idc5"
      parameters:
        teleport_range: 16.0
        teleport_interval: 200
        teleport_damage_bonus: 2.0
    
    web_trap:
      name: "蛛网陷阱"
      description: "在目标周围放置蛛网"
      source: "idc6"
      parameters:
        web_range: 3.0
        web_duration: 100
        web_interval: 300
    
    shield_bash:
      name: "盾击攻击"
      description: "使用盾牌进行强力攻击"
      source: "idc7"
      parameters:
        bash_damage: 6.0
        bash_knockback: 2.0
        bash_cooldown: 100
    
    magic_missile:
      name: "魔法飞弹"
      description: "发射魔法飞弹攻击"
      source: "idc8"
      parameters:
        missile_damage: 5.0
        missile_speed: 2.0
        missile_count: 3
    
    charge_attack:
      name: "冲锋攻击"
      description: "快速冲向目标进行攻击"
      source: "idc9"
      parameters:
        charge_speed: 2.0
        charge_damage: 8.0
        charge_range: 12.0

# ========================================
# GUI界面配置
# ========================================
gui_settings:
  # 主编辑界面配置
  main_editor:
    title: "§6IDZ怪物编辑器"
    size: 54
    
  # 属性编辑界面配置
  attribute_editor:
    title: "§6属性编辑器"
    size: 27
    
  # 技能选择界面配置
  skill_selector:
    title: "§6技能选择器"
    size: 54
    items_per_page: 45
    
  # 装备编辑界面配置
  equipment_editor:
    title: "§6装备编辑器"
    size: 27

# ========================================
# 系统配置
# ========================================
system_settings:
  # 最大IDZ怪物数量限制
  max_monsters: 100
  
  # 自动保存间隔（秒）
  auto_save_interval: 300
  
  # 配置文件备份
  backup_enabled: true
  backup_interval: 3600
  max_backups: 5
  
  # 性能优化
  cache_enabled: true
  cache_size: 50
  
  # 安全设置
  validate_configs: true
  sanitize_names: true
  max_name_length: 32
