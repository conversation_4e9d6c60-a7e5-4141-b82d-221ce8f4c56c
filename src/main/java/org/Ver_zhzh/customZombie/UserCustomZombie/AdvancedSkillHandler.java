package org.Ver_zhzh.customZombie.UserCustomZombie;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Arrow;
import org.bukkit.entity.Creeper;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.List;
import org.Ver_zhzh.customZombie.CustomZombie;

/**
 * 高级技能处理器
 * 专门处理ID11-ID13等复杂技能的僵尸
 */
public class AdvancedSkillHandler {
    
    private final Plugin plugin;
    private final Logger logger;
    private final CustomZombie originalCustomZombie;
    private final boolean debugMode;
    
    // 任务管理
    private final Map<Zombie, BukkitTask> explosionTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> poisonArrowTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> electricTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> freezingTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> shadowTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> lightningTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> scientistTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> scientistGlobalDamageTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mageLightningTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mageEffectTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mageSummonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mageParticleTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> balloonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> fogTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantThunderGlobalLightningTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantThunderTeleportTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantThunderSummonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantThunderTimeSlowTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantThunderFreezeTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> ultimateDestructionSummonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> ultimateDestructionAuraTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> ultimateDestructionExplosionTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> ultimateDestructionParticleTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantShadowTeleportTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantShadowSummonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> mutantShadowDebuffTasks = new HashMap<>();

    // 变异科学家伤害追踪
    private final Map<Zombie, Double> scientistDamageReceived = new HashMap<>();

    // 变异博士技能处理器
    private MutantDoctorSkillHandler mutantDoctorSkillHandler;
    
    public AdvancedSkillHandler(Plugin plugin, Logger logger, CustomZombie originalCustomZombie, boolean debugMode) {
        this.plugin = plugin;
        this.logger = logger;
        this.originalCustomZombie = originalCustomZombie;
        this.debugMode = debugMode;

        // 初始化变异博士技能处理器
        this.mutantDoctorSkillHandler = new MutantDoctorSkillHandler(plugin, logger, originalCustomZombie, debugMode);
    }
    
    /**
     * 启用自爆僵尸技能（ID11）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableExplosionZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加自爆僵尸标记
            zombie.setMetadata("explosionZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查自爆僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int detectionRange = 5; // 默认检测范围5格
            int explosionDelay = 40; // 默认2秒延迟
            double explosionDamage = 10.0; // 默认10点伤害
            double explosionRange = 5.0; // 默认爆炸范围5格
            boolean explosionEnabled = true; // 默认启用

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    detectionRange = config.skillCooldownOverrides.getOrDefault("detection_range", detectionRange);
                    explosionDelay = config.skillCooldownOverrides.getOrDefault("explosion_delay", explosionDelay);
                }
                
                if (config.specialAbilities != null) {
                    Object damageObj = config.specialAbilities.get("explosion_damage");
                    if (damageObj instanceof Number) {
                        explosionDamage = ((Number) damageObj).doubleValue();
                    }
                    Object rangeObj = config.specialAbilities.get("explosion_range");
                    if (rangeObj instanceof Number) {
                        explosionRange = ((Number) rangeObj).doubleValue();
                    }
                    Object enabledObj = config.specialAbilities.get("explosion_enabled");
                    if (enabledObj instanceof Boolean) {
                        explosionEnabled = (Boolean) enabledObj;
                    }
                }
            }

            if (!explosionEnabled) {
                if (debugMode) {
                    logger.info("自爆功能已禁用");
                }
                return;
            }

            // 设置配置元数据
            zombie.setMetadata("explosion_detection_range", new FixedMetadataValue(plugin, detectionRange));
            zombie.setMetadata("explosion_delay", new FixedMetadataValue(plugin, explosionDelay));
            zombie.setMetadata("explosion_damage", new FixedMetadataValue(plugin, explosionDamage));
            zombie.setMetadata("explosion_range", new FixedMetadataValue(plugin, explosionRange));

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义自爆任务
                startCustomExplosionTask(zombie, detectionRange, explosionDelay, explosionDamage, explosionRange);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startExplosionMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startExplosionZombieTasks", Zombie.class);
                    startExplosionMethod.setAccessible(true);
                    startExplosionMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有自爆技能失败，使用自定义实现: " + e.getMessage());
                    startCustomExplosionTask(zombie, detectionRange, explosionDelay, explosionDamage, explosionRange);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用自爆僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用自爆僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义自爆任务
     */
    private void startCustomExplosionTask(Zombie zombie, int detectionRange, int explosionDelay, double explosionDamage, double explosionRange) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    explosionTasks.remove(zombie);
                    return;
                }

                // 检查指定范围内是否有玩家
                for (Entity entity : zombie.getNearbyEntities(detectionRange, detectionRange, detectionRange)) {
                    if (entity instanceof Player) {
                        // 有玩家在附近，准备自爆

                        // 播放TNT点燃音效
                        zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_TNT_PRIMED, 1.0f, 1.0f);

                        // 显示自爆提示 (红色粒子)
                        Location loc = zombie.getLocation().add(0, 1, 0);
                        zombie.getWorld().spawnParticle(Particle.FLAME, loc, 20, 1.0, 0.5, 1.0, 0.1);

                        // 延迟后自爆
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (zombie == null || zombie.isDead()) {
                                    return;
                                }

                                // 获取爆炸位置
                                Location explodeLocation = zombie.getLocation();

                                // 移除僵尸
                                zombie.remove();
                                explosionTasks.remove(zombie);
                                cancel();

                                // 不破坏方块的爆炸效果
                                explodeLocation.getWorld().createExplosion(explodeLocation, 0F, false, false);

                                // 对附近玩家造成配置的伤害
                                for (Entity nearby : explodeLocation.getWorld().getNearbyEntities(explodeLocation, explosionRange, explosionRange, explosionRange)) {
                                    if (nearby instanceof Player) {
                                        ((Player) nearby).damage(explosionDamage);
                                    }
                                }

                                // 显示爆炸粒子效果
                                explodeLocation.getWorld().spawnParticle(Particle.EXPLOSION, explodeLocation, 50, 2.0, 2.0, 2.0, 0.1);
                            }
                        }.runTaskLater(plugin, explosionDelay);

                        // 一旦进入自爆程序，就取消当前任务
                        cancel();
                        explosionTasks.remove(zombie);
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 20, 20); // 1秒后开始，每秒检查一次

        explosionTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义自爆任务，检测范围: " + detectionRange + "，延迟: " + explosionDelay + " ticks");
        }
    }

    /**
     * 启用毒箭僵尸技能（ID12）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enablePoisonArrowZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加毒箭僵尸标记
            zombie.setMetadata("poisonArrowZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查毒箭僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int shootingInterval = 100; // 默认5秒间隔
            int arrowCount = 3; // 默认3支箭
            int targetRange = 16; // 默认16格目标范围
            int arrowDespawnTime = 200; // 默认10秒消失
            int poisonDuration = 100; // 默认5秒毒性
            int poisonLevel = 1; // 默认2级毒性
            boolean shootingEnabled = true; // 默认启用

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    shootingInterval = config.skillCooldownOverrides.getOrDefault("shooting_interval", shootingInterval);
                    arrowCount = config.skillCooldownOverrides.getOrDefault("arrow_count", arrowCount);
                    targetRange = config.skillCooldownOverrides.getOrDefault("target_range", targetRange);
                    arrowDespawnTime = config.skillCooldownOverrides.getOrDefault("arrow_despawn_time", arrowDespawnTime);
                }
                
                if (config.specialAbilities != null) {
                    Object enabledObj = config.specialAbilities.get("shooting_enabled");
                    if (enabledObj instanceof Boolean) {
                        shootingEnabled = (Boolean) enabledObj;
                    }
                    Object poisonDurObj = config.specialAbilities.get("poison_arrow_duration");
                    if (poisonDurObj instanceof Number) {
                        poisonDuration = ((Number) poisonDurObj).intValue();
                    }
                    Object poisonLvlObj = config.specialAbilities.get("poison_arrow_level");
                    if (poisonLvlObj instanceof Number) {
                        poisonLevel = ((Number) poisonLvlObj).intValue();
                    }
                }
            }

            if (!shootingEnabled) {
                if (debugMode) {
                    logger.info("毒箭射击功能已禁用");
                }
                return;
            }

            // 设置毒性参数元数据
            zombie.setMetadata("poison_arrow_duration", new FixedMetadataValue(plugin, poisonDuration));
            zombie.setMetadata("poison_arrow_level", new FixedMetadataValue(plugin, poisonLevel));

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义毒箭任务
                startCustomPoisonArrowTask(zombie, shootingInterval, arrowCount, targetRange, arrowDespawnTime);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startPoisonArrowMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startShootingPoisonArrows", Zombie.class);
                    startPoisonArrowMethod.setAccessible(true);
                    startPoisonArrowMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有毒箭技能失败，使用自定义实现: " + e.getMessage());
                    startCustomPoisonArrowTask(zombie, shootingInterval, arrowCount, targetRange, arrowDespawnTime);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用毒箭僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用毒箭僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义毒箭任务
     */
    private void startCustomPoisonArrowTask(Zombie zombie, int shootingInterval, int arrowCount, int targetRange, int arrowDespawnTime) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    poisonArrowTasks.remove(zombie);
                    return;
                }

                // 查找指定范围内最近的玩家
                Player target = null;
                double closestDistance = targetRange + 1.0;

                for (Entity entity : zombie.getNearbyEntities(targetRange, targetRange, targetRange)) {
                    if (entity instanceof Player) {
                        double distance = zombie.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 让僵尸面向目标玩家
                    zombie.teleport(zombie.getLocation().setDirection(
                            target.getLocation().subtract(zombie.getLocation()).toVector()));

                    // 发射配置数量的毒箭
                    for (int i = 0; i < arrowCount; i++) {
                        // 延迟发射，使箭矢不重叠
                        final int index = i;
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (zombie == null || zombie.isDead()) {
                                    return;
                                }

                                // 发射箭矢
                                Arrow arrow = zombie.launchProjectile(Arrow.class);
                                arrow.setVelocity(zombie.getLocation().getDirection().multiply(1.5));
                                arrow.setMetadata("poisonZombieArrow", new FixedMetadataValue(plugin, true));

                                // 设置箭矢按配置时间后消失
                                new BukkitRunnable() {
                                    @Override
                                    public void run() {
                                        if (arrow != null && !arrow.isDead()) {
                                            arrow.remove();
                                        }
                                    }
                                }.runTaskLater(plugin, arrowDespawnTime);
                            }
                        }.runTaskLater(plugin, index * 5); // 每支箭间隔5刻
                    }

                    // 播放射箭音效
                    zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 1.0f);

                    if (debugMode) {
                        logger.info("毒箭僵尸发射了 " + arrowCount + " 支毒箭");
                    }
                }
            }
        }.runTaskTimer(plugin, 60, shootingInterval); // 3秒后开始，按配置间隔执行

        poisonArrowTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义毒箭任务，间隔: " + shootingInterval + " ticks，箭数: " + arrowCount);
        }
    }

    /**
     * 启用电击僵尸技能（ID13）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableElectricZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加电击僵尸标记
            zombie.setMetadata("electricZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查电击僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int electricInterval = 40; // 默认2秒间隔
            double electricDamage = 1.0; // 默认1点伤害
            int electricRange = 5; // 默认5格范围
            boolean electricEnabled = true; // 默认启用

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    electricInterval = config.skillCooldownOverrides.getOrDefault("electric_interval", electricInterval);
                    electricRange = config.skillCooldownOverrides.getOrDefault("electric_range", electricRange);
                }

                if (config.specialAbilities != null) {
                    Object damageObj = config.specialAbilities.get("electric_damage");
                    if (damageObj instanceof Number) {
                        electricDamage = ((Number) damageObj).doubleValue();
                    }
                    Object enabledObj = config.specialAbilities.get("electric_enabled");
                    if (enabledObj instanceof Boolean) {
                        electricEnabled = (Boolean) enabledObj;
                    }
                }
            }

            if (!electricEnabled) {
                if (debugMode) {
                    logger.info("电击功能已禁用");
                }
                return;
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义电击任务
                startCustomElectricTask(zombie, electricInterval, electricDamage, electricRange);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startElectricMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startElectricShockTasks", Zombie.class);
                    startElectricMethod.setAccessible(true);
                    startElectricMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有电击技能失败，使用自定义实现: " + e.getMessage());
                    startCustomElectricTask(zombie, electricInterval, electricDamage, electricRange);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用电击僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用电击僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义电击任务
     */
    private void startCustomElectricTask(Zombie zombie, int electricInterval, double electricDamage, int electricRange) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (!zombie.isValid() || zombie.isDead()) {
                    this.cancel();
                    electricTasks.remove(zombie);
                    return;
                }

                // 在僵尸周围创建电击粒子效果
                Location location = zombie.getLocation().add(0, 1, 0);
                zombie.getWorld().spawnParticle(Particle.CRIT, location, 10, 0.5, 0.5, 0.5, 0.1);

                // 给指定范围内玩家造成伤害并产生特效
                for (Entity entity : zombie.getNearbyEntities(electricRange, electricRange, electricRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 造成闪电效果
                        Location playerLoc = player.getLocation();
                        player.getWorld().spawnParticle(Particle.WITCH, playerLoc, 10, 0.5, 0.5, 0.5, 0.1);

                        // 造成配置的伤害
                        player.damage(electricDamage, zombie);

                        // 播放电击音效
                        player.getWorld().playSound(playerLoc, Sound.ENTITY_CREEPER_HURT, 0.5f, 1.0f);

                        if (debugMode) {
                            logger.info("电击僵尸对玩家 " + player.getName() + " 造成了 " + electricDamage + " 点电击伤害");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 20, electricInterval); // 1秒后开始，按配置间隔执行

        electricTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义电击任务，间隔: " + electricInterval + " ticks，伤害: " + electricDamage);
        }
    }

    /**
     * 启用冰冻僵尸技能（ID14）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableFreezingZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加冰冻僵尸标记
            zombie.setMetadata("freezingZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查冰冻僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int freezingInterval = 40; // 默认2秒间隔
            int freezingRange = 6; // 默认6格范围
            int slownessDuration = 60; // 默认3秒缓慢
            int slownessLevel = 1; // 默认2级缓慢
            boolean freezingEnabled = true; // 默认启用

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    freezingInterval = config.skillCooldownOverrides.getOrDefault("freezing_interval", freezingInterval);
                    freezingRange = config.skillCooldownOverrides.getOrDefault("freezing_range", freezingRange);
                    slownessDuration = config.skillCooldownOverrides.getOrDefault("slowness_duration", slownessDuration);
                }

                if (config.specialAbilities != null) {
                    Object enabledObj = config.specialAbilities.get("freezing_enabled");
                    if (enabledObj instanceof Boolean) {
                        freezingEnabled = (Boolean) enabledObj;
                    }
                    Object slownessLvlObj = config.specialAbilities.get("slowness_level");
                    if (slownessLvlObj instanceof Number) {
                        slownessLevel = ((Number) slownessLvlObj).intValue();
                    }
                }
            }

            if (!freezingEnabled) {
                if (debugMode) {
                    logger.info("冰冻功能已禁用");
                }
                return;
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义冰冻任务
                startCustomFreezingTask(zombie, freezingInterval, freezingRange, slownessDuration, slownessLevel);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startFreezingMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startFreezingEffectTasks", Zombie.class);
                    startFreezingMethod.setAccessible(true);
                    startFreezingMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有冰冻技能失败，使用自定义实现: " + e.getMessage());
                    startCustomFreezingTask(zombie, freezingInterval, freezingRange, slownessDuration, slownessLevel);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用冰冻僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用冰冻僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义冰冻任务
     */
    private void startCustomFreezingTask(Zombie zombie, int freezingInterval, int freezingRange, int slownessDuration, int slownessLevel) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    freezingTasks.remove(zombie);
                    return;
                }

                // 在僵尸周围创建冰冻粒子效果
                Location location = zombie.getLocation().add(0, 1, 0);
                zombie.getWorld().spawnParticle(Particle.CLOUD, location, 10, 0.5, 0.5, 0.5, 0.05);

                // 给指定范围内玩家添加缓慢和挖掘疲劳效果
                for (Entity entity : zombie.getNearbyEntities(freezingRange, freezingRange, freezingRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 添加效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, slownessLevel));
                        player.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, slownessDuration, 0));

                        // 在玩家周围创建冰冻粒子
                        Location playerLoc = player.getLocation();
                        player.getWorld().spawnParticle(Particle.CLOUD, playerLoc, 10, 0.3, 0.3, 0.3, 0.01);

                        // 播放音效
                        player.getWorld().playSound(playerLoc, Sound.BLOCK_GLASS_BREAK, 0.5f, 1.5f);

                        if (debugMode) {
                            logger.info("冰冻僵尸对玩家 " + player.getName() + " 施加了冰冻效果");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 20, freezingInterval); // 1秒后开始，按配置间隔执行

        freezingTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义冰冻任务，间隔: " + freezingInterval + " ticks，范围: " + freezingRange);
        }
    }

    /**
     * 启用暗影僵尸技能（ID15）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableShadowZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加暗影僵尸标记
            zombie.setMetadata("shadowZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查暗影僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int teleportInterval = 100; // 默认5秒间隔
            int targetRange = 15; // 默认15格目标范围
            boolean teleportEnabled = true; // 默认启用
            boolean invisibilityEnabled = true; // 默认启用隐身

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    teleportInterval = config.skillCooldownOverrides.getOrDefault("teleport_interval", teleportInterval);
                    targetRange = config.skillCooldownOverrides.getOrDefault("target_range", targetRange);
                }

                if (config.specialAbilities != null) {
                    Object teleportEnabledObj = config.specialAbilities.get("teleport_enabled");
                    if (teleportEnabledObj instanceof Boolean) {
                        teleportEnabled = (Boolean) teleportEnabledObj;
                    }
                    Object invisibilityEnabledObj = config.specialAbilities.get("invisibility_enabled");
                    if (invisibilityEnabledObj instanceof Boolean) {
                        invisibilityEnabled = (Boolean) invisibilityEnabledObj;
                    }
                }
            }

            // 应用隐身效果
            if (invisibilityEnabled) {
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, Integer.MAX_VALUE, 0));
                if (debugMode) {
                    logger.info("为暗影僵尸应用隐身效果");
                }
            }

            if (!teleportEnabled) {
                if (debugMode) {
                    logger.info("暗影瞬移功能已禁用");
                }
                return;
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义暗影瞬移任务
                startCustomShadowTeleportTask(zombie, teleportInterval, targetRange);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startShadowMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startShadowTeleportTasks", Zombie.class);
                    startShadowMethod.setAccessible(true);
                    startShadowMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有暗影技能失败，使用自定义实现: " + e.getMessage());
                    startCustomShadowTeleportTask(zombie, teleportInterval, targetRange);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用暗影僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用暗影僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义暗影瞬移任务
     */
    private void startCustomShadowTeleportTask(Zombie zombie, int teleportInterval, int targetRange) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    shadowTasks.remove(zombie);
                    return;
                }

                // 查找指定范围内最近的玩家
                Player target = null;
                double closestDistance = targetRange + 1.0;

                for (Entity entity : zombie.getNearbyEntities(targetRange, targetRange, targetRange)) {
                    if (entity instanceof Player) {
                        double distance = zombie.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 瞬移到玩家背后
                    Location targetLoc = target.getLocation();

                    // 计算玩家背后2格的位置
                    Vector direction = targetLoc.getDirection().normalize().multiply(-2);
                    Location behindPlayer = targetLoc.clone().add(direction);

                    // 确保瞬移位置是安全的
                    if (behindPlayer.getBlock().isPassable() && behindPlayer.getBlock().getRelative(0, 1, 0).isPassable()) {
                        // 在原位置显示消失效果
                        zombie.getWorld().spawnParticle(Particle.SMOKE, zombie.getLocation().add(0, 1, 0), 10, 0.3, 0.5, 0.3, 0.05);
                        zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

                        // 瞬移僵尸
                        zombie.teleport(behindPlayer);

                        // 在新位置显示出现效果
                        zombie.getWorld().spawnParticle(Particle.PORTAL, behindPlayer.add(0, 1, 0), 15, 0.3, 0.5, 0.3, 0.1);
                        zombie.getWorld().playSound(behindPlayer, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.2f);

                        if (debugMode) {
                            logger.info("暗影僵尸瞬移到玩家 " + target.getName() + " 背后");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 60, teleportInterval); // 3秒后开始，按配置间隔执行

        shadowTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义暗影瞬移任务，间隔: " + teleportInterval + " ticks，范围: " + targetRange);
        }
    }

    /**
     * 启用雷霆僵尸技能（ID17）
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableLightningZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加雷霆僵尸标记
            zombie.setMetadata("lightningZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查雷霆僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int lightningInterval = 40; // 默认2秒间隔
            int targetRange = 12; // 默认12格目标范围
            double lightningDamage = 6.0; // 默认6点伤害
            double lightningChance = 0.1; // 默认10%概率
            boolean lightningEnabled = true; // 默认启用

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    lightningInterval = config.skillCooldownOverrides.getOrDefault("lightning_interval", lightningInterval);
                    targetRange = config.skillCooldownOverrides.getOrDefault("target_range", targetRange);
                }

                if (config.specialAbilities != null) {
                    Object enabledObj = config.specialAbilities.get("lightning_enabled");
                    if (enabledObj instanceof Boolean) {
                        lightningEnabled = (Boolean) enabledObj;
                    }
                    Object damageObj = config.specialAbilities.get("lightning_damage");
                    if (damageObj instanceof Number) {
                        lightningDamage = ((Number) damageObj).doubleValue();
                    }
                    Object chanceObj = config.specialAbilities.get("lightning_chance");
                    if (chanceObj instanceof Number) {
                        lightningChance = ((Number) chanceObj).doubleValue();
                    }
                }
            }

            if (!lightningEnabled) {
                if (debugMode) {
                    logger.info("雷霆攻击功能已禁用");
                }
                return;
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义雷霆任务
                startCustomLightningTask(zombie, lightningInterval, targetRange, lightningDamage, lightningChance);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startLightningMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startLightningAttackTasks", Zombie.class);
                    startLightningMethod.setAccessible(true);
                    startLightningMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有雷霆技能失败，使用自定义实现: " + e.getMessage());
                    startCustomLightningTask(zombie, lightningInterval, targetRange, lightningDamage, lightningChance);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用雷霆僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用雷霆僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义雷霆任务
     */
    private void startCustomLightningTask(Zombie zombie, int lightningInterval, int targetRange, double lightningDamage, double lightningChance) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    lightningTasks.remove(zombie);
                    return;
                }

                // 查找指定范围内所有玩家
                for (Entity entity : zombie.getNearbyEntities(targetRange, targetRange, targetRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 按配置概率在玩家位置生成闪电
                        if (Math.random() < lightningChance) {
                            // 显示闪电效果
                            player.getWorld().strikeLightningEffect(player.getLocation());

                            // 造成配置的伤害
                            player.damage(lightningDamage, zombie);

                            // 播放音效
                            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                            if (debugMode) {
                                logger.info("雷霆僵尸对玩家 " + player.getName() + " 释放了闪电攻击");
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 40, lightningInterval); // 2秒后开始，按配置间隔执行

        lightningTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义雷霆任务，间隔: " + lightningInterval + " ticks，范围: " + targetRange);
        }
    }

    /**
     * 启用变异科学家技能（ID18）- 最复杂的技能
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableMutantScientistSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加变异科学家标记
            zombie.setMetadata("mutantScientist", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查变异科学家配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int effectInterval = 20; // 默认1秒间隔
            int smokeRange = 10; // 默认10格烟雾范围
            int damageRange = 5; // 默认5格伤害范围
            double smokeDamage = 2.0; // 默认2点伤害
            int nauseaDuration = 60; // 默认3秒恶心
            int slownessDuration = 60; // 默认3秒缓慢
            boolean smokeEnabled = true; // 默认启用烟雾
            boolean nauseaEnabled = true; // 默认启用恶心
            boolean slownessEnabled = true; // 默认启用缓慢

            // 新增的技能参数
            double summonDamageThreshold = 50.0; // 默认50点伤害召唤
            int globalDamageInterval = 400; // 默认20秒间隔
            double globalDamage = 10.0; // 默认10点全局伤害
            boolean summonEnabled = true; // 默认启用召唤
            boolean globalDamageEnabled = true; // 默认启用全局伤害
            boolean deathSummonEnabled = true; // 默认启用死亡召唤
            int deathSummonCount = 4; // 默认召唤4个暗影僵尸
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤
            boolean useUserCustomDeathSummon = true; // 默认使用用户配置版本死亡召唤

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    effectInterval = config.skillCooldownOverrides.getOrDefault("effect_interval", effectInterval);
                    smokeRange = config.skillCooldownOverrides.getOrDefault("smoke_range", smokeRange);
                    damageRange = config.skillCooldownOverrides.getOrDefault("damage_range", damageRange);
                    nauseaDuration = config.skillCooldownOverrides.getOrDefault("nausea_duration", nauseaDuration);
                    slownessDuration = config.skillCooldownOverrides.getOrDefault("slowness_duration", slownessDuration);
                    globalDamageInterval = config.skillCooldownOverrides.getOrDefault("global_damage_interval", globalDamageInterval);
                    deathSummonCount = config.skillCooldownOverrides.getOrDefault("death_summon_count", deathSummonCount);
                }

                if (config.specialAbilities != null) {
                    Object smokeEnabledObj = config.specialAbilities.get("smoke_enabled");
                    if (smokeEnabledObj instanceof Boolean) {
                        smokeEnabled = (Boolean) smokeEnabledObj;
                    }
                    Object nauseaEnabledObj = config.specialAbilities.get("nausea_enabled");
                    if (nauseaEnabledObj instanceof Boolean) {
                        nauseaEnabled = (Boolean) nauseaEnabledObj;
                    }
                    Object slownessEnabledObj = config.specialAbilities.get("slowness_enabled");
                    if (slownessEnabledObj instanceof Boolean) {
                        slownessEnabled = (Boolean) slownessEnabledObj;
                    }
                    Object damageObj = config.specialAbilities.get("smoke_damage");
                    if (damageObj instanceof Number) {
                        smokeDamage = ((Number) damageObj).doubleValue();
                    }

                    // 新增技能参数
                    Object summonThresholdObj = config.specialAbilities.get("summon_damage_threshold");
                    if (summonThresholdObj instanceof Number) {
                        summonDamageThreshold = ((Number) summonThresholdObj).doubleValue();
                    }
                    Object globalDamageObj = config.specialAbilities.get("global_damage");
                    if (globalDamageObj instanceof Number) {
                        globalDamage = ((Number) globalDamageObj).doubleValue();
                    }
                    Object summonEnabledObj = config.specialAbilities.get("summon_enabled");
                    if (summonEnabledObj instanceof Boolean) {
                        summonEnabled = (Boolean) summonEnabledObj;
                    }
                    Object globalDamageEnabledObj = config.specialAbilities.get("global_damage_enabled");
                    if (globalDamageEnabledObj instanceof Boolean) {
                        globalDamageEnabled = (Boolean) globalDamageEnabledObj;
                    }
                    Object deathSummonEnabledObj = config.specialAbilities.get("death_summon_enabled");
                    if (deathSummonEnabledObj instanceof Boolean) {
                        deathSummonEnabled = (Boolean) deathSummonEnabledObj;
                    }
                    Object useUserCustomSummonObj = config.specialAbilities.get("use_user_custom_summon");
                    if (useUserCustomSummonObj instanceof Boolean) {
                        useUserCustomSummon = (Boolean) useUserCustomSummonObj;
                    }
                    Object useUserCustomDeathSummonObj = config.specialAbilities.get("use_user_custom_death_summon");
                    if (useUserCustomDeathSummonObj instanceof Boolean) {
                        useUserCustomDeathSummon = (Boolean) useUserCustomDeathSummonObj;
                    }
                }
            }

            if (!smokeEnabled) {
                if (debugMode) {
                    logger.info("变异科学家烟雾功能已禁用");
                }
                return;
            }

            // 初始化伤害追踪
            scientistDamageReceived.put(zombie, 0.0);

            // 设置死亡召唤元数据
            if (deathSummonEnabled) {
                zombie.setMetadata("scientist_death_summon_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("scientist_death_summon_count", new FixedMetadataValue(plugin, deathSummonCount));
                zombie.setMetadata("scientist_use_user_custom_death_summon", new FixedMetadataValue(plugin, useUserCustomDeathSummon));
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义科学家任务
                startCustomScientistTask(zombie, effectInterval, smokeRange, damageRange, smokeDamage,
                                       nauseaDuration, slownessDuration, nauseaEnabled, slownessEnabled);

                // 启动伤害召唤监听
                if (summonEnabled) {
                    zombie.setMetadata("scientist_summon_enabled", new FixedMetadataValue(plugin, true));
                    zombie.setMetadata("scientist_summon_threshold", new FixedMetadataValue(plugin, summonDamageThreshold));
                    zombie.setMetadata("scientist_use_user_custom_summon", new FixedMetadataValue(plugin, useUserCustomSummon));
                }

                // 启动全局伤害任务
                if (globalDamageEnabled) {
                    startGlobalDamageTask(zombie, globalDamageInterval, globalDamage);
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startScientistMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startMutantScientistTasks", Zombie.class);
                    startScientistMethod.setAccessible(true);
                    startScientistMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有科学家技能失败，使用自定义实现: " + e.getMessage());
                    startCustomScientistTask(zombie, effectInterval, smokeRange, damageRange, smokeDamage,
                                           nauseaDuration, slownessDuration, nauseaEnabled, slownessEnabled);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用变异科学家技能");
            }

        } catch (Exception e) {
            logger.warning("启用变异科学家技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动自定义变异科学家任务
     */
    private void startCustomScientistTask(Zombie zombie, int effectInterval, int smokeRange, int damageRange,
                                        double smokeDamage, int nauseaDuration, int slownessDuration,
                                        boolean nauseaEnabled, boolean slownessEnabled) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    scientistTasks.remove(zombie);
                    return;
                }

                // 创建围绕变异科学家的烟雾粒子效果
                Location location = zombie.getLocation();

                // 创建围绕科学家的烟雾效果
                for (double y = 0; y < 2; y += 0.5) {
                    double radius = 1.0;
                    for (int i = 0; i < 8; i++) {
                        double angle = Math.toRadians(i * 45);
                        double x = radius * Math.cos(angle);
                        double z = radius * Math.sin(angle);

                        Location particleLoc = location.clone().add(x, y, z);
                        zombie.getWorld().spawnParticle(Particle.SMOKE, particleLoc, 3, 0.1, 0.1, 0.1, 0.01);
                    }
                }

                // 给烟雾范围内玩家恶心和减速效果
                for (Entity entity : zombie.getNearbyEntities(smokeRange, smokeRange, smokeRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        if (nauseaEnabled) {
                            player.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, nauseaDuration, 0));
                        }
                        if (slownessEnabled) {
                            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, 0));
                        }
                    }
                }

                // 给伤害范围内所有玩家造成伤害
                for (Entity entity : zombie.getNearbyEntities(damageRange, damageRange, damageRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;
                        // 造成配置的伤害
                        player.damage(smokeDamage, zombie);

                        if (debugMode) {
                            logger.info("变异科学家对玩家 " + player.getName() + " 造成了 " + smokeDamage + " 点烟雾伤害");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0, effectInterval); // 立即开始，按配置间隔执行

        scientistTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动自定义变异科学家任务，间隔: " + effectInterval + " ticks");
        }
    }

    /**
     * 启动全局伤害任务
     */
    private void startGlobalDamageTask(Zombie zombie, int globalDamageInterval, double globalDamage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    scientistGlobalDamageTasks.remove(zombie);
                    return;
                }

                // 对服务器内所有玩家造成伤害
                for (Player player : plugin.getServer().getOnlinePlayers()) {
                    player.damage(globalDamage, zombie);

                    // 播放爆炸音效
                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);

                    if (debugMode) {
                        logger.info("变异科学家对玩家 " + player.getName() + " 造成了 " + globalDamage + " 点全局伤害");
                    }
                }
            }
        }.runTaskTimer(plugin, globalDamageInterval, globalDamageInterval); // 按配置间隔执行

        scientistGlobalDamageTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异科学家全局伤害任务，间隔: " + globalDamageInterval + " ticks");
        }
    }

    /**
     * 处理变异科学家受到伤害时的召唤
     */
    public void handleScientistDamage(Zombie zombie, double damage) {
        logger.info("=== 处理变异科学家伤害 ===");
        logger.info("伤害值: " + damage);
        logger.info("是否有mutantScientist标记: " + zombie.hasMetadata("mutantScientist"));
        logger.info("是否有scientist_summon_enabled标记: " + zombie.hasMetadata("scientist_summon_enabled"));

        if (!zombie.hasMetadata("mutantScientist") || !zombie.hasMetadata("scientist_summon_enabled")) {
            logger.info("变异科学家缺少必要标记，跳过伤害处理");
            return;
        }

        double threshold = zombie.hasMetadata("scientist_summon_threshold") ?
                          zombie.getMetadata("scientist_summon_threshold").get(0).asDouble() : 50.0;
        double currentDamage = scientistDamageReceived.getOrDefault(zombie, 0.0);
        double newDamage = currentDamage + damage;

        logger.info("召唤阈值: " + threshold);
        logger.info("当前累计伤害: " + currentDamage);
        logger.info("新累计伤害: " + newDamage);

        // 检查是否达到召唤阈值
        if (newDamage >= threshold) {
            logger.info("达到召唤阈值，开始召唤");

            // 重置伤害计数
            scientistDamageReceived.put(zombie, 0.0);

            // 随机召唤武装僵尸(ID8)或毁灭僵尸(ID16)
            String summonType = Math.random() < 0.5 ? "id8" : "id16";
            logger.info("选择召唤类型: " + summonType);

            // 在科学家周围随机位置召唤
            Location summonLoc = zombie.getLocation().clone();
            double offsetX = (Math.random() - 0.5) * 4;
            double offsetZ = (Math.random() - 0.5) * 4;
            summonLoc.add(offsetX, 0, offsetZ);

            // 确保生成位置有效
            while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                summonLoc.setY(summonLoc.getY() - 1);
            }
            summonLoc.setY(summonLoc.getY() + 1);

            logger.info("召唤位置: " + summonLoc);

            // 根据配置选择召唤方式
            boolean useUserCustom = zombie.hasMetadata("scientist_use_user_custom_summon") ?
                                   zombie.getMetadata("scientist_use_user_custom_summon").get(0).asBoolean() : true;

            logger.info("召唤配置 - 使用用户配置版本: " + useUserCustom);

            // 生成僵尸
            if (originalCustomZombie != null) {
                try {
                    Zombie summonedZombie = null;

                    if (useUserCustom) {
                        // 使用双系统召唤（用户配置版本）
                        if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                            if (dzPlugin.getDualZombieSystemManager() != null) {
                                summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, summonType);
                                logger.info("使用双系统召唤了用户配置版本的 " + summonType);
                            }
                        }
                    } else {
                        // 使用原有系统召唤（默认版本）
                        summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, summonType);
                        logger.info("使用原有系统召唤了默认版本的 " + summonType);
                    }

                    if (summonedZombie != null) {
                        // 显示召唤效果
                        zombie.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 20, 0.5, 0.5, 0.5, 0.1);
                        zombie.getWorld().playSound(summonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                        logger.info("变异科学家受到 " + damage + " 点伤害，成功召唤了 " + summonType +
                                  " (版本: " + (useUserCustom ? "用户配置" : "默认") + ")");
                    } else {
                        logger.warning("召唤失败：生成的僵尸为null");
                    }
                } catch (Exception e) {
                    logger.warning("变异科学家召唤失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                logger.warning("originalCustomZombie为null，无法召唤");
            }
        } else {
            // 更新伤害计数
            scientistDamageReceived.put(zombie, newDamage);
            logger.info("伤害未达到阈值，更新累计伤害为: " + newDamage);
        }
    }

    /**
     * 启用变异法师技能（ID19）- 复杂的三重技能系统
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableMutantMageSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加变异法师标记
            zombie.setMetadata("mutantMage", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查变异法师配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int lightningInterval = 100; // 默认5秒间隔
            int lightningRange = 6; // 默认6*6范围
            int effectInterval = 20; // 默认1秒间隔检查
            int effectRange = 10; // 默认10*10范围
            int blindnessDuration = 60; // 默认3秒失明
            int slownessDuration = 60; // 默认3秒缓慢
            int summonInterval = 200; // 默认10秒间隔
            int summonRange = 5; // 默认5*5范围
            boolean lightningEnabled = true; // 默认启用闪电
            boolean effectEnabled = true; // 默认启用负面效果
            boolean summonEnabled = true; // 默认启用召唤
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    lightningInterval = config.skillCooldownOverrides.getOrDefault("lightning_interval", lightningInterval);
                    lightningRange = config.skillCooldownOverrides.getOrDefault("lightning_range", lightningRange);
                    effectInterval = config.skillCooldownOverrides.getOrDefault("effect_interval", effectInterval);
                    effectRange = config.skillCooldownOverrides.getOrDefault("effect_range", effectRange);
                    blindnessDuration = config.skillCooldownOverrides.getOrDefault("blindness_duration", blindnessDuration);
                    slownessDuration = config.skillCooldownOverrides.getOrDefault("slowness_duration", slownessDuration);
                    summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", summonInterval);
                    summonRange = config.skillCooldownOverrides.getOrDefault("summon_range", summonRange);
                }

                if (config.specialAbilities != null) {
                    Object lightningEnabledObj = config.specialAbilities.get("lightning_enabled");
                    if (lightningEnabledObj instanceof Boolean) {
                        lightningEnabled = (Boolean) lightningEnabledObj;
                    }
                    Object effectEnabledObj = config.specialAbilities.get("effect_enabled");
                    if (effectEnabledObj instanceof Boolean) {
                        effectEnabled = (Boolean) effectEnabledObj;
                    }
                    Object summonEnabledObj = config.specialAbilities.get("summon_enabled");
                    if (summonEnabledObj instanceof Boolean) {
                        summonEnabled = (Boolean) summonEnabledObj;
                    }
                    Object useUserCustomSummonObj = config.specialAbilities.get("use_user_custom_summon");
                    if (useUserCustomSummonObj instanceof Boolean) {
                        useUserCustomSummon = (Boolean) useUserCustomSummonObj;
                    }
                }
            }

            // 设置召唤配置元数据
            if (summonEnabled) {
                zombie.setMetadata("mage_summon_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("mage_use_user_custom_summon", new FixedMetadataValue(plugin, useUserCustomSummon));
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义法师任务
                if (lightningEnabled) {
                    startMageLightningTask(zombie, lightningInterval, lightningRange);
                }
                if (effectEnabled) {
                    startMageEffectTask(zombie, effectInterval, effectRange, blindnessDuration, slownessDuration);
                }
                if (summonEnabled) {
                    startMageSummonTask(zombie, summonInterval, summonRange, useUserCustomSummon);
                }
                // 启动粒子效果
                startMageParticleTask(zombie);
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startMageMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startMutantMageTasks", Zombie.class);
                    startMageMethod.setAccessible(true);
                    startMageMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有法师技能失败，使用自定义实现: " + e.getMessage());
                    // 启动默认技能
                    if (lightningEnabled) {
                        startMageLightningTask(zombie, lightningInterval, lightningRange);
                    }
                    if (effectEnabled) {
                        startMageEffectTask(zombie, effectInterval, effectRange, blindnessDuration, slownessDuration);
                    }
                    if (summonEnabled) {
                        startMageSummonTask(zombie, summonInterval, summonRange, useUserCustomSummon);
                    }
                    startMageParticleTask(zombie);
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用变异法师技能");
            }

        } catch (Exception e) {
            logger.warning("启用变异法师技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动法师闪电攻击任务
     */
    private void startMageLightningTask(Zombie zombie, int lightningInterval, int lightningRange) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mageLightningTasks.remove(zombie);
                    return;
                }

                // 对指定范围内所有玩家进行闪电攻击
                for (Entity entity : zombie.getNearbyEntities(lightningRange, lightningRange, lightningRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 在玩家位置生成闪电效果
                        player.getWorld().strikeLightningEffect(player.getLocation());

                        // 造成伤害
                        player.damage(6.0, zombie);

                        // 播放音效
                        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                        if (debugMode) {
                            logger.info("变异法师对玩家 " + player.getName() + " 释放了闪电攻击");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 100, lightningInterval); // 5秒后开始，按配置间隔执行

        mageLightningTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异法师闪电任务，间隔: " + lightningInterval + " ticks，范围: " + lightningRange);
        }
    }

    /**
     * 启动法师负面效果任务
     */
    private void startMageEffectTask(Zombie zombie, int effectInterval, int effectRange, int blindnessDuration, int slownessDuration) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mageEffectTasks.remove(zombie);
                    return;
                }

                // 给指定范围内玩家添加失明和缓慢效果
                for (Entity entity : zombie.getNearbyEntities(effectRange, effectRange, effectRange)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 添加失明效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, blindnessDuration, 0));

                        // 添加缓慢效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, 0));

                        if (debugMode) {
                            logger.info("变异法师对玩家 " + player.getName() + " 施加了失明和缓慢效果");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0, effectInterval); // 立即开始，按配置间隔执行

        mageEffectTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异法师负面效果任务，间隔: " + effectInterval + " ticks，范围: " + effectRange);
        }
    }

    /**
     * 启动法师召唤任务
     */
    private void startMageSummonTask(Zombie zombie, int summonInterval, int summonRange, boolean useUserCustom) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mageSummonTasks.remove(zombie);
                    return;
                }

                // 检测指定范围内是否有玩家
                boolean hasPlayer = false;
                for (Entity entity : zombie.getNearbyEntities(summonRange, summonRange, summonRange)) {
                    if (entity instanceof Player) {
                        hasPlayer = true;
                        break;
                    }
                }

                if (hasPlayer) {
                    logger.info("变异法师检测到玩家，开始召唤");

                    // 召唤2个法师僵尸和1个冰冻僵尸
                    String[] summonTypes = {"id7", "id7", "id14"}; // 2个法师僵尸 + 1个冰冻僵尸

                    for (int i = 0; i < summonTypes.length; i++) {
                        final String summonType = summonTypes[i];
                        final int index = i;

                        // 延迟召唤
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            try {
                                // 计算召唤位置
                                Location summonLoc = zombie.getLocation().clone();
                                double offsetX = (Math.random() - 0.5) * 6;
                                double offsetZ = (Math.random() - 0.5) * 6;
                                summonLoc.add(offsetX, 0, offsetZ);

                                // 确保生成位置有效
                                while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                                    summonLoc.setY(summonLoc.getY() - 1);
                                }
                                summonLoc.setY(summonLoc.getY() + 1);

                                Zombie summonedZombie = null;

                                if (useUserCustom) {
                                    // 使用双系统召唤（用户配置版本）
                                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                                        if (dzPlugin.getDualZombieSystemManager() != null) {
                                            summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, summonType);
                                            logger.info("使用双系统召唤了用户配置版本的 " + summonType);
                                        }
                                    }
                                } else {
                                    // 使用原有系统召唤（默认版本）
                                    if (originalCustomZombie != null) {
                                        summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, summonType);
                                        logger.info("使用原有系统召唤了默认版本的 " + summonType);
                                    }
                                }

                                if (summonedZombie != null) {
                                    // 显示召唤效果
                                    zombie.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 20, 0.5, 0.5, 0.5, 0.1);
                                    zombie.getWorld().playSound(summonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                                    logger.info("变异法师召唤了第 " + (index + 1) + " 个僵尸: " + summonType +
                                              " (版本: " + (useUserCustom ? "用户配置" : "默认") + ")");
                                } else {
                                    logger.warning("变异法师召唤失败：生成的僵尸为null");
                                }

                            } catch (Exception e) {
                                logger.warning("变异法师召唤失败: " + e.getMessage());
                                e.printStackTrace();
                            }
                        }, index * 10L); // 每个僵尸间隔0.5秒生成
                    }
                }
            }
        }.runTaskTimer(plugin, 200, summonInterval); // 10秒后开始，按配置间隔执行

        mageSummonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异法师召唤任务，间隔: " + summonInterval + " ticks，范围: " + summonRange);
        }
    }

    /**
     * 启动法师粒子效果任务
     */
    private void startMageParticleTask(Zombie zombie) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mageParticleTasks.remove(zombie);
                    return;
                }

                Location location = zombie.getLocation();

                // 脚底火焰粒子圆圈
                double radius = 1.5;
                for (int i = 0; i < 16; i++) {
                    double angle = Math.toRadians(i * 22.5);
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);

                    Location particleLoc = location.clone().add(x, 0.1, z);
                    zombie.getWorld().spawnParticle(Particle.FLAME, particleLoc, 1, 0, 0, 0, 0.01);
                }

                // 身边IDC1僵尸粒子特效（紫色粒子螺旋上升）
                for (double y = 0; y < 3; y += 0.3) {
                    double spiralRadius = 0.8;
                    double angle = y * 4; // 螺旋角度
                    double x = spiralRadius * Math.cos(angle);
                    double z = spiralRadius * Math.sin(angle);

                    Location spiralLoc = location.clone().add(x, y, z);
                    zombie.getWorld().spawnParticle(Particle.WITCH, spiralLoc, 1, 0, 0, 0, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0, 5); // 立即开始，每0.25秒执行一次

        mageParticleTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异法师粒子效果任务");
        }
    }

    /**
     * 启用气球僵尸技能（ID20）- 飞行和粒子效果
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableBalloonZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加气球僵尸标记
            zombie.setMetadata("balloonZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查气球僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            boolean flyingEnabled = true; // 默认启用飞行
            boolean particleEnabled = true; // 默认启用粒子效果
            int particleInterval = 10; // 默认0.5秒间隔
            double floatHeight = 3.0; // 默认飞行高度

            if (config != null) {
                if (config.specialAbilities != null) {
                    Object flyingEnabledObj = config.specialAbilities.get("flying_enabled");
                    if (flyingEnabledObj instanceof Boolean) {
                        flyingEnabled = (Boolean) flyingEnabledObj;
                    }
                    Object particleEnabledObj = config.specialAbilities.get("particle_enabled");
                    if (particleEnabledObj instanceof Boolean) {
                        particleEnabled = (Boolean) particleEnabledObj;
                    }
                    Object floatHeightObj = config.specialAbilities.get("float_height");
                    if (floatHeightObj instanceof Number) {
                        floatHeight = ((Number) floatHeightObj).doubleValue();
                    }
                }

                if (config.skillCooldownOverrides != null) {
                    particleInterval = config.skillCooldownOverrides.getOrDefault("particle_interval", particleInterval);
                }
            }

            // 启用飞行能力
            if (flyingEnabled) {
                zombie.setGravity(false);
                // 设置缓慢上升
                startBalloonFloatTask(zombie, floatHeight);
            }

            // 启用粒子效果
            if (particleEnabled) {
                startBalloonParticleTask(zombie, particleInterval);
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 使用自定义配置
                if (debugMode) {
                    logger.info("使用自定义气球僵尸配置");
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startBalloonMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startBalloonZombieTasks", Zombie.class);
                    startBalloonMethod.setAccessible(true);
                    startBalloonMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有气球僵尸技能失败，使用自定义实现: " + e.getMessage());
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用气球僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用气球僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动气球僵尸飞行任务
     */
    private void startBalloonFloatTask(Zombie zombie, double targetHeight) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    balloonTasks.remove(zombie);
                    return;
                }

                Location currentLoc = zombie.getLocation();
                double currentY = currentLoc.getY();

                // 缓慢上升到目标高度
                if (currentY < currentLoc.getY() + targetHeight) {
                    Vector upward = new Vector(0, 0.05, 0);
                    zombie.setVelocity(upward);
                }
            }
        }.runTaskTimer(plugin, 0, 5); // 立即开始，每0.25秒执行一次

        balloonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动气球僵尸飞行任务，目标高度: " + targetHeight);
        }
    }

    /**
     * 启动气球僵尸粒子效果任务
     */
    private void startBalloonParticleTask(Zombie zombie, int particleInterval) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    balloonTasks.remove(zombie);
                    return;
                }

                Location location = zombie.getLocation();

                // 周围彩色气球粒子效果
                for (int i = 0; i < 8; i++) {
                    double angle = Math.toRadians(i * 45);
                    double radius = 1.0;
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);

                    Location particleLoc = location.clone().add(x, 1.5, z);

                    // 随机彩色粒子
                    Particle[] colorParticles = {Particle.HEART, Particle.HAPPY_VILLAGER, Particle.NOTE};
                    Particle randomParticle = colorParticles[(int) (Math.random() * colorParticles.length)];

                    zombie.getWorld().spawnParticle(randomParticle, particleLoc, 1, 0.1, 0.1, 0.1, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0, particleInterval); // 立即开始，按配置间隔执行

        balloonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动气球僵尸粒子效果任务，间隔: " + particleInterval + " ticks");
        }
    }

    /**
     * 启用迷雾僵尸技能（ID21）- 迷雾粒子效果
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableFogZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加迷雾僵尸标记
            zombie.setMetadata("fogZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查迷雾僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            boolean fogEnabled = true; // 默认启用迷雾
            int fogInterval = 5; // 默认0.25秒间隔
            double fogRadius = 2.0; // 默认迷雾半径
            int fogDensity = 5; // 默认迷雾密度

            if (config != null) {
                if (config.specialAbilities != null) {
                    Object fogEnabledObj = config.specialAbilities.get("fog_enabled");
                    if (fogEnabledObj instanceof Boolean) {
                        fogEnabled = (Boolean) fogEnabledObj;
                    }
                    Object fogRadiusObj = config.specialAbilities.get("fog_radius");
                    if (fogRadiusObj instanceof Number) {
                        fogRadius = ((Number) fogRadiusObj).doubleValue();
                    }
                    Object fogDensityObj = config.specialAbilities.get("fog_density");
                    if (fogDensityObj instanceof Number) {
                        fogDensity = ((Number) fogDensityObj).intValue();
                    }
                }

                if (config.skillCooldownOverrides != null) {
                    fogInterval = config.skillCooldownOverrides.getOrDefault("fog_interval", fogInterval);
                }
            }

            // 启用迷雾效果
            if (fogEnabled) {
                startFogParticleTask(zombie, fogInterval, fogRadius, fogDensity);
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 使用自定义配置
                if (debugMode) {
                    logger.info("使用自定义迷雾僵尸配置");
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startFogMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startFogZombieTasks", Zombie.class);
                    startFogMethod.setAccessible(true);
                    startFogMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有迷雾僵尸技能失败，使用自定义实现: " + e.getMessage());
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用迷雾僵尸技能");
            }

        } catch (Exception e) {
            logger.warning("启用迷雾僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动迷雾僵尸粒子效果任务
     */
    private void startFogParticleTask(Zombie zombie, int fogInterval, double fogRadius, int fogDensity) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    fogTasks.remove(zombie);
                    return;
                }

                Location location = zombie.getLocation();

                // 周围迷雾粒子效果
                for (int i = 0; i < fogDensity; i++) {
                    // 随机位置在僵尸周围
                    double offsetX = (Math.random() - 0.5) * fogRadius * 2;
                    double offsetY = Math.random() * 2;
                    double offsetZ = (Math.random() - 0.5) * fogRadius * 2;

                    Location fogLoc = location.clone().add(offsetX, offsetY, offsetZ);

                    // 生成迷雾粒子
                    zombie.getWorld().spawnParticle(Particle.CLOUD, fogLoc, 1, 0.1, 0.1, 0.1, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0, fogInterval); // 立即开始，按配置间隔执行

        fogTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动迷雾僵尸粒子效果任务，间隔: " + fogInterval + " ticks，半径: " + fogRadius + "，密度: " + fogDensity);
        }
    }

    /**
     * 启用变异雷霆僵尸技能（ID22）- 最复杂的五重技能系统
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableMutantThunderZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加变异雷霆僵尸标记
            zombie.setMetadata("mutantThunderZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查变异雷霆僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int globalLightningInterval = 300; // 默认15秒间隔
            int teleportInterval = 300; // 默认15秒间隔
            int summonInterval = 1200; // 默认60秒间隔
            int timeSlowInterval = 200; // 默认10秒间隔
            int freezeInterval = 2400; // 默认2分钟间隔

            double globalLightningDamage = 8.0; // 默认8点伤害
            double teleportLightningDamage = 6.0; // 默认6点伤害
            int timeSlowDuration = 100; // 默认5秒持续时间
            int freezeDuration = 100; // 默认5秒冻结时间
            int summonThunderCount = 4; // 默认4个雷霆僵尸
            int summonCreeperCount = 2; // 默认2个变异爬行者

            boolean globalLightningEnabled = true; // 默认启用全局雷电
            boolean teleportEnabled = true; // 默认启用瞬移雷击
            boolean summonEnabled = true; // 默认启用召唤
            boolean timeSlowEnabled = true; // 默认启用时间流速控制
            boolean freezeEnabled = true; // 默认启用冻结
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    globalLightningInterval = config.skillCooldownOverrides.getOrDefault("global_lightning_interval", globalLightningInterval);
                    teleportInterval = config.skillCooldownOverrides.getOrDefault("teleport_interval", teleportInterval);
                    summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", summonInterval);
                    timeSlowInterval = config.skillCooldownOverrides.getOrDefault("time_slow_interval", timeSlowInterval);
                    freezeInterval = config.skillCooldownOverrides.getOrDefault("freeze_interval", freezeInterval);
                    timeSlowDuration = config.skillCooldownOverrides.getOrDefault("time_slow_duration", timeSlowDuration);
                    freezeDuration = config.skillCooldownOverrides.getOrDefault("freeze_duration", freezeDuration);
                    summonThunderCount = config.skillCooldownOverrides.getOrDefault("summon_thunder_count", summonThunderCount);
                    summonCreeperCount = config.skillCooldownOverrides.getOrDefault("summon_creeper_count", summonCreeperCount);
                }

                if (config.specialAbilities != null) {
                    Object globalLightningEnabledObj = config.specialAbilities.get("global_lightning_enabled");
                    if (globalLightningEnabledObj instanceof Boolean) {
                        globalLightningEnabled = (Boolean) globalLightningEnabledObj;
                    }
                    Object teleportEnabledObj = config.specialAbilities.get("teleport_enabled");
                    if (teleportEnabledObj instanceof Boolean) {
                        teleportEnabled = (Boolean) teleportEnabledObj;
                    }
                    Object summonEnabledObj = config.specialAbilities.get("summon_enabled");
                    if (summonEnabledObj instanceof Boolean) {
                        summonEnabled = (Boolean) summonEnabledObj;
                    }
                    Object timeSlowEnabledObj = config.specialAbilities.get("time_slow_enabled");
                    if (timeSlowEnabledObj instanceof Boolean) {
                        timeSlowEnabled = (Boolean) timeSlowEnabledObj;
                    }
                    Object freezeEnabledObj = config.specialAbilities.get("freeze_enabled");
                    if (freezeEnabledObj instanceof Boolean) {
                        freezeEnabled = (Boolean) freezeEnabledObj;
                    }
                    Object useUserCustomSummonObj = config.specialAbilities.get("use_user_custom_summon");
                    if (useUserCustomSummonObj instanceof Boolean) {
                        useUserCustomSummon = (Boolean) useUserCustomSummonObj;
                    }
                    Object globalLightningDamageObj = config.specialAbilities.get("global_lightning_damage");
                    if (globalLightningDamageObj instanceof Number) {
                        globalLightningDamage = ((Number) globalLightningDamageObj).doubleValue();
                    }
                    Object teleportLightningDamageObj = config.specialAbilities.get("teleport_lightning_damage");
                    if (teleportLightningDamageObj instanceof Number) {
                        teleportLightningDamage = ((Number) teleportLightningDamageObj).doubleValue();
                    }
                }
            }

            // 设置召唤配置元数据
            if (summonEnabled) {
                zombie.setMetadata("mutant_thunder_summon_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("mutant_thunder_use_user_custom_summon", new FixedMetadataValue(plugin, useUserCustomSummon));
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义变异雷霆僵尸任务
                if (globalLightningEnabled) {
                    startMutantThunderGlobalLightningTask(zombie, globalLightningInterval, globalLightningDamage);
                }
                if (teleportEnabled) {
                    startMutantThunderTeleportTask(zombie, teleportInterval, teleportLightningDamage);
                }
                if (summonEnabled) {
                    startMutantThunderSummonTask(zombie, summonInterval, summonThunderCount, summonCreeperCount, useUserCustomSummon);
                }
                if (timeSlowEnabled) {
                    startMutantThunderTimeSlowTask(zombie, timeSlowInterval, timeSlowDuration);
                }
                if (freezeEnabled) {
                    startMutantThunderFreezeTask(zombie, freezeInterval, freezeDuration);
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startMutantThunderMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startMutantThunderZombieTasks", Zombie.class);
                    startMutantThunderMethod.setAccessible(true);
                    startMutantThunderMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有变异雷霆僵尸技能失败，使用自定义实现: " + e.getMessage());
                    // 启动默认技能
                    if (globalLightningEnabled) {
                        startMutantThunderGlobalLightningTask(zombie, globalLightningInterval, globalLightningDamage);
                    }
                    if (teleportEnabled) {
                        startMutantThunderTeleportTask(zombie, teleportInterval, teleportLightningDamage);
                    }
                    if (summonEnabled) {
                        startMutantThunderSummonTask(zombie, summonInterval, summonThunderCount, summonCreeperCount, useUserCustomSummon);
                    }
                    if (timeSlowEnabled) {
                        startMutantThunderTimeSlowTask(zombie, timeSlowInterval, timeSlowDuration);
                    }
                    if (freezeEnabled) {
                        startMutantThunderFreezeTask(zombie, freezeInterval, freezeDuration);
                    }
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用变异雷霆僵尸五重技能系统");
            }

        } catch (Exception e) {
            logger.warning("启用变异雷霆僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动变异雷霆僵尸全局雷电攻击任务
     */
    private void startMutantThunderGlobalLightningTask(Zombie zombie, int globalLightningInterval, double globalLightningDamage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantThunderGlobalLightningTasks.remove(zombie);
                    return;
                }

                // 对所有在线玩家释放雷电攻击
                for (Player player : plugin.getServer().getOnlinePlayers()) {
                    // 在玩家位置生成雷电效果
                    player.getWorld().strikeLightningEffect(player.getLocation());

                    // 造成伤害
                    player.damage(globalLightningDamage, zombie);

                    // 播放雷电音效
                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                    // 雷电爆炸粒子效果
                    player.getWorld().spawnParticle(Particle.EXPLOSION, player.getLocation(), 3, 0.5, 0.5, 0.5, 0.1);

                    if (debugMode) {
                        logger.info("变异雷霆僵尸对玩家 " + player.getName() + " 释放了全局雷电攻击");
                    }
                }
            }
        }.runTaskTimer(plugin, 300, globalLightningInterval); // 15秒后开始，按配置间隔执行

        mutantThunderGlobalLightningTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异雷霆僵尸全局雷电任务，间隔: " + globalLightningInterval + " ticks");
        }
    }

    /**
     * 启动变异雷霆僵尸瞬移雷击任务
     */
    private void startMutantThunderTeleportTask(Zombie zombie, int teleportInterval, double teleportLightningDamage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantThunderTeleportTasks.remove(zombie);
                    return;
                }

                // 获取所有在线玩家
                List<Player> onlinePlayers = new ArrayList<>(plugin.getServer().getOnlinePlayers());
                if (onlinePlayers.isEmpty()) {
                    return;
                }

                // 随机选择一个玩家
                Player targetPlayer = onlinePlayers.get((int) (Math.random() * onlinePlayers.size()));
                Location targetLoc = targetPlayer.getLocation();

                // 计算瞬移位置（玩家附近3-5格范围）
                double offsetX = (Math.random() - 0.5) * 8;
                double offsetZ = (Math.random() - 0.5) * 8;
                Location teleportLoc = targetLoc.clone().add(offsetX, 0, offsetZ);

                // 确保瞬移位置有效
                while (!teleportLoc.getBlock().getType().isSolid() && teleportLoc.getBlockY() > 0) {
                    teleportLoc.setY(teleportLoc.getY() - 1);
                }
                teleportLoc.setY(teleportLoc.getY() + 1);

                // 瞬移僵尸
                zombie.teleport(teleportLoc);

                // 瞬移效果
                zombie.getWorld().spawnParticle(Particle.PORTAL, teleportLoc, 20, 0.5, 0.5, 0.5, 0.1);
                zombie.getWorld().playSound(teleportLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                // 瞬移后立即对附近玩家释放雷电攻击
                for (Entity entity : zombie.getNearbyEntities(12, 12, 12)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 在玩家位置生成雷电效果
                        player.getWorld().strikeLightningEffect(player.getLocation());

                        // 造成伤害
                        player.damage(teleportLightningDamage, zombie);

                        // 播放雷电音效
                        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                        if (debugMode) {
                            logger.info("变异雷霆僵尸瞬移后对玩家 " + player.getName() + " 释放了雷电攻击");
                        }
                    }
                }

                if (debugMode) {
                    logger.info("变异雷霆僵尸瞬移到玩家 " + targetPlayer.getName() + " 附近并释放雷电攻击");
                }
            }
        }.runTaskTimer(plugin, 300, teleportInterval); // 15秒后开始，按配置间隔执行

        mutantThunderTeleportTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异雷霆僵尸瞬移雷击任务，间隔: " + teleportInterval + " ticks");
        }
    }

    /**
     * 启动变异雷霆僵尸召唤任务
     */
    private void startMutantThunderSummonTask(Zombie zombie, int summonInterval, int summonThunderCount, int summonCreeperCount, boolean useUserCustom) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantThunderSummonTasks.remove(zombie);
                    return;
                }

                logger.info("变异雷霆僵尸开始召唤");

                // 召唤雷霆僵尸
                for (int i = 0; i < summonThunderCount; i++) {
                    final int index = i;

                    // 延迟召唤
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        try {
                            // 计算召唤位置
                            Location summonLoc = zombie.getLocation().clone();
                            double offsetX = (Math.random() - 0.5) * 8;
                            double offsetZ = (Math.random() - 0.5) * 8;
                            summonLoc.add(offsetX, 0, offsetZ);

                            // 确保生成位置有效
                            while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                                summonLoc.setY(summonLoc.getY() - 1);
                            }
                            summonLoc.setY(summonLoc.getY() + 1);

                            Zombie summonedZombie = null;

                            if (useUserCustom) {
                                // 使用双系统召唤（用户配置版本）
                                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                                    if (dzPlugin.getDualZombieSystemManager() != null) {
                                        summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, "id17");
                                        logger.info("使用双系统召唤了用户配置版本的雷霆僵尸");
                                    }
                                }
                            } else {
                                // 使用原有系统召唤（默认版本）
                                if (originalCustomZombie != null) {
                                    summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, "id17");
                                    logger.info("使用原有系统召唤了默认版本的雷霆僵尸");
                                }
                            }

                            if (summonedZombie != null) {
                                // 显示召唤效果
                                zombie.getWorld().spawnParticle(Particle.EXPLOSION, summonLoc, 5, 0.5, 0.5, 0.5, 0.1);
                                zombie.getWorld().playSound(summonLoc, Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 0.8f);

                                logger.info("变异雷霆僵尸召唤了第 " + (index + 1) + " 个雷霆僵尸");
                            }

                        } catch (Exception e) {
                            logger.warning("变异雷霆僵尸召唤雷霆僵尸失败: " + e.getMessage());
                        }
                    }, index * 15L); // 每个僵尸间隔0.75秒生成
                }

                // 召唤变异爬行者
                for (int i = 0; i < summonCreeperCount; i++) {
                    final int index = i;

                    // 延迟召唤
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        try {
                            // 计算召唤位置
                            Location summonLoc = zombie.getLocation().clone();
                            double offsetX = (Math.random() - 0.5) * 8;
                            double offsetZ = (Math.random() - 0.5) * 8;
                            summonLoc.add(offsetX, 0, offsetZ);

                            // 确保生成位置有效
                            while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                                summonLoc.setY(summonLoc.getY() - 1);
                            }
                            summonLoc.setY(summonLoc.getY() + 1);

                            // 生成变异爬行者（仿照原有系统的实现）
                            try {
                                // 直接创建强化爬行者，仿照CustomZombie.java第5010-5015行的实现
                                org.bukkit.entity.Creeper creeper = summonLoc.getWorld().spawn(summonLoc, org.bukkit.entity.Creeper.class);
                                creeper.setPowered(true); // 使用带电爬行者
                                creeper.setMaxHealth(40.0);
                                creeper.setHealth(40.0);
                                creeper.setCustomName("§b变异爬行者");
                                creeper.setCustomNameVisible(true);

                                // 显示召唤效果
                                zombie.getWorld().spawnParticle(Particle.EXPLOSION, summonLoc, 5, 0.5, 0.5, 0.5, 0.1);
                                zombie.getWorld().playSound(summonLoc, Sound.ENTITY_CREEPER_PRIMED, 1.0f, 0.8f);

                                logger.info("变异雷霆僵尸召唤了第 " + (index + 1) + " 个变异爬行者");

                            } catch (Exception e) {
                                logger.warning("变异雷霆僵尸召唤变异爬行者失败: " + e.getMessage());
                                e.printStackTrace();
                            }

                        } catch (Exception e) {
                            logger.warning("变异雷霆僵尸召唤变异爬行者失败: " + e.getMessage());
                        }
                    }, (summonThunderCount * 15L) + (index * 15L)); // 在雷霆僵尸召唤完成后开始
                }
            }
        }.runTaskTimer(plugin, 1200, summonInterval); // 60秒后开始，按配置间隔执行

        mutantThunderSummonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异雷霆僵尸召唤任务，间隔: " + summonInterval + " ticks");
        }
    }

    /**
     * 启动变异雷霆僵尸时间流速控制任务
     */
    private void startMutantThunderTimeSlowTask(Zombie zombie, int timeSlowInterval, int timeSlowDuration) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantThunderTimeSlowTasks.remove(zombie);
                    return;
                }

                // 对周围玩家施加时间流速控制效果
                for (Entity entity : zombie.getNearbyEntities(15, 15, 15)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 施加缓慢效果（移动速度减慢50%）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, timeSlowDuration, 1)); // 等级1 = 50%减速

                        // 施加挖掘疲劳效果（攻击速度减慢50%）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, timeSlowDuration, 1)); // 等级1 = 50%减速

                        // 时间流速控制视觉效果
                        player.getWorld().spawnParticle(Particle.WITCH, player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                        player.getWorld().playSound(player.getLocation(), Sound.BLOCK_PORTAL_AMBIENT, 0.5f, 0.5f);

                        if (debugMode) {
                            logger.info("变异雷霆僵尸对玩家 " + player.getName() + " 施加了时间流速控制");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 200, timeSlowInterval); // 10秒后开始，按配置间隔执行

        mutantThunderTimeSlowTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异雷霆僵尸时间流速控制任务，间隔: " + timeSlowInterval + " ticks，持续: " + timeSlowDuration + " ticks");
        }
    }

    /**
     * 启动变异雷霆僵尸冻结任务
     */
    private void startMutantThunderFreezeTask(Zombie zombie, int freezeInterval, int freezeDuration) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantThunderFreezeTasks.remove(zombie);
                    return;
                }

                // 对周围所有玩家施加冻结效果
                for (Entity entity : zombie.getNearbyEntities(20, 20, 20)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 施加极强的缓慢效果（几乎无法移动）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, freezeDuration, 9)); // 等级9 = 几乎冻结

                        // 施加极强的挖掘疲劳效果（无法攻击）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, freezeDuration, 9)); // 等级9 = 几乎无法攻击

                        // 施加跳跃减弱效果（无法跳跃）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, freezeDuration, -10)); // 负等级 = 无法跳跃

                        // 冻结视觉效果
                        player.getWorld().spawnParticle(Particle.CLOUD, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);
                        player.getWorld().spawnParticle(Particle.CLOUD, player.getLocation().add(0, 1, 0), 10, 0.5, 1, 0.5, 0.1);
                        player.getWorld().playSound(player.getLocation(), Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);

                        if (debugMode) {
                            logger.info("变异雷霆僵尸冻结了玩家 " + player.getName());
                        }
                    }
                }

                // 全局冻结音效
                zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.5f);

                if (debugMode) {
                    logger.info("变异雷霆僵尸释放了冻结技能");
                }
            }
        }.runTaskTimer(plugin, 2400, freezeInterval); // 2分钟后开始，按配置间隔执行

        mutantThunderFreezeTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异雷霆僵尸冻结任务，间隔: " + freezeInterval + " ticks，持续: " + freezeDuration + " ticks");
        }
    }

    /**
     * 启用终极毁灭僵尸技能（ID23）- 最强的四重技能系统
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableUltimateDestructionZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加终极毁灭僵尸标记
            zombie.setMetadata("ultimateDestructionZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查终极毁灭僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int summonInterval = 600; // 默认30秒间隔
            int explosionInterval = 800; // 默认40秒间隔
            int auraCheckInterval = 100; // 默认5秒检查间隔

            double explosionDamage = 5.0; // 默认5点爆炸伤害
            int weaknessLevel = 1; // 默认虚弱2等级（1=虚弱2）
            int weaknessDuration = 60; // 默认3秒持续时间
            double knockbackDistance = 4.0; // 默认4格击退距离
            int summonCount = 3; // 默认召唤3个毁灭僵尸

            boolean attackEnhanceEnabled = true; // 默认启用攻击强化
            boolean summonEnabled = true; // 默认启用召唤
            boolean auraEnabled = true; // 默认启用光环
            boolean explosionEnabled = true; // 默认启用爆炸
            boolean particleEnabled = true; // 默认启用粒子效果
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", summonInterval);
                    explosionInterval = config.skillCooldownOverrides.getOrDefault("explosion_interval", explosionInterval);
                    auraCheckInterval = config.skillCooldownOverrides.getOrDefault("aura_check_interval", auraCheckInterval);
                    weaknessLevel = config.skillCooldownOverrides.getOrDefault("weakness_level", weaknessLevel);
                    weaknessDuration = config.skillCooldownOverrides.getOrDefault("weakness_duration", weaknessDuration);
                    summonCount = config.skillCooldownOverrides.getOrDefault("summon_count", summonCount);
                }

                if (config.specialAbilities != null) {
                    Object attackEnhanceEnabledObj = config.specialAbilities.get("attack_enhance_enabled");
                    if (attackEnhanceEnabledObj instanceof Boolean) {
                        attackEnhanceEnabled = (Boolean) attackEnhanceEnabledObj;
                    }
                    Object summonEnabledObj = config.specialAbilities.get("summon_enabled");
                    if (summonEnabledObj instanceof Boolean) {
                        summonEnabled = (Boolean) summonEnabledObj;
                    }
                    Object auraEnabledObj = config.specialAbilities.get("aura_enabled");
                    if (auraEnabledObj instanceof Boolean) {
                        auraEnabled = (Boolean) auraEnabledObj;
                    }
                    Object explosionEnabledObj = config.specialAbilities.get("explosion_enabled");
                    if (explosionEnabledObj instanceof Boolean) {
                        explosionEnabled = (Boolean) explosionEnabledObj;
                    }
                    Object particleEnabledObj = config.specialAbilities.get("particle_enabled");
                    if (particleEnabledObj instanceof Boolean) {
                        particleEnabled = (Boolean) particleEnabledObj;
                    }
                    Object useUserCustomSummonObj = config.specialAbilities.get("use_user_custom_summon");
                    if (useUserCustomSummonObj instanceof Boolean) {
                        useUserCustomSummon = (Boolean) useUserCustomSummonObj;
                    }
                    Object explosionDamageObj = config.specialAbilities.get("explosion_damage");
                    if (explosionDamageObj instanceof Number) {
                        explosionDamage = ((Number) explosionDamageObj).doubleValue();
                    }
                    Object knockbackDistanceObj = config.specialAbilities.get("knockback_distance");
                    if (knockbackDistanceObj instanceof Number) {
                        knockbackDistance = ((Number) knockbackDistanceObj).doubleValue();
                    }
                }
            }

            // 设置攻击强化元数据
            if (attackEnhanceEnabled) {
                zombie.setMetadata("ultimate_attack_enhance_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("ultimate_weakness_level", new FixedMetadataValue(plugin, weaknessLevel));
                zombie.setMetadata("ultimate_weakness_duration", new FixedMetadataValue(plugin, weaknessDuration));
                zombie.setMetadata("ultimate_knockback_distance", new FixedMetadataValue(plugin, knockbackDistance));
            }

            // 设置召唤配置元数据
            if (summonEnabled) {
                zombie.setMetadata("ultimate_summon_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("ultimate_use_user_custom_summon", new FixedMetadataValue(plugin, useUserCustomSummon));
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义终极毁灭僵尸任务
                if (summonEnabled) {
                    startUltimateDestructionSummonTask(zombie, summonInterval, summonCount, useUserCustomSummon);
                }
                if (auraEnabled) {
                    startUltimateDestructionAuraTask(zombie, auraCheckInterval);
                }
                if (explosionEnabled) {
                    startUltimateDestructionExplosionTask(zombie, explosionInterval, explosionDamage);
                }
                if (particleEnabled) {
                    startUltimateDestructionParticleTask(zombie);
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startUltimateMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startUltimateDestructionZombieTasks", Zombie.class);
                    startUltimateMethod.setAccessible(true);
                    startUltimateMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有终极毁灭僵尸技能失败，使用自定义实现: " + e.getMessage());
                    // 启动默认技能
                    if (summonEnabled) {
                        startUltimateDestructionSummonTask(zombie, summonInterval, summonCount, useUserCustomSummon);
                    }
                    if (auraEnabled) {
                        startUltimateDestructionAuraTask(zombie, auraCheckInterval);
                    }
                    if (explosionEnabled) {
                        startUltimateDestructionExplosionTask(zombie, explosionInterval, explosionDamage);
                    }
                    if (particleEnabled) {
                        startUltimateDestructionParticleTask(zombie);
                    }
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用终极毁灭僵尸四重技能系统");
            }

        } catch (Exception e) {
            logger.warning("启用终极毁灭僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动终极毁灭僵尸召唤任务
     */
    private void startUltimateDestructionSummonTask(Zombie zombie, int summonInterval, int summonCount, boolean useUserCustom) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    ultimateDestructionSummonTasks.remove(zombie);
                    return;
                }

                logger.info("终极毁灭僵尸开始召唤，数量: " + summonCount);

                // 召唤毁灭僵尸
                for (int i = 0; i < summonCount; i++) {
                    final int index = i;

                    // 延迟召唤
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        try {
                            // 计算召唤位置
                            Location summonLoc = zombie.getLocation().clone();
                            double offsetX = (Math.random() - 0.5) * 10;
                            double offsetZ = (Math.random() - 0.5) * 10;
                            summonLoc.add(offsetX, 0, offsetZ);

                            // 确保生成位置有效
                            while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                                summonLoc.setY(summonLoc.getY() - 1);
                            }
                            summonLoc.setY(summonLoc.getY() + 1);

                            Zombie summonedZombie = null;

                            if (useUserCustom) {
                                // 使用双系统召唤（用户配置版本）
                                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                                    if (dzPlugin.getDualZombieSystemManager() != null) {
                                        summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, "id16");
                                        logger.info("使用双系统召唤了用户配置版本的毁灭僵尸");
                                    }
                                }
                            } else {
                                // 使用原有系统召唤（默认版本）
                                if (originalCustomZombie != null) {
                                    summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, "id16");
                                    logger.info("使用原有系统召唤了默认版本的毁灭僵尸");
                                }
                            }

                            if (summonedZombie != null) {
                                // 显示召唤效果
                                zombie.getWorld().spawnParticle(Particle.EXPLOSION, summonLoc, 8, 0.5, 0.5, 0.5, 0.1);
                                zombie.getWorld().playSound(summonLoc, Sound.ENTITY_WITHER_SPAWN, 1.0f, 0.8f);

                                logger.info("终极毁灭僵尸召唤了第 " + (index + 1) + " 个毁灭僵尸");
                            }

                        } catch (Exception e) {
                            logger.warning("终极毁灭僵尸召唤毁灭僵尸失败: " + e.getMessage());
                        }
                    }, index * 20L); // 每个僵尸间隔1秒生成
                }
            }
        }.runTaskTimer(plugin, 600, summonInterval); // 30秒后开始，按配置间隔执行

        ultimateDestructionSummonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动终极毁灭僵尸召唤任务，间隔: " + summonInterval + " ticks");
        }
    }

    /**
     * 启动终极毁灭僵尸光环任务
     */
    private void startUltimateDestructionAuraTask(Zombie zombie, int auraCheckInterval) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    ultimateDestructionAuraTasks.remove(zombie);
                    return;
                }

                // 对10*10范围内名称叫"毁灭僵尸"的僵尸施加光环效果
                for (Entity entity : zombie.getNearbyEntities(10, 10, 10)) {
                    if (entity instanceof Zombie) {
                        Zombie nearbyZombie = (Zombie) entity;
                        String zombieName = nearbyZombie.getCustomName();

                        if (zombieName != null && zombieName.contains("毁灭僵尸")) {
                            // 施加力量2和速度2buff
                            nearbyZombie.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, auraCheckInterval + 20, 1)); // 力量2
                            nearbyZombie.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, auraCheckInterval + 20, 1)); // 速度2

                            // 设置死亡召唤标记
                            nearbyZombie.setMetadata("destruction_aura_death_summon", new FixedMetadataValue(plugin, true));

                            // 光环效果粒子
                            nearbyZombie.getWorld().spawnParticle(Particle.ENCHANT, nearbyZombie.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0.1);

                            if (debugMode) {
                                logger.info("终极毁灭僵尸对 " + zombieName + " 施加了光环效果");
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0, auraCheckInterval); // 立即开始，按配置间隔执行

        ultimateDestructionAuraTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动终极毁灭僵尸光环任务，间隔: " + auraCheckInterval + " ticks");
        }
    }

    /**
     * 启动终极毁灭僵尸爆炸任务
     */
    private void startUltimateDestructionExplosionTask(Zombie zombie, int explosionInterval, double explosionDamage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    ultimateDestructionExplosionTasks.remove(zombie);
                    return;
                }

                // 获取所有在线玩家
                List<Player> onlinePlayers = new ArrayList<>(plugin.getServer().getOnlinePlayers());
                if (onlinePlayers.isEmpty()) {
                    return;
                }

                // 对每个玩家在其旁边生成小型爆炸
                for (Player player : onlinePlayers) {
                    Location playerLoc = player.getLocation();

                    // 在玩家旁边随机位置生成爆炸
                    double offsetX = (Math.random() - 0.5) * 4;
                    double offsetZ = (Math.random() - 0.5) * 4;
                    Location explosionLoc = playerLoc.clone().add(offsetX, 0, offsetZ);

                    // 小型爆炸效果
                    player.getWorld().spawnParticle(Particle.EXPLOSION, explosionLoc, 5, 0.5, 0.5, 0.5, 0.1);
                    player.getWorld().playSound(explosionLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.2f);

                    // 造成伤害
                    player.damage(explosionDamage, zombie);

                    if (debugMode) {
                        logger.info("终极毁灭僵尸在玩家 " + player.getName() + " 旁边生成了小型爆炸");
                    }
                }
            }
        }.runTaskTimer(plugin, 800, explosionInterval); // 40秒后开始，按配置间隔执行

        ultimateDestructionExplosionTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动终极毁灭僵尸爆炸任务，间隔: " + explosionInterval + " ticks");
        }
    }

    /**
     * 启动终极毁灭僵尸粒子效果任务
     */
    private void startUltimateDestructionParticleTask(Zombie zombie) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    ultimateDestructionParticleTasks.remove(zombie);
                    return;
                }

                Location location = zombie.getLocation();

                // 1. 黑色烟雾粒子围成的正方体"气场"
                double size = 2.0;
                for (double x = -size; x <= size; x += 0.5) {
                    for (double y = 0; y <= size * 2; y += 0.5) {
                        for (double z = -size; z <= size; z += 0.5) {
                            // 只在正方体的边缘生成粒子
                            if (Math.abs(x) == size || Math.abs(z) == size || y == 0 || y == size * 2) {
                                Location particleLoc = location.clone().add(x, y, z);
                                zombie.getWorld().spawnParticle(Particle.SMOKE, particleLoc, 1, 0, 0, 0, 0.01);
                            }
                        }
                    }
                }

                // 2. 火焰光球体环绕身体
                double time = System.currentTimeMillis() / 1000.0;
                for (int i = 0; i < 6; i++) {
                    double angle = (time + i * Math.PI / 3) % (2 * Math.PI);
                    double radius = 1.5;
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);
                    double y = 1 + 0.5 * Math.sin(time * 2 + i); // 上下浮动

                    Location orbLoc = location.clone().add(x, y, z);
                    zombie.getWorld().spawnParticle(Particle.FLAME, orbLoc, 3, 0.1, 0.1, 0.1, 0.01);
                }

                // 3. 脚底火焰光圈
                double radius = 1.2;
                for (int i = 0; i < 16; i++) {
                    double angle = Math.toRadians(i * 22.5);
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);

                    Location circleLoc = location.clone().add(x, 0.1, z);
                    zombie.getWorld().spawnParticle(Particle.FLAME, circleLoc, 1, 0, 0, 0, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0, 3); // 立即开始，每0.15秒执行一次

        ultimateDestructionParticleTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动终极毁灭僵尸粒子效果任务");
        }
    }

    /**
     * 启用变异暗影僵尸技能（ID24）- 三重暗影技能系统
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableMutantShadowZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加变异暗影僵尸标记
            zombie.setMetadata("mutantShadowZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("检查变异暗影僵尸配置 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int teleportInterval = 100; // 默认5秒间隔
            int summonInterval = 200; // 默认10秒间隔
            int debuffInterval = 40; // 默认2秒间隔

            double teleportDamage = 8.0; // 默认8点瞬移攻击伤害
            int debuffDuration = 40; // 默认2秒持续时间
            int blindnessLevel = 8; // 默认失明9等级（8=失明9）
            int nauseaLevel = 8; // 默认反胃9等级（8=反胃9）

            boolean teleportEnabled = true; // 默认启用瞬移攻击
            boolean summonEnabled = true; // 默认启用召唤
            boolean debuffEnabled = true; // 默认启用负面效果
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤

            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    teleportInterval = config.skillCooldownOverrides.getOrDefault("teleport_interval", teleportInterval);
                    summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", summonInterval);
                    debuffInterval = config.skillCooldownOverrides.getOrDefault("debuff_interval", debuffInterval);
                    debuffDuration = config.skillCooldownOverrides.getOrDefault("debuff_duration", debuffDuration);
                    blindnessLevel = config.skillCooldownOverrides.getOrDefault("blindness_level", blindnessLevel);
                    nauseaLevel = config.skillCooldownOverrides.getOrDefault("nausea_level", nauseaLevel);
                }

                if (config.specialAbilities != null) {
                    Object teleportEnabledObj = config.specialAbilities.get("teleport_enabled");
                    if (teleportEnabledObj instanceof Boolean) {
                        teleportEnabled = (Boolean) teleportEnabledObj;
                    }
                    Object summonEnabledObj = config.specialAbilities.get("summon_enabled");
                    if (summonEnabledObj instanceof Boolean) {
                        summonEnabled = (Boolean) summonEnabledObj;
                    }
                    Object debuffEnabledObj = config.specialAbilities.get("debuff_enabled");
                    if (debuffEnabledObj instanceof Boolean) {
                        debuffEnabled = (Boolean) debuffEnabledObj;
                    }
                    Object useUserCustomSummonObj = config.specialAbilities.get("use_user_custom_summon");
                    if (useUserCustomSummonObj instanceof Boolean) {
                        useUserCustomSummon = (Boolean) useUserCustomSummonObj;
                    }
                    Object teleportDamageObj = config.specialAbilities.get("teleport_damage");
                    if (teleportDamageObj instanceof Number) {
                        teleportDamage = ((Number) teleportDamageObj).doubleValue();
                    }
                }
            }

            // 设置召唤配置元数据
            if (summonEnabled) {
                zombie.setMetadata("mutant_shadow_summon_enabled", new FixedMetadataValue(plugin, true));
                zombie.setMetadata("mutant_shadow_use_user_custom_summon", new FixedMetadataValue(plugin, useUserCustomSummon));
            }

            // 检查是否有自定义参数
            if (config != null && (config.skillCooldownOverrides != null || config.specialAbilities != null)) {
                // 启动自定义变异暗影僵尸任务
                if (teleportEnabled) {
                    startMutantShadowTeleportTask(zombie, teleportInterval, teleportDamage);
                }
                if (summonEnabled) {
                    startMutantShadowSummonTask(zombie, summonInterval, useUserCustomSummon);
                }
                if (debuffEnabled) {
                    startMutantShadowDebuffTask(zombie, debuffInterval, debuffDuration, blindnessLevel, nauseaLevel);
                }
            } else {
                // 使用原有的默认实现
                try {
                    java.lang.reflect.Method startShadowMethod =
                        originalCustomZombie.getClass().getDeclaredMethod("startMutantShadowZombieTasks", Zombie.class);
                    startShadowMethod.setAccessible(true);
                    startShadowMethod.invoke(originalCustomZombie, zombie);
                } catch (Exception e) {
                    logger.warning("调用原有变异暗影僵尸技能失败，使用自定义实现: " + e.getMessage());
                    // 启动默认技能
                    if (teleportEnabled) {
                        startMutantShadowTeleportTask(zombie, teleportInterval, teleportDamage);
                    }
                    if (summonEnabled) {
                        startMutantShadowSummonTask(zombie, summonInterval, useUserCustomSummon);
                    }
                    if (debuffEnabled) {
                        startMutantShadowDebuffTask(zombie, debuffInterval, debuffDuration, blindnessLevel, nauseaLevel);
                    }
                }
            }

            if (debugMode) {
                logger.info("已为" + zombieId + "启用变异暗影僵尸三重技能系统");
            }

        } catch (Exception e) {
            logger.warning("启用变异暗影僵尸技能失败: " + e.getMessage());
        }
    }

    /**
     * 启动变异暗影僵尸瞬移攻击任务
     */
    private void startMutantShadowTeleportTask(Zombie zombie, int teleportInterval, double teleportDamage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantShadowTeleportTasks.remove(zombie);
                    return;
                }

                // 获取所有在线玩家
                List<Player> onlinePlayers = new ArrayList<>(plugin.getServer().getOnlinePlayers());
                if (onlinePlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player nearestPlayer = null;
                double nearestDistance = Double.MAX_VALUE;

                for (Player player : onlinePlayers) {
                    double distance = zombie.getLocation().distance(player.getLocation());
                    if (distance < nearestDistance) {
                        nearestDistance = distance;
                        nearestPlayer = player;
                    }
                }

                if (nearestPlayer != null) {
                    Location targetLoc = nearestPlayer.getLocation();

                    // 计算瞬移位置（玩家附近2-3格范围）
                    double offsetX = (Math.random() - 0.5) * 6;
                    double offsetZ = (Math.random() - 0.5) * 6;
                    Location teleportLoc = targetLoc.clone().add(offsetX, 0, offsetZ);

                    // 确保瞬移位置有效
                    while (!teleportLoc.getBlock().getType().isSolid() && teleportLoc.getBlockY() > 0) {
                        teleportLoc.setY(teleportLoc.getY() - 1);
                    }
                    teleportLoc.setY(teleportLoc.getY() + 1);

                    // 瞬移僵尸
                    zombie.teleport(teleportLoc);

                    // 瞬移效果
                    zombie.getWorld().spawnParticle(Particle.PORTAL, teleportLoc, 30, 1.0, 1.0, 1.0, 0.1);
                    zombie.getWorld().spawnParticle(Particle.SMOKE, teleportLoc, 20, 0.5, 0.5, 0.5, 0.1);
                    zombie.getWorld().playSound(teleportLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);

                    // 瞬移后立即对最近玩家造成高额伤害
                    nearestPlayer.damage(teleportDamage, zombie);

                    // 攻击音效
                    nearestPlayer.getWorld().playSound(nearestPlayer.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 0.8f);
                    nearestPlayer.getWorld().playSound(nearestPlayer.getLocation(), Sound.ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR, 1.0f, 0.6f);

                    // 攻击粒子效果
                    nearestPlayer.getWorld().spawnParticle(Particle.CRIT, nearestPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                    if (debugMode) {
                        logger.info("变异暗影僵尸瞬移到玩家 " + nearestPlayer.getName() + " 附近并造成了 " + teleportDamage + " 点伤害");
                    }
                }
            }
        }.runTaskTimer(plugin, 100, teleportInterval); // 5秒后开始，按配置间隔执行

        mutantShadowTeleportTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异暗影僵尸瞬移攻击任务，间隔: " + teleportInterval + " ticks");
        }
    }

    /**
     * 启动变异暗影僵尸召唤任务
     */
    private void startMutantShadowSummonTask(Zombie zombie, int summonInterval, boolean useUserCustom) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantShadowSummonTasks.remove(zombie);
                    return;
                }

                logger.info("变异暗影僵尸开始召唤暗影僵尸");

                try {
                    // 计算召唤位置
                    Location summonLoc = zombie.getLocation().clone();
                    double offsetX = (Math.random() - 0.5) * 8;
                    double offsetZ = (Math.random() - 0.5) * 8;
                    summonLoc.add(offsetX, 0, offsetZ);

                    // 确保生成位置有效
                    while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                        summonLoc.setY(summonLoc.getY() - 1);
                    }
                    summonLoc.setY(summonLoc.getY() + 1);

                    Zombie summonedZombie = null;

                    if (useUserCustom) {
                        // 使用双系统召唤（用户配置版本）
                        if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                            if (dzPlugin.getDualZombieSystemManager() != null) {
                                summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, "id15");
                                logger.info("使用双系统召唤了用户配置版本的暗影僵尸");
                            }
                        }
                    } else {
                        // 使用原有系统召唤（默认版本）
                        if (originalCustomZombie != null) {
                            summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, "id15");
                            logger.info("使用原有系统召唤了默认版本的暗影僵尸");
                        }
                    }

                    if (summonedZombie != null) {
                        // 显示召唤效果
                        zombie.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 20, 0.5, 0.5, 0.5, 0.1);
                        zombie.getWorld().spawnParticle(Particle.SMOKE, summonLoc, 15, 0.5, 0.5, 0.5, 0.1);
                        zombie.getWorld().playSound(summonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                        logger.info("变异暗影僵尸召唤了一个暗影僵尸");
                    }

                } catch (Exception e) {
                    logger.warning("变异暗影僵尸召唤暗影僵尸失败: " + e.getMessage());
                }
            }
        }.runTaskTimer(plugin, 200, summonInterval); // 10秒后开始，按配置间隔执行

        mutantShadowSummonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异暗影僵尸召唤任务，间隔: " + summonInterval + " ticks");
        }
    }

    /**
     * 启动变异暗影僵尸全局负面效果任务
     */
    private void startMutantShadowDebuffTask(Zombie zombie, int debuffInterval, int debuffDuration, int blindnessLevel, int nauseaLevel) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    mutantShadowDebuffTasks.remove(zombie);
                    return;
                }

                // 对所有在线玩家施加负面效果
                for (Player player : plugin.getServer().getOnlinePlayers()) {
                    // 施加失明效果
                    player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, debuffDuration, blindnessLevel));

                    // 施加反胃效果
                    player.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, debuffDuration, nauseaLevel));

                    // 负面效果粒子
                    player.getWorld().spawnParticle(Particle.WITCH, player.getLocation().add(0, 1, 0), 8, 0.5, 0.5, 0.5, 0.1);
                    player.getWorld().spawnParticle(Particle.SMOKE, player.getLocation().add(0, 1, 0), 5, 0.3, 0.3, 0.3, 0.1);

                    // 负面效果音效
                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_WITCH_AMBIENT, 0.5f, 0.5f);

                    if (debugMode) {
                        logger.info("变异暗影僵尸对玩家 " + player.getName() + " 施加了失明" + (blindnessLevel + 1) + "和反胃" + (nauseaLevel + 1) + "效果");
                    }
                }

                // 全局负面效果音效
                zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.3f);

                if (debugMode) {
                    logger.info("变异暗影僵尸释放了全局负面效果");
                }
            }
        }.runTaskTimer(plugin, 40, debuffInterval); // 2秒后开始，按配置间隔执行

        mutantShadowDebuffTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异暗影僵尸全局负面效果任务，间隔: " + debuffInterval + " ticks，持续: " + debuffDuration + " ticks");
        }
    }

    /**
     * 启用变异博士技能（ID25）- 委托给专门的MutantDoctorSkillHandler
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableMutantDoctorZombieSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        // 委托给专门的变异博士技能处理器
        mutantDoctorSkillHandler.enableSkills(zombie, zombieId, config);
    }

    /**
     * 清理所有任务
     */
    public void cleanup() {
        explosionTasks.values().forEach(BukkitTask::cancel);
        explosionTasks.clear();

        poisonArrowTasks.values().forEach(BukkitTask::cancel);
        poisonArrowTasks.clear();

        electricTasks.values().forEach(BukkitTask::cancel);
        electricTasks.clear();

        freezingTasks.values().forEach(BukkitTask::cancel);
        freezingTasks.clear();

        shadowTasks.values().forEach(BukkitTask::cancel);
        shadowTasks.clear();

        lightningTasks.values().forEach(BukkitTask::cancel);
        lightningTasks.clear();

        scientistTasks.values().forEach(BukkitTask::cancel);
        scientistTasks.clear();

        scientistGlobalDamageTasks.values().forEach(BukkitTask::cancel);
        scientistGlobalDamageTasks.clear();

        mageLightningTasks.values().forEach(BukkitTask::cancel);
        mageLightningTasks.clear();

        mageEffectTasks.values().forEach(BukkitTask::cancel);
        mageEffectTasks.clear();

        mageSummonTasks.values().forEach(BukkitTask::cancel);
        mageSummonTasks.clear();

        mageParticleTasks.values().forEach(BukkitTask::cancel);
        mageParticleTasks.clear();

        balloonTasks.values().forEach(BukkitTask::cancel);
        balloonTasks.clear();

        fogTasks.values().forEach(BukkitTask::cancel);
        fogTasks.clear();

        mutantThunderGlobalLightningTasks.values().forEach(BukkitTask::cancel);
        mutantThunderGlobalLightningTasks.clear();

        mutantThunderTeleportTasks.values().forEach(BukkitTask::cancel);
        mutantThunderTeleportTasks.clear();

        mutantThunderSummonTasks.values().forEach(BukkitTask::cancel);
        mutantThunderSummonTasks.clear();

        mutantThunderTimeSlowTasks.values().forEach(BukkitTask::cancel);
        mutantThunderTimeSlowTasks.clear();

        mutantThunderFreezeTasks.values().forEach(BukkitTask::cancel);
        mutantThunderFreezeTasks.clear();

        ultimateDestructionSummonTasks.values().forEach(BukkitTask::cancel);
        ultimateDestructionSummonTasks.clear();

        ultimateDestructionAuraTasks.values().forEach(BukkitTask::cancel);
        ultimateDestructionAuraTasks.clear();

        ultimateDestructionExplosionTasks.values().forEach(BukkitTask::cancel);
        ultimateDestructionExplosionTasks.clear();

        ultimateDestructionParticleTasks.values().forEach(BukkitTask::cancel);
        ultimateDestructionParticleTasks.clear();

        mutantShadowTeleportTasks.values().forEach(BukkitTask::cancel);
        mutantShadowTeleportTasks.clear();

        mutantShadowSummonTasks.values().forEach(BukkitTask::cancel);
        mutantShadowSummonTasks.clear();

        mutantShadowDebuffTasks.values().forEach(BukkitTask::cancel);
        mutantShadowDebuffTasks.clear();

        // 清理变异博士技能处理器
        if (mutantDoctorSkillHandler != null) {
            mutantDoctorSkillHandler.cleanup();
        }

        scientistDamageReceived.clear();
    }
}
