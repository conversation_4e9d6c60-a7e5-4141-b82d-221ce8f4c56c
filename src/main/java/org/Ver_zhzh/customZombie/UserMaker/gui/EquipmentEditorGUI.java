package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * IDZ怪物装备编辑器GUI
 */
public class EquipmentEditorGUI implements Listener {
    
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final Logger logger;
    private final EquipmentSelectorGUI equipmentSelector;
    
    // GUI配置
    private static final String GUI_TITLE = "§6装备编辑器";
    private static final int GUI_SIZE = 54;
    
    // 装备槽位定义
    private static final int MAIN_HAND_SLOT = 10;
    private static final int OFF_HAND_SLOT = 12;
    private static final int HELMET_SLOT = 14;
    private static final int CHESTPLATE_SLOT = 16;
    private static final int LEGGINGS_SLOT = 24;
    private static final int BOOTS_SLOT = 26;
    
    // 控制按钮槽位
    private static final int BACK_SLOT = 49;
    private static final int SAVE_SLOT = 53;
    
    // 玩家编辑状态
    private final Map<Player, String> editingMonster = new HashMap<>();
    
    public EquipmentEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        this.equipmentSelector = new EquipmentSelectorGUI(plugin, idzManager);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开装备编辑器
     */
    public void openEquipmentEditor(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        
        editingMonster.put(player, monsterId);
        
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE + " - " + config.getDisplayName());
        
        // 填充背景
        fillBackground(gui);
        
        // 设置装备编辑按钮
        setupEquipmentButtons(gui, config);
        
        // 设置控制按钮
        setupControlButtons(gui);
        
        player.openInventory(gui);
        
        logger.info("为玩家 " + player.getName() + " 打开装备编辑器: " + monsterId);
    }
    
    /**
     * 设置装备编辑按钮
     */
    private void setupEquipmentButtons(Inventory gui, IDZMonsterConfig config) {
        // 主手武器
        ItemStack mainHandButton = createEquipmentButton(
            config.getMainHand() != null ? config.getMainHand().getType() : Material.WOODEN_SWORD,
            "§e⚔ 主手武器",
            config.getMainHand(),
            Arrays.asList(
                "§7当前装备: §e" + (config.getMainHand() != null ? config.getMainHand().getType().name() : "无"),
                "",
                "§e点击设置主手武器"
            ));
        gui.setItem(MAIN_HAND_SLOT, mainHandButton);
        
        // 副手物品
        ItemStack offHandButton = createEquipmentButton(
            config.getOffHand() != null ? config.getOffHand().getType() : Material.SHIELD,
            "§e🛡 副手物品",
            config.getOffHand(),
            Arrays.asList(
                "§7当前装备: §e" + (config.getOffHand() != null ? config.getOffHand().getType().name() : "无"),
                "",
                "§e点击设置副手物品"
            ));
        gui.setItem(OFF_HAND_SLOT, offHandButton);
        
        // 头盔
        ItemStack helmetButton = createEquipmentButton(
            config.getHelmet() != null ? config.getHelmet().getType() : Material.IRON_HELMET,
            "§9⛑ 头盔",
            config.getHelmet(),
            Arrays.asList(
                "§7当前装备: §9" + (config.getHelmet() != null ? config.getHelmet().getType().name() : "无"),
                "",
                "§e点击设置头盔"
            ));
        gui.setItem(HELMET_SLOT, helmetButton);
        
        // 胸甲
        ItemStack chestplateButton = createEquipmentButton(
            config.getChestplate() != null ? config.getChestplate().getType() : Material.IRON_CHESTPLATE,
            "§9🛡 胸甲",
            config.getChestplate(),
            Arrays.asList(
                "§7当前装备: §9" + (config.getChestplate() != null ? config.getChestplate().getType().name() : "无"),
                "",
                "§e点击设置胸甲"
            ));
        gui.setItem(CHESTPLATE_SLOT, chestplateButton);
        
        // 护腿
        ItemStack leggingsButton = createEquipmentButton(
            config.getLeggings() != null ? config.getLeggings().getType() : Material.IRON_LEGGINGS,
            "§9👖 护腿",
            config.getLeggings(),
            Arrays.asList(
                "§7当前装备: §9" + (config.getLeggings() != null ? config.getLeggings().getType().name() : "无"),
                "",
                "§e点击设置护腿"
            ));
        gui.setItem(LEGGINGS_SLOT, leggingsButton);
        
        // 靴子
        ItemStack bootsButton = createEquipmentButton(
            config.getBoots() != null ? config.getBoots().getType() : Material.IRON_BOOTS,
            "§9👢 靴子",
            config.getBoots(),
            Arrays.asList(
                "§7当前装备: §9" + (config.getBoots() != null ? config.getBoots().getType().name() : "无"),
                "",
                "§e点击设置靴子"
            ));
        gui.setItem(BOOTS_SLOT, bootsButton);
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回",
            Arrays.asList("§7返回主编辑界面"));
        gui.setItem(BACK_SLOT, backButton);
        
        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存",
            Arrays.asList("§7保存装备配置"));
        gui.setItem(SAVE_SLOT, saveButton);
    }
    
    /**
     * 创建装备按钮
     */
    private ItemStack createEquipmentButton(Material material, String name, ItemStack equipment, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, background);
            }
        }
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE)) {
            return;
        }

        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        String monsterId = editingMonster.get(player);
        
        if (monsterId == null) {
            return;
        }
        
        if (slot == BACK_SLOT) {
            // 返回主编辑界面
            returnToMainEditor(player, monsterId);
        } else if (slot == SAVE_SLOT) {
            // 保存配置
            saveEquipmentConfig(player, monsterId);
        } else if (isEquipmentSlot(slot)) {
            // 装备编辑
            openEquipmentSelector(player, slot, monsterId);
        }
    }
    
    /**
     * 检查是否是装备槽位
     */
    private boolean isEquipmentSlot(int slot) {
        return slot == MAIN_HAND_SLOT || slot == OFF_HAND_SLOT || 
               slot == HELMET_SLOT || slot == CHESTPLATE_SLOT || 
               slot == LEGGINGS_SLOT || slot == BOOTS_SLOT;
    }
    
    /**
     * 打开装备选择器
     */
    private void openEquipmentSelector(Player player, int slot, String monsterId) {
        equipmentSelector.openEquipmentSelector(player, monsterId, slot);
    }
    
    /**
     * 获取装备类型名称
     */
    private String getEquipmentTypeName(int slot) {
        switch (slot) {
            case MAIN_HAND_SLOT: return "主手武器";
            case OFF_HAND_SLOT: return "副手物品";
            case HELMET_SLOT: return "头盔";
            case CHESTPLATE_SLOT: return "胸甲";
            case LEGGINGS_SLOT: return "护腿";
            case BOOTS_SLOT: return "靴子";
            default: return "未知装备";
        }
    }
    
    /**
     * 返回主编辑界面
     */
    private void returnToMainEditor(Player player, String monsterId) {
        editingMonster.remove(player);
        player.closeInventory();
        
        // 重新打开主编辑界面
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            idzManager.getGuiManager().openMainEditor(player, monsterId);
        }, 1L);
    }
    
    /**
     * 保存装备配置
     */
    private void saveEquipmentConfig(Player player, String monsterId) {
        editingMonster.remove(player);
        player.closeInventory();
        player.sendMessage(ChatColor.GREEN + "装备配置已保存！");
    }
    
    /**
     * 处理GUI关闭事件
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        if (title.startsWith(GUI_TITLE)) {
            // 延迟清理，避免在切换GUI时误清理
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (!player.getOpenInventory().getTitle().contains("IDZ")) {
                    cleanupPlayer(player);
                }
            }, 1L);
        }
    }
    
    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
    }
}
