package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;
import org.Ver_zhzh.customZombie.ParticleHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

/**
 * 粒子效果编辑器GUI
 * 用于编辑IDZ怪物的粒子特效配置
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ParticleEditorGUI implements Listener {
    
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final Logger logger;
    private final ParticleHelper particleHelper;
    
    // GUI配置
    private static final String GUI_TITLE = "§6粒子特效编辑器";
    private static final int GUI_SIZE = 54;
    
    // 玩家编辑状态
    private final Map<Player, String> editingMonster = new HashMap<>();
    private final Map<Player, String> waitingForInput = new HashMap<>();
    private final Map<Player, String> inputType = new HashMap<>();
    private final Map<Player, Set<String>> selectedParticles = new HashMap<>();
    private final Map<Player, BukkitRunnable> previewTasks = new HashMap<>();
    private final Map<Player, String> editingParticleType = new HashMap<>(); // 当前编辑的粒子类型
    
    // 第1行粒子类型槽位 (0-8)
    private static final int NONE_SLOT = 0;
    private static final int FLAME_SLOT = 1;
    private static final int SMOKE_SLOT = 2;
    private static final int HEART_SLOT = 3;
    private static final int VILLAGER_HAPPY_SLOT = 4;
    private static final int CRIT_SLOT = 5;
    private static final int ENCHANTED_HIT_SLOT = 6;
    private static final int EXPLOSION_SLOT = 7;
    private static final int PORTAL_SLOT = 8;

    // 第2行粒子类型槽位 (9-17)
    private static final int ENCHANT_SLOT = 9;
    private static final int WITCH_SLOT = 10;
    private static final int DRIP_WATER_SLOT = 11;
    private static final int DRIP_LAVA_SLOT = 12;
    private static final int ANGRY_VILLAGER_SLOT = 13;
    private static final int NOTE_SLOT = 14;
    private static final int CLOUD_SLOT = 15;
    private static final int LAVA_SLOT = 16;
    private static final int DUST_SLOT = 17;

    // 第3行参数配置槽位 (18-26)
    private static final int COUNT_DECREASE_SLOT = 18;
    private static final int COUNT_INCREASE_SLOT = 19;
    private static final int RANGE_DECREASE_SLOT = 20;
    private static final int RANGE_INCREASE_SLOT = 21;
    private static final int INTERVAL_DECREASE_SLOT = 22;
    private static final int INTERVAL_INCREASE_SLOT = 23;
    private static final int PREVIEW_TIME_DECREASE_SLOT = 24;
    private static final int PREVIEW_TIME_INCREASE_SLOT = 25;
    private static final int CLEAR_ALL_SLOT = 26;

    // 第4行当前选择显示槽位 (27-35)
    private static final int SELECTED_DISPLAY_START = 27;
    private static final int SELECTED_DISPLAY_END = 35;

    // 第5行控制按钮槽位 (45-53)
    private static final int PREVIEW_SLOT = 45;
    private static final int STOP_PREVIEW_SLOT = 46;
    private static final int BACK_SLOT = 49;
    private static final int SAVE_SLOT = 53;
    
    public ParticleEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        this.particleHelper = new ParticleHelper(plugin);
        
        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开粒子效果编辑器
     */
    public void openParticleEditor(Player player, String monsterId) {
        openParticleEditor(player, monsterId, true);
    }

    /**
     * 打开粒子效果编辑器
     *
     * @param player 玩家
     * @param monsterId 怪物ID
     * @param resetSelection 是否重置选择状态
     */
    public void openParticleEditor(Player player, String monsterId, boolean resetSelection) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }

        editingMonster.put(player, monsterId);

        // 根据参数决定是否重置选择状态
        if (resetSelection) {
            // 初始化选择的粒子集合
            Set<String> selected = selectedParticles.computeIfAbsent(player, k -> new HashSet<>());
            selected.clear();
            selected.addAll(config.getParticleTypes());

            if (logger != null) {
                logger.info("为玩家 " + player.getName() + " 重置粒子选择状态，加载了 " + selected.size() + " 个粒子类型");
            }
        } else {
            // 确保选择集合存在，但不重置
            selectedParticles.computeIfAbsent(player, k -> new HashSet<>());
        }

        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE + " - " + config.getDisplayName());

        // 填充背景
        fillBackground(gui);

        // 设置粒子类型选择按钮
        setupParticleTypeButtons(gui, config);

        // 设置参数配置按钮
        setupParameterButtons(gui, config);

        // 设置当前选择显示
        setupSelectedParticlesDisplay(gui, player);

        // 设置控制按钮
        setupControlButtons(gui);

        player.openInventory(gui);

        logger.info("为玩家 " + player.getName() + " 打开增强粒子效果编辑器: " + monsterId);
    }
    
    /**
     * 设置粒子类型选择按钮（18种粒子类型）
     */
    private void setupParticleTypeButtons(Inventory gui, IDZMonsterConfig config) {
        Player player = null;
        // 从editingMonster中找到当前玩家
        for (Map.Entry<Player, String> entry : editingMonster.entrySet()) {
            if (entry.getValue().equals(config.getMonsterId())) {
                player = entry.getKey();
                break;
            }
        }

        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());

        // 第1行粒子类型 (0-8)
        gui.setItem(NONE_SLOT, createParticleTypeButton(Material.BARRIER, "§c无粒子效果", "NONE", selected,
            Arrays.asList("§7禁用粒子效果", "§7怪物将不会产生任何粒子")));

        gui.setItem(FLAME_SLOT, createParticleTypeButton(Material.FIRE_CHARGE, "§6火焰粒子", "FLAME", selected,
            Arrays.asList("§7产生火焰粒子效果", "§7适合火系怪物")));

        gui.setItem(SMOKE_SLOT, createParticleTypeButton(Material.COAL, "§8烟雾粒子", "SMOKE", selected,
            Arrays.asList("§7产生烟雾粒子效果", "§7营造神秘氛围")));

        gui.setItem(HEART_SLOT, createParticleTypeButton(Material.PINK_DYE, "§d爱心粒子", "HEART", selected,
            Arrays.asList("§7产生爱心粒子效果", "§7适合友好型怪物")));

        gui.setItem(VILLAGER_HAPPY_SLOT, createParticleTypeButton(Material.EMERALD, "§a开心粒子", "VILLAGER_HAPPY", selected,
            Arrays.asList("§7产生绿色开心粒子", "§7表示正面状态")));

        gui.setItem(CRIT_SLOT, createParticleTypeButton(Material.DIAMOND_SWORD, "§e暴击粒子", "CRIT", selected,
            Arrays.asList("§7产生暴击粒子效果", "§7适合战斗型怪物")));

        gui.setItem(ENCHANTED_HIT_SLOT, createParticleTypeButton(Material.ENCHANTED_BOOK, "§b附魔粒子", "ENCHANTED_HIT", selected,
            Arrays.asList("§7产生附魔粒子效果", "§7魔法系怪物首选")));

        gui.setItem(EXPLOSION_SLOT, createParticleTypeButton(Material.TNT, "§c爆炸粒子", "EXPLOSION_NORMAL", selected,
            Arrays.asList("§7产生爆炸粒子效果", "§7震撼的视觉冲击")));

        gui.setItem(PORTAL_SLOT, createParticleTypeButton(Material.OBSIDIAN, "§5传送门粒子", "PORTAL", selected,
            Arrays.asList("§7产生传送门粒子效果", "§7神秘的紫色粒子")));

        // 第2行粒子类型 (9-17)
        gui.setItem(ENCHANT_SLOT, createParticleTypeButton(Material.ENCHANTING_TABLE, "§b附魔台粒子", "ENCHANTMENT_TABLE", selected,
            Arrays.asList("§7产生附魔台粒子效果", "§7魔法能量环绕")));

        gui.setItem(WITCH_SLOT, createParticleTypeButton(Material.CAULDRON, "§5女巫粒子", "SPELL_WITCH", selected,
            Arrays.asList("§7产生女巫法术粒子", "§7紫色魔法效果")));

        gui.setItem(DRIP_WATER_SLOT, createParticleTypeButton(Material.WATER_BUCKET, "§9滴水粒子", "DRIP_WATER", selected,
            Arrays.asList("§7产生滴水粒子效果", "§7清凉的水滴")));

        gui.setItem(DRIP_LAVA_SLOT, createParticleTypeButton(Material.LAVA_BUCKET, "§6滴岩浆粒子", "DRIP_LAVA", selected,
            Arrays.asList("§7产生滴岩浆粒子效果", "§7炽热的岩浆滴")));

        gui.setItem(ANGRY_VILLAGER_SLOT, createParticleTypeButton(Material.IRON_SWORD, "§c愤怒粒子", "VILLAGER_ANGRY", selected,
            Arrays.asList("§7产生愤怒村民粒子", "§7表示敌意状态")));

        gui.setItem(NOTE_SLOT, createParticleTypeButton(Material.NOTE_BLOCK, "§e音符粒子", "NOTE", selected,
            Arrays.asList("§7产生音符粒子效果", "§7彩色音符飞舞")));

        gui.setItem(CLOUD_SLOT, createParticleTypeButton(Material.WHITE_WOOL, "§f云朵粒子", "CLOUD", selected,
            Arrays.asList("§7产生云朵粒子效果", "§7轻盈的白色云朵")));

        gui.setItem(LAVA_SLOT, createParticleTypeButton(Material.MAGMA_BLOCK, "§6岩浆粒子", "LAVA", selected,
            Arrays.asList("§7产生岩浆粒子效果", "§7炽热的岩浆泡泡")));

        gui.setItem(DUST_SLOT, createParticleTypeButton(Material.REDSTONE, "§4红石粒子", "REDSTONE", selected,
            Arrays.asList("§7产生红石粒子效果", "§7红色能量粉尘")));
    }
    
    /**
     * 创建粒子类型按钮（支持多选）
     */
    private ItemStack createParticleTypeButton(Material material, String name, String particleType, Set<String> selectedTypes, List<String> description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        // 创建可变的描述列表副本
        List<String> mutableDescription = new ArrayList<>(description);

        // 检查是否已选择
        boolean isSelected = selectedTypes.contains(particleType);

        if (isSelected) {
            meta.setDisplayName(name + " §a✓");
            mutableDescription.add("");
            mutableDescription.add("§a已选择此粒子类型");
            mutableDescription.add("§e点击取消选择");
            // 添加绿色边框效果
            button.addUnsafeEnchantment(org.bukkit.enchantments.Enchantment.UNBREAKING, 1);
            meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
        } else {
            meta.setDisplayName(name);
            mutableDescription.add("");
            if (particleType.equals("NONE")) {
                mutableDescription.add("§e点击清除所有粒子效果");
            } else {
                mutableDescription.add("§e点击添加此粒子类型");
                mutableDescription.add("§7最多可选择5种粒子");
            }
        }

        meta.setLore(mutableDescription);
        button.setItemMeta(meta);
        return button;
    }
    
    /**
     * 设置参数配置按钮（支持增减）
     */
    private void setupParameterButtons(Inventory gui, IDZMonsterConfig config) {
        // 粒子数量 - 减少按钮
        ItemStack countDecreaseButton = createButton(Material.RED_CONCRETE,
            "§c➖ 数量减少",
            Arrays.asList(
                "§7当前数量: §b" + config.getParticleCount() + " §7个",
                "§7最小值: 1个",
                "",
                "§c左键: -1  右键: -10  中键: -50"
            ));
        gui.setItem(COUNT_DECREASE_SLOT, countDecreaseButton);

        // 粒子数量 - 增加按钮
        ItemStack countIncreaseButton = createButton(Material.GREEN_CONCRETE,
            "§a➕ 数量增加",
            Arrays.asList(
                "§7当前数量: §b" + config.getParticleCount() + " §7个",
                "§7最大值: 100个",
                "",
                "§a左键: +1  右键: +10  中键: +50"
            ));
        gui.setItem(COUNT_INCREASE_SLOT, countIncreaseButton);

        // 粒子范围 - 减少按钮
        ItemStack rangeDecreaseButton = createButton(Material.RED_CONCRETE,
            "§c➖ 范围减少",
            Arrays.asList(
                "§7当前范围: §b" + String.format("%.1f", config.getParticleRange()) + " §7格",
                "§7最小值: 0.1格",
                "",
                "§c左键: -0.1  右键: -0.5  中键: -1.0"
            ));
        gui.setItem(RANGE_DECREASE_SLOT, rangeDecreaseButton);

        // 粒子范围 - 增加按钮
        ItemStack rangeIncreaseButton = createButton(Material.GREEN_CONCRETE,
            "§a➕ 范围增加",
            Arrays.asList(
                "§7当前范围: §b" + String.format("%.1f", config.getParticleRange()) + " §7格",
                "§7最大值: 10.0格",
                "",
                "§a左键: +0.1  右键: +0.5  中键: +1.0"
            ));
        gui.setItem(RANGE_INCREASE_SLOT, rangeIncreaseButton);

        // 粒子间隔 - 减少按钮
        ItemStack intervalDecreaseButton = createButton(Material.RED_CONCRETE,
            "§c➖ 间隔减少",
            Arrays.asList(
                "§7当前间隔: §b" + config.getParticleInterval() + " §7tick",
                "§7最小值: 1 tick",
                "",
                "§c左键: -1  右键: -5  中键: -20"
            ));
        gui.setItem(INTERVAL_DECREASE_SLOT, intervalDecreaseButton);

        // 粒子间隔 - 增加按钮
        ItemStack intervalIncreaseButton = createButton(Material.GREEN_CONCRETE,
            "§a➕ 间隔增加",
            Arrays.asList(
                "§7当前间隔: §b" + config.getParticleInterval() + " §7tick",
                "§7最大值: 100 tick",
                "§7(20 tick = 1秒)",
                "",
                "§a左键: +1  右键: +5  中键: +20"
            ));
        gui.setItem(INTERVAL_INCREASE_SLOT, intervalIncreaseButton);

        // 预览时间 - 减少按钮
        ItemStack previewDecreaseButton = createButton(Material.RED_CONCRETE,
            "§c➖ 预览时间减少",
            Arrays.asList(
                "§7当前预览时间: §b" + config.getPreviewDuration() + " §7秒",
                "§7最小值: 1秒",
                "",
                "§c左键: -1秒  右键: -5秒"
            ));
        gui.setItem(PREVIEW_TIME_DECREASE_SLOT, previewDecreaseButton);

        // 预览时间 - 增加按钮
        ItemStack previewIncreaseButton = createButton(Material.GREEN_CONCRETE,
            "§a➕ 预览时间增加",
            Arrays.asList(
                "§7当前预览时间: §b" + config.getPreviewDuration() + " §7秒",
                "§7最大值: 30秒",
                "",
                "§a左键: +1秒  右键: +5秒"
            ));
        gui.setItem(PREVIEW_TIME_INCREASE_SLOT, previewIncreaseButton);

        // 清除所有选择按钮
        ItemStack clearAllButton = createButton(Material.BARRIER,
            "§c🧹 清除所有",
            Arrays.asList(
                "§7清除所有选择的粒子效果",
                "§7重置为无粒子状态",
                "",
                "§e点击清除所有选择"
            ));
        gui.setItem(CLEAR_ALL_SLOT, clearAllButton);
    }
    
    /**
     * 设置当前选择的粒子显示区域
     */
    private void setupSelectedParticlesDisplay(Inventory gui, Player player) {
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());

        // 清空显示区域
        for (int i = SELECTED_DISPLAY_START; i <= SELECTED_DISPLAY_END; i++) {
            gui.setItem(i, null);
        }

        if (selected.isEmpty()) {
            // 显示"无选择"提示
            ItemStack noSelection = createButton(Material.GRAY_STAINED_GLASS_PANE,
                "§7无选择的粒子效果",
                Arrays.asList("§7请在上方选择粒子类型"));
            gui.setItem(SELECTED_DISPLAY_START + 4, noSelection); // 居中显示
        } else {
            // 显示选择的粒子
            int slot = SELECTED_DISPLAY_START;
            for (String particleType : selected) {
                if (slot > SELECTED_DISPLAY_END) break;

                Material material = getParticleDisplayMaterial(particleType);
                String displayName = getParticleDisplayName(particleType);

                ItemStack selectedParticle = createButton(material,
                    "§a✓ " + displayName,
                    Arrays.asList(
                        "§7已选择的粒子类型",
                        "",
                        "§e左键: 编辑粒子参数",
                        "§c右键: 移除此粒子"
                    ));

                gui.setItem(slot, selectedParticle);
                slot++;
            }
        }
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 预览按钮
        ItemStack previewButton = createButton(Material.ENDER_EYE,
            "§a👁 开始预览",
            Arrays.asList(
                "§7预览所有选择的粒子效果",
                "§7使用当前配置参数",
                "§e点击开始预览"
            ));
        gui.setItem(PREVIEW_SLOT, previewButton);

        // 停止预览按钮
        ItemStack stopPreviewButton = createButton(Material.REDSTONE_BLOCK,
            "§c⏹ 停止预览",
            Arrays.asList(
                "§7停止当前的粒子预览",
                "§e点击停止预览"
            ));
        gui.setItem(STOP_PREVIEW_SLOT, stopPreviewButton);

        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回主菜单",
            Arrays.asList("§7返回IDZ怪物主编辑器"));
        gui.setItem(BACK_SLOT, backButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存配置",
            Arrays.asList("§7保存粒子效果配置"));
        gui.setItem(SAVE_SLOT, saveButton);
    }
    
    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 填充空白区域
        for (int i = 0; i < GUI_SIZE; i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, background);
            }
        }
    }
    
    /**
     * 获取粒子类型的显示材料
     */
    private Material getParticleDisplayMaterial(String particleType) {
        switch (particleType.toUpperCase()) {
            case "FLAME": return Material.FIRE_CHARGE;
            case "SMOKE": return Material.COAL;
            case "HEART": return Material.PINK_DYE;
            case "VILLAGER_HAPPY": return Material.EMERALD;
            case "CRIT": return Material.DIAMOND_SWORD;
            case "ENCHANTED_HIT": return Material.ENCHANTED_BOOK;
            default: return Material.BARRIER;
        }
    }
    
    /**
     * 获取粒子类型的显示名称（支持18种粒子）
     */
    private String getParticleDisplayName(String particleType) {
        switch (particleType.toUpperCase()) {
            case "FLAME": return "火焰粒子";
            case "SMOKE": return "烟雾粒子";
            case "HEART": return "爱心粒子";
            case "VILLAGER_HAPPY": return "开心粒子";
            case "CRIT": return "暴击粒子";
            case "ENCHANTED_HIT": return "附魔粒子";
            case "EXPLOSION_NORMAL": return "爆炸粒子";
            case "PORTAL": return "传送门粒子";
            case "ENCHANTMENT_TABLE": return "附魔台粒子";
            case "SPELL_WITCH": return "女巫粒子";
            case "DRIP_WATER": return "滴水粒子";
            case "DRIP_LAVA": return "滴岩浆粒子";
            case "VILLAGER_ANGRY": return "愤怒粒子";
            case "NOTE": return "音符粒子";
            case "CLOUD": return "云朵粒子";
            case "LAVA": return "岩浆粒子";
            case "REDSTONE": return "红石粒子";
            case "NONE": return "无粒子";
            default: return "未知类型";
        }
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE) && !title.startsWith("§6粒子参数编辑器")) {
            return;
        }

        event.setCancelled(true);

        int slot = event.getRawSlot();

        // 处理粒子参数编辑器
        if (title.startsWith("§6粒子参数编辑器")) {
            handleParticleParameterEditorClick(player, slot, event.getClick());
            return;
        }
        String monsterId = editingMonster.get(player);

        if (monsterId == null) {
            return;
        }

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            return;
        }

        // 处理粒子类型选择 (第1-2行，槽位0-17)
        if (slot >= 0 && slot <= 17) {
            handleParticleTypeSelection(player, slot, config);
        }
        // 处理参数配置 (第3行，槽位18-26)
        else if (slot >= 18 && slot <= 26) {
            handleParameterAdjustment(player, slot, config, event.getClick());
        }
        // 处理选择的粒子显示区域点击 (第4行，槽位27-35)
        else if (slot >= SELECTED_DISPLAY_START && slot <= SELECTED_DISPLAY_END) {
            handleSelectedParticleClick(player, slot, config, event.getClick());
        }
        // 处理控制按钮 (第5行，槽位45-53)
        else if (slot == PREVIEW_SLOT) {
            startParticlePreview(player, config);
        }
        else if (slot == STOP_PREVIEW_SLOT) {
            stopParticlePreview(player);
        }
        else if (slot == BACK_SLOT) {
            returnToMainEditor(player, monsterId);
        }
        else if (slot == SAVE_SLOT) {
            saveConfiguration(player, monsterId);
        }
    }

    /**
     * 处理粒子类型选择（支持多选）
     */
    private void handleParticleTypeSelection(Player player, int slot, IDZMonsterConfig config) {
        String particleType = getParticleTypeBySlot(slot);
        if (particleType == null) return;

        Set<String> selected = selectedParticles.computeIfAbsent(player, k -> new HashSet<>());

        if (particleType.equals("NONE")) {
            // 清除所有选择
            selected.clear();
            player.sendMessage(ChatColor.YELLOW + "✨ 已清除所有粒子效果");
            player.sendTitle("", ChatColor.YELLOW + "已清除所有粒子效果", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.5f);
        } else {
            // 切换选择状态
            if (selected.contains(particleType)) {
                selected.remove(particleType);
                player.sendMessage(ChatColor.RED + "➖ 已移除粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.RED + "➖ 移除 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);
            } else {
                if (selected.size() >= 5) {
                    player.sendMessage(ChatColor.RED + "❌ 最多只能选择5种粒子效果！");
                    player.sendTitle("", ChatColor.RED + "❌ 最多只能选择5种粒子效果", 10, 30, 10);
                    player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                    return;
                }
                selected.add(particleType);
                player.sendMessage(ChatColor.GREEN + "➕ 已添加粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.GREEN + "➕ 添加 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                // 播放简短预览
                playQuickPreview(player, particleType);
            }
        }

        // 重新打开GUI以刷新显示，但不重置选择状态
        openParticleEditor(player, editingMonster.get(player), false);
    }

    /**
     * 根据槽位获取粒子类型
     */
    private String getParticleTypeBySlot(int slot) {
        switch (slot) {
            case NONE_SLOT: return "NONE";
            case FLAME_SLOT: return "FLAME";
            case SMOKE_SLOT: return "SMOKE";
            case HEART_SLOT: return "HEART";
            case VILLAGER_HAPPY_SLOT: return "VILLAGER_HAPPY";
            case CRIT_SLOT: return "CRIT";
            case ENCHANTED_HIT_SLOT: return "ENCHANTED_HIT";
            case EXPLOSION_SLOT: return "EXPLOSION_NORMAL";
            case PORTAL_SLOT: return "PORTAL";
            case ENCHANT_SLOT: return "ENCHANTMENT_TABLE";
            case WITCH_SLOT: return "SPELL_WITCH";
            case DRIP_WATER_SLOT: return "DRIP_WATER";
            case DRIP_LAVA_SLOT: return "DRIP_LAVA";
            case ANGRY_VILLAGER_SLOT: return "VILLAGER_ANGRY";
            case NOTE_SLOT: return "NOTE";
            case CLOUD_SLOT: return "CLOUD";
            case LAVA_SLOT: return "LAVA";
            case DUST_SLOT: return "REDSTONE";
            default: return null;
        }
    }

    /**
     * 处理参数调整（新的增减按钮系统）
     */
    private void handleParameterAdjustment(Player player, int slot, IDZMonsterConfig config, org.bukkit.event.inventory.ClickType clickType) {
        switch (slot) {
            // 粒子数量调整
            case COUNT_DECREASE_SLOT:
                int countDecrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 10 :
                                   (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 50 : 1;
                adjustParticleCount(config, -countDecrease);
                break;

            case COUNT_INCREASE_SLOT:
                int countIncrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 10 :
                                   (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 50 : 1;
                adjustParticleCount(config, countIncrease);
                break;

            // 粒子范围调整
            case RANGE_DECREASE_SLOT:
                double rangeDecrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 0.5 :
                                      (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 1.0 : 0.1;
                adjustParticleRange(config, -rangeDecrease);
                break;

            case RANGE_INCREASE_SLOT:
                double rangeIncrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 0.5 :
                                      (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 1.0 : 0.1;
                adjustParticleRange(config, rangeIncrease);
                break;

            // 粒子间隔调整
            case INTERVAL_DECREASE_SLOT:
                int intervalDecrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 5 :
                                      (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 20 : 1;
                adjustParticleInterval(config, -intervalDecrease);
                break;

            case INTERVAL_INCREASE_SLOT:
                int intervalIncrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 5 :
                                      (clickType == org.bukkit.event.inventory.ClickType.MIDDLE) ? 20 : 1;
                adjustParticleInterval(config, intervalIncrease);
                break;

            // 预览时间调整
            case PREVIEW_TIME_DECREASE_SLOT:
                int previewDecrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 5 : 1;
                adjustPreviewDuration(config, -previewDecrease);
                break;

            case PREVIEW_TIME_INCREASE_SLOT:
                int previewIncrease = (clickType == org.bukkit.event.inventory.ClickType.RIGHT) ? 5 : 1;
                adjustPreviewDuration(config, previewIncrease);
                break;

            // 清除所有选择
            case CLEAR_ALL_SLOT:
                selectedParticles.computeIfAbsent(player, k -> new HashSet<>()).clear();
                player.sendMessage(ChatColor.YELLOW + "已清除所有粒子选择");
                break;

            default:
                return;
        }

        // 重新打开GUI以刷新显示，但不重置选择状态
        openParticleEditor(player, editingMonster.get(player), false);
    }

    /**
     * 调整粒子数量
     */
    private void adjustParticleCount(IDZMonsterConfig config, int change) {
        int newCount = Math.max(1, Math.min(100, config.getParticleCount() + change));
        config.setParticleCount(newCount);
    }

    /**
     * 调整粒子范围
     */
    private void adjustParticleRange(IDZMonsterConfig config, double change) {
        double currentRange = config.getParticleRange();
        double newRange = currentRange + change;

        // 严格限制范围，防止异常值
        newRange = Math.max(0.1, Math.min(10.0, newRange));

        // 保留一位小数，避免浮点数精度问题
        newRange = Math.round(newRange * 10.0) / 10.0;

        config.setParticleRange(newRange);
    }

    /**
     * 调整粒子间隔
     */
    private void adjustParticleInterval(IDZMonsterConfig config, int change) {
        int newInterval = Math.max(1, Math.min(100, config.getParticleInterval() + change));
        config.setParticleInterval(newInterval);
    }

    /**
     * 调整预览时间
     */
    private void adjustPreviewDuration(IDZMonsterConfig config, int change) {
        int newDuration = Math.max(1, Math.min(30, config.getPreviewDuration() + change));
        config.setPreviewDuration(newDuration);
    }

    /**
     * 处理选择的粒子显示区域点击
     */
    private void handleSelectedParticleClick(Player player, int slot, IDZMonsterConfig config, org.bukkit.event.inventory.ClickType clickType) {
        Set<String> selected = selectedParticles.get(player);
        if (selected == null || selected.isEmpty()) return;

        // 计算点击的是第几个选择的粒子
        int index = slot - SELECTED_DISPLAY_START;
        if (index < 0 || index >= selected.size()) return;

        // 将Set转换为List以便按索引访问
        List<String> selectedList = new ArrayList<>(selected);
        if (index < selectedList.size()) {
            String particleType = selectedList.get(index);

            if (clickType == org.bukkit.event.inventory.ClickType.RIGHT) {
                // 右键删除粒子
                selected.remove(particleType);
                player.sendMessage(ChatColor.RED + "➖ 已移除粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.RED + "➖ 移除 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);

                // 重新打开GUI以刷新显示，但不重置选择状态
                openParticleEditor(player, editingMonster.get(player), false);
            } else if (clickType == org.bukkit.event.inventory.ClickType.LEFT) {
                // 左键编辑粒子参数
                openParticleParameterEditor(player, editingMonster.get(player), particleType);
            }
        }
    }

    /**
     * 播放快速预览（0.5秒）
     */
    private void playQuickPreview(Player player, String particleType) {
        Particle bukkitParticle = getBukkitParticle(particleType);
        if (bukkitParticle == null) return;

        try {
            org.bukkit.Location particleLocation = player.getLocation().clone().add(0, 1, 0);

            // 直接使用Bukkit的粒子生成方法
            if (bukkitParticle == Particle.DUST) {
                // 红石粒子需要特殊处理
                org.bukkit.Particle.DustOptions dustOptions = new org.bukkit.Particle.DustOptions(
                    org.bukkit.Color.RED, 1.0f);
                player.getWorld().spawnParticle(bukkitParticle, particleLocation, 5, 0.3, 0.3, 0.3, 0.1, dustOptions);
            } else {
                // 普通粒子
                player.getWorld().spawnParticle(bukkitParticle, particleLocation, 5, 0.3, 0.3, 0.3, 0.1);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("快速预览粒子生成失败: " + particleType + " - " + e.getMessage());
        }
    }

    /**
     * 请求聊天输入
     */
    private void requestInput(Player player, String type, String prompt) {
        waitingForInput.put(player, editingMonster.get(player));
        inputType.put(player, type);

        player.closeInventory();
        player.sendMessage(ChatColor.YELLOW + prompt);
        player.sendMessage(ChatColor.GRAY + "输入 'cancel' 取消修改");
    }

    /**
     * 处理聊天输入
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();

        if (!waitingForInput.containsKey(player)) {
            return;
        }

        event.setCancelled(true);

        String input = event.getMessage().trim();
        String type = inputType.get(player);
        String monsterId = waitingForInput.get(player);

        // 清理输入状态
        waitingForInput.remove(player);
        inputType.remove(player);

        if ("cancel".equalsIgnoreCase(input)) {
            player.sendMessage(ChatColor.GRAY + "已取消修改");
            // 根据当前编辑状态决定返回哪个GUI
            String particleType = editingParticleType.get(player);
            if (particleType != null) {
                // 返回粒子参数编辑器
                Bukkit.getScheduler().runTask(plugin, () -> openParticleParameterEditor(player, monsterId, particleType));
            } else {
                // 返回粒子编辑器
                Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
            }
            return;
        }

        // 处理参数输入
        processParameterInput(player, type, input, monsterId);
    }

    /**
     * 处理参数输入
     */
    private void processParameterInput(Player player, String type, String input, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "配置不存在");
            return;
        }

        try {
            switch (type) {
                case "count":
                    int count = Integer.parseInt(input);
                    if (count < 1 || count > 100) {
                        player.sendMessage(ChatColor.RED + "粒子数量必须在1-100之间！");
                    } else {
                        config.setParticleCount(count);
                        player.sendMessage(ChatColor.GREEN + "粒子数量已设置为: " + count);
                    }
                    break;

                case "range":
                    double range = Double.parseDouble(input);
                    if (range < 1.0 || range > 10.0) {
                        player.sendMessage(ChatColor.RED + "粒子范围必须在1.0-10.0之间！");
                    } else {
                        config.setParticleRange(range);
                        player.sendMessage(ChatColor.GREEN + "粒子范围已设置为: " + range);
                    }
                    break;

                case "interval":
                    int interval = Integer.parseInt(input);
                    if (interval < 5 || interval > 100) {
                        player.sendMessage(ChatColor.RED + "粒子间隔必须在5-100 tick之间！");
                    } else {
                        config.setParticleInterval(interval);
                        player.sendMessage(ChatColor.GREEN + "粒子间隔已设置为: " + interval + " tick");
                    }
                    break;

                case "duration":
                    int duration = Integer.parseInt(input);
                    if (duration < 1 || duration > 30) {
                        player.sendMessage(ChatColor.RED + "预览时长必须在1-30秒之间！");
                    } else {
                        config.setPreviewDuration(duration);
                        player.sendMessage(ChatColor.GREEN + "预览时长已设置为: " + duration + " 秒");
                    }
                    break;
            }
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "输入格式错误！请输入有效的数值。");
        }

        // 根据当前编辑状态决定返回哪个GUI
        String particleType = editingParticleType.get(player);
        if (particleType != null) {
            // 返回粒子参数编辑器
            Bukkit.getScheduler().runTask(plugin, () -> openParticleParameterEditor(player, monsterId, particleType));
        } else {
            // 返回粒子编辑器
            Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
        }
    }

    /**
     * 开始粒子预览（支持多粒子和自定义时间）
     */
    private void startParticlePreview(Player player, IDZMonsterConfig config) {
        Set<String> selected = selectedParticles.get(player);
        if (selected == null || selected.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "请先选择要预览的粒子效果");
            return;
        }

        // 停止之前的预览
        stopParticlePreview(player);

        int previewDuration = config.getPreviewDuration();
        player.sendMessage(ChatColor.GREEN + "🎬 开始预览 " + selected.size() + " 种粒子效果");
        player.sendMessage(ChatColor.GRAY + "⏱️ 预览时间: " + previewDuration + " 秒");
        player.sendTitle("", ChatColor.GREEN + "🎬 开始预览粒子效果", 10, 30, 10);
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.5f);

        // 创建预览任务
        BukkitRunnable previewTask = new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = previewDuration * 20; // 转换为tick

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    if (player.isOnline()) {
                        player.sendMessage(ChatColor.GREEN + "粒子效果预览结束");
                    }
                    previewTasks.remove(player);
                    this.cancel();
                    return;
                }

                // 为每种选择的粒子生成效果
                for (String particleType : selected) {
                    Particle bukkitParticle = getBukkitParticle(particleType);
                    if (bukkitParticle != null) {
                        try {
                            // 在玩家周围不同位置生成粒子，避免重叠
                            double angle = Math.random() * Math.PI * 2;
                            double offsetX = Math.cos(angle) * 0.5;
                            double offsetZ = Math.sin(angle) * 0.5;

                            org.bukkit.Location particleLocation = player.getLocation().clone().add(offsetX, 1, offsetZ);

                            // 直接使用Bukkit的粒子生成方法
                            if (bukkitParticle == Particle.DUST) {
                                // 红石粒子需要特殊处理
                                org.bukkit.Particle.DustOptions dustOptions = new org.bukkit.Particle.DustOptions(
                                    org.bukkit.Color.RED, 1.0f);
                                player.getWorld().spawnParticle(bukkitParticle, particleLocation,
                                    config.getParticleCount() / selected.size(),
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    0.1, dustOptions);
                            } else {
                                // 普通粒子
                                player.getWorld().spawnParticle(bukkitParticle, particleLocation,
                                    config.getParticleCount() / selected.size(),
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    0.1);
                            }
                        } catch (Exception e) {
                            // 如果粒子生成失败，记录错误但继续其他粒子
                            plugin.getLogger().warning("粒子生成失败: " + particleType + " - " + e.getMessage());
                        }
                    }
                }

                ticks += config.getParticleInterval();

                // 显示剩余时间（每秒更新一次）
                if (ticks % 20 == 0) {
                    int remainingSeconds = (maxTicks - ticks) / 20;
                    player.sendActionBar(ChatColor.YELLOW + "预览剩余时间: " + remainingSeconds + "秒");
                }
            }
        };

        previewTask.runTaskTimer(plugin, 0, config.getParticleInterval());
        previewTasks.put(player, previewTask);
    }

    /**
     * 停止粒子预览
     */
    private void stopParticlePreview(Player player) {
        BukkitRunnable task = previewTasks.remove(player);
        if (task != null && !task.isCancelled()) {
            task.cancel();
            player.sendMessage(ChatColor.YELLOW + "已停止粒子预览");
        }
    }

    /**
     * 获取对应的Bukkit粒子类型（支持18种粒子）
     */
    private Particle getBukkitParticle(String particleType) {
        switch (particleType.toUpperCase()) {
            case "FLAME": return Particle.FLAME;
            case "SMOKE": return Particle.SMOKE;
            case "HEART": return Particle.HEART;
            case "VILLAGER_HAPPY": return Particle.HAPPY_VILLAGER;
            case "CRIT": return Particle.CRIT;
            case "ENCHANTED_HIT": return Particle.ENCHANTED_HIT;
            case "EXPLOSION_NORMAL": return Particle.POOF;
            case "PORTAL": return Particle.PORTAL;
            case "ENCHANTMENT_TABLE": return Particle.ENCHANT;
            case "SPELL_WITCH": return Particle.WITCH;
            case "DRIP_WATER": return Particle.DRIPPING_WATER;
            case "DRIP_LAVA": return Particle.DRIPPING_LAVA;
            case "VILLAGER_ANGRY": return Particle.ANGRY_VILLAGER;
            case "NOTE": return Particle.NOTE;
            case "CLOUD": return Particle.CLOUD;
            case "LAVA": return Particle.LAVA;
            case "REDSTONE": return Particle.DUST;
            default: return null;
        }
    }

    /**
     * 返回主编辑器
     */
    private void returnToMainEditor(Player player, String monsterId) {
        // 清理玩家状态
        editingMonster.remove(player);
        waitingForInput.remove(player);
        inputType.remove(player);

        player.closeInventory();

        // 重新打开主编辑器
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            idzManager.getGuiManager().openMainEditor(player, monsterId);
        }, 1L);
    }

    /**
     * 保存配置（支持多粒子）
     */
    private void saveConfiguration(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，无法保存");
            logger.warning("尝试保存不存在的怪物配置: " + monsterId);
            return;
        }

        // 获取选择的粒子并保存
        Set<String> selected = selectedParticles.get(player);

        // 调试日志
        if (logger != null) {
            logger.info("玩家 " + player.getName() + " 尝试保存粒子配置:");
            logger.info("- 怪物ID: " + monsterId);
            logger.info("- 选择的粒子数量: " + (selected != null ? selected.size() : 0));
            if (selected != null && !selected.isEmpty()) {
                logger.info("- 选择的粒子类型: " + String.join(", ", selected));
            }
        }

        try {
            // 设置粒子类型到配置
            if (selected != null) {
                config.setParticleTypes(new ArrayList<>(selected));
            } else {
                config.setParticleTypes(new ArrayList<>());
            }

            // 保存配置
            boolean saveResult = idzManager.updateMonsterConfig(monsterId, config);

            if (saveResult) {
                player.sendMessage(ChatColor.GREEN + "✅ 粒子效果配置已成功保存！");
                player.sendTitle("", ChatColor.GREEN + "✅ 配置保存成功", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                player.sendMessage(ChatColor.GRAY + "配置详情:");
                if (selected == null || selected.isEmpty()) {
                    player.sendMessage(ChatColor.GRAY + "- 粒子类型: " + ChatColor.AQUA + "无粒子");
                } else {
                    player.sendMessage(ChatColor.GRAY + "- 粒子类型: " + ChatColor.AQUA + selected.size() + " 种");
                    for (String particleType : selected) {
                        player.sendMessage(ChatColor.GRAY + "  • " + ChatColor.YELLOW + getParticleDisplayName(particleType));
                    }
                }

                if (logger != null) {
                    logger.info("粒子配置保存成功: " + monsterId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "❌ 保存配置失败，请检查控制台错误信息");
                player.sendTitle("", ChatColor.RED + "❌ 保存失败", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                if (logger != null) {
                    logger.warning("粒子配置保存失败: " + monsterId);
                }
            }

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "❌ 保存配置时发生错误: " + e.getMessage());
            player.sendTitle("", ChatColor.RED + "❌ 保存出错", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

            if (logger != null) {
                logger.severe("保存粒子配置时发生异常: " + monsterId);
                e.printStackTrace();
            }
        }

        player.sendMessage(ChatColor.GRAY + "- 粒子数量: " + ChatColor.AQUA + config.getParticleCount() + " 个");
        player.sendMessage(ChatColor.GRAY + "- 粒子范围: " + ChatColor.AQUA + config.getParticleRange() + " 格");
        player.sendMessage(ChatColor.GRAY + "- 粒子间隔: " + ChatColor.AQUA + config.getParticleInterval() + " tick");
        player.sendMessage(ChatColor.GRAY + "- 预览时间: " + ChatColor.AQUA + config.getPreviewDuration() + " 秒");

        // 返回主编辑器
        returnToMainEditor(player, monsterId);
    }

    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        waitingForInput.remove(player);
        inputType.remove(player);
        selectedParticles.remove(player);

        // 停止预览任务
        stopParticlePreview(player);

        // 清理粒子参数编辑状态
        editingParticleType.remove(player);
    }

    /**
     * 打开粒子参数编辑器
     */
    private void openParticleParameterEditor(Player player, String monsterId, String particleType) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }

        editingMonster.put(player, monsterId);
        editingParticleType.put(player, particleType);

        String particleDisplayName = getParticleDisplayName(particleType);
        Inventory gui = Bukkit.createInventory(null, 54, "§6粒子参数编辑器 - " + particleDisplayName);

        // 填充背景
        fillBackground(gui);

        // 设置参数编辑按钮
        setupParticleParameterButtons(gui, config, particleType);

        // 设置控制按钮
        setupParticleParameterControlButtons(gui);

        player.openInventory(gui);

        if (logger != null) {
            logger.info("为玩家 " + player.getName() + " 打开粒子参数编辑器: " + particleType);
        }
    }

    /**
     * 设置粒子参数编辑按钮
     */
    private void setupParticleParameterButtons(Inventory gui, IDZMonsterConfig config, String particleType) {
        String particleDisplayName = getParticleDisplayName(particleType);

        // 粒子数量参数 (槽位10)
        ItemStack countButton = createParameterEditButton(
            Material.EMERALD,
            "§a粒子数量",
            config.getParticleCount(),
            "个",
            Arrays.asList(
                "§7控制每次生成的粒子数量",
                "§7数量越多效果越密集",
                "§7范围: 1-100"
            )
        );
        gui.setItem(10, countButton);

        // 粒子范围参数 (槽位11)
        ItemStack rangeButton = createParameterEditButton(
            Material.COMPASS,
            "§b粒子范围",
            config.getParticleRange(),
            "格",
            Arrays.asList(
                "§7控制粒子散布的范围",
                "§7范围越大粒子分布越广",
                "§7范围: 0.5-10.0"
            )
        );
        gui.setItem(11, rangeButton);

        // 粒子间隔参数 (槽位12)
        ItemStack intervalButton = createParameterEditButton(
            Material.CLOCK,
            "§e粒子间隔",
            config.getParticleInterval(),
            "tick",
            Arrays.asList(
                "§7控制粒子生成的时间间隔",
                "§7间隔越小生成越频繁",
                "§7范围: 5-100 (20tick=1秒)"
            )
        );
        gui.setItem(12, intervalButton);

        // 预览时长参数 (槽位13)
        ItemStack durationButton = createParameterEditButton(
            Material.REPEATER,
            "§d预览时长",
            config.getPreviewDuration(),
            "秒",
            Arrays.asList(
                "§7控制预览粒子的持续时间",
                "§7时长越长预览越久",
                "§7范围: 1-30"
            )
        );
        gui.setItem(13, durationButton);
    }

    /**
     * 创建参数编辑按钮
     */
    private ItemStack createParameterEditButton(Material material, String name, Object currentValue, String unit, List<String> description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        meta.setDisplayName(name);

        List<String> lore = new ArrayList<>(description);
        lore.add("");
        lore.add("§7当前值: §a" + currentValue + " " + unit);
        lore.add("");
        lore.add("§e点击修改此参数");

        meta.setLore(lore);
        button.setItemMeta(meta);
        return button;
    }

    /**
     * 设置粒子参数编辑器的控制按钮
     */
    private void setupParticleParameterControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回粒子编辑器",
            Arrays.asList("§7返回粒子效果编辑界面"));
        gui.setItem(49, backButton);

        // 预览按钮
        ItemStack previewButton = createButton(Material.ENDER_EYE,
            "§a👁 预览效果",
            Arrays.asList("§7使用当前参数预览粒子效果"));
        gui.setItem(52, previewButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存参数",
            Arrays.asList("§7保存当前粒子参数配置"));
        gui.setItem(53, saveButton);
    }

    /**
     * 处理粒子参数编辑器点击事件
     */
    private void handleParticleParameterEditorClick(Player player, int slot, org.bukkit.event.inventory.ClickType clickType) {
        String monsterId = editingMonster.get(player);
        String particleType = editingParticleType.get(player);

        if (monsterId == null || particleType == null) {
            return;
        }

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            return;
        }

        if (slot == 10) {
            // 编辑粒子数量
            requestParameterInput(player, "count", "粒子数量", config.getParticleCount(), "个", "1-100");
        } else if (slot == 11) {
            // 编辑粒子范围
            requestParameterInput(player, "range", "粒子范围", config.getParticleRange(), "格", "0.5-10.0");
        } else if (slot == 12) {
            // 编辑粒子间隔
            requestParameterInput(player, "interval", "粒子间隔", config.getParticleInterval(), "tick", "5-100");
        } else if (slot == 13) {
            // 编辑预览时长
            requestParameterInput(player, "duration", "预览时长", config.getPreviewDuration(), "秒", "1-30");
        } else if (slot == 49) {
            // 返回粒子编辑器
            openParticleEditor(player, monsterId, false);
        } else if (slot == 52) {
            // 预览效果
            previewSingleParticle(player, config, particleType);
        } else if (slot == 53) {
            // 保存参数
            saveParticleParameters(player, monsterId);
        }
    }

    /**
     * 请求参数输入
     */
    private void requestParameterInput(Player player, String paramType, String paramName, Object currentValue, String unit, String range) {
        waitingForInput.put(player, player.getName());
        inputType.put(player, paramType);

        player.closeInventory();

        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "=== 粒子参数配置 ===");
        player.sendMessage(ChatColor.YELLOW + "请输入新的 " + ChatColor.AQUA + paramName + ChatColor.YELLOW + ":");
        player.sendMessage(ChatColor.GRAY + "当前值: " + ChatColor.WHITE + currentValue + " " + unit);
        player.sendMessage(ChatColor.GRAY + "有效范围: " + ChatColor.WHITE + range);
        player.sendMessage("");
        player.sendMessage(ChatColor.GRAY + "输入 " + ChatColor.RED + "'cancel'" + ChatColor.GRAY + " 取消修改");
        player.sendMessage(ChatColor.GOLD + "==================");
        player.sendMessage("");
    }

    /**
     * 预览单个粒子效果
     */
    private void previewSingleParticle(Player player, IDZMonsterConfig config, String particleType) {
        // 停止之前的预览
        stopParticlePreview(player);

        player.sendMessage(ChatColor.GREEN + "开始预览粒子效果: " + getParticleDisplayName(particleType));

        // 创建临时的单粒子列表
        Set<String> tempSelected = new HashSet<>();
        tempSelected.add(particleType);
        selectedParticles.put(player, tempSelected);

        // 开始预览
        startParticlePreview(player, config);
    }

    /**
     * 保存粒子参数
     */
    private void saveParticleParameters(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，无法保存");
            return;
        }

        try {
            // 保存配置
            boolean saveResult = idzManager.updateMonsterConfig(monsterId, config);

            if (saveResult) {
                player.sendMessage(ChatColor.GREEN + "✅ 粒子参数已成功保存！");
                player.sendTitle("", ChatColor.GREEN + "✅ 参数保存成功", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                if (logger != null) {
                    logger.info("粒子参数保存成功: " + monsterId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "❌ 保存参数失败，请检查控制台错误信息");
                player.sendTitle("", ChatColor.RED + "❌ 保存失败", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                if (logger != null) {
                    logger.warning("粒子参数保存失败: " + monsterId);
                }
            }

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "❌ 保存参数时发生错误: " + e.getMessage());
            player.sendTitle("", ChatColor.RED + "❌ 保存出错", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

            if (logger != null) {
                logger.severe("保存粒子参数时发生异常: " + monsterId);
                e.printStackTrace();
            }
        }
    }
}
