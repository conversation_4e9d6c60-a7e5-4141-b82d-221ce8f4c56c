package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 装备选择器GUI
 */
public class EquipmentSelectorGUI implements Listener {
    
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final Logger logger;
    private final EnchantmentEditorGUI enchantmentEditor;
    
    // GUI配置
    private static final String GUI_TITLE = "§6装备选择器";
    private static final int GUI_SIZE = 54;
    
    // 玩家编辑状态
    private final Map<Player, String> editingMonster = new HashMap<>();
    private final Map<Player, Integer> editingSlot = new HashMap<>();
    private final Map<Player, String> searchFilter = new HashMap<>();
    private final Map<Player, Integer> currentPage = new HashMap<>();
    private final Map<Player, Boolean> waitingForSearch = new HashMap<>();
    
    public EquipmentSelectorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        this.enchantmentEditor = new EnchantmentEditorGUI(plugin, idzManager);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开装备选择器
     */
    public void openEquipmentSelector(Player player, String monsterId, int equipmentSlot) {
        openEquipmentSelector(player, monsterId, equipmentSlot, "", 0);
    }

    /**
     * 打开装备选择器（带搜索和分页）
     */
    public void openEquipmentSelector(Player player, String monsterId, int equipmentSlot, String searchTerm, int page) {
        editingMonster.put(player, monsterId);
        editingSlot.put(player, equipmentSlot);
        searchFilter.put(player, searchTerm);
        currentPage.put(player, page);

        String equipmentType = getEquipmentTypeName(equipmentSlot);
        String title = GUI_TITLE + " - " + equipmentType;
        if (!searchTerm.isEmpty()) {
            title += " [搜索: " + searchTerm + "]";
        }
        if (page > 0) {
            title += " [第" + (page + 1) + "页]";
        }

        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, title);

        // 填充背景
        fillBackground(gui);

        // 设置装备选项
        setupEquipmentOptions(gui, equipmentSlot, searchTerm, page);

        // 设置控制按钮
        setupControlButtons(gui, equipmentSlot, searchTerm, page);

        player.openInventory(gui);

        logger.info("为玩家 " + player.getName() + " 打开装备选择器: " + equipmentType +
                   (searchTerm.isEmpty() ? "" : " (搜索: " + searchTerm + ")") +
                   (page > 0 ? " (第" + (page + 1) + "页)" : ""));
    }
    
    /**
     * 设置装备选项
     */
    private void setupEquipmentOptions(Inventory gui, int equipmentSlot) {
        List<Material> materials = getEquipmentMaterials(equipmentSlot);

        int slot = 0;
        int maxSlots = (equipmentSlot == 14) ? 36 : 45; // 头盔槽位留更多空间给分页

        for (Material material : materials) {
            if (slot >= maxSlots) break; // 限制显示数量

            ItemStack item = createEquipmentOption(material);
            gui.setItem(slot, item);
            slot++;
        }

        // 添加"无装备"选项
        ItemStack noneOption = createButton(Material.BARRIER, "§c无装备", Arrays.asList("§7移除当前装备"));
        gui.setItem(45, noneOption);

        // 如果是头盔槽位且材料很多，添加提示
        if (equipmentSlot == 14 && materials.size() > maxSlots) {
            ItemStack moreOption = createButton(Material.BOOK, "§e更多选项",
                Arrays.asList("§7总共有 " + materials.size() + " 种材料可选",
                             "§7当前显示前 " + maxSlots + " 种",
                             "§7可以使用搜索功能查找特定物品"));
            gui.setItem(47, moreOption);
        }
    }

    /**
     * 设置装备选项（带搜索和分页）
     */
    private void setupEquipmentOptions(Inventory gui, int equipmentSlot, String searchTerm, int page) {
        List<Material> allMaterials = getEquipmentMaterials(equipmentSlot);
        List<Material> filteredMaterials = filterMaterialsBySearch(allMaterials, searchTerm);

        // 计算分页
        int itemsPerPage = 36; // 为头盔槽位优化，留出更多控制按钮空间
        int totalPages = (filteredMaterials.size() + itemsPerPage - 1) / itemsPerPage;
        int startIndex = page * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, filteredMaterials.size());

        // 获取当前页的材料
        List<Material> pageMaterials = filteredMaterials.subList(startIndex, endIndex);

        // 添加装备选项
        int slot = 0;
        for (Material material : pageMaterials) {
            if (slot >= itemsPerPage) break;

            ItemStack item = createEquipmentOption(material);
            gui.setItem(slot, item);
            slot++;
        }

        // 添加"无装备"选项
        ItemStack noneOption = createButton(Material.BARRIER, "§c无装备", Arrays.asList("§7移除当前装备"));
        gui.setItem(45, noneOption);

        // 如果没有搜索结果，显示提示
        if (filteredMaterials.isEmpty() && !searchTerm.isEmpty()) {
            ItemStack noResultOption = createButton(Material.STRUCTURE_VOID, "§c无搜索结果",
                Arrays.asList("§7没有找到包含 '" + searchTerm + "' 的物品",
                             "§7请尝试其他搜索关键词",
                             "§e点击搜索按钮重新搜索"));
            gui.setItem(22, noResultOption); // 居中显示
        }
    }

    /**
     * 搜索过滤材料
     */
    private List<Material> filterMaterialsBySearch(List<Material> materials, String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return materials;
        }

        String lowerSearchTerm = searchTerm.toLowerCase().trim();
        List<Material> filtered = new ArrayList<>();

        for (Material material : materials) {
            if (material.name().toLowerCase().contains(lowerSearchTerm)) {
                filtered.add(material);
            }
        }

        return filtered;
    }

    /**
     * 获取装备材料列表
     */
    private List<Material> getEquipmentMaterials(int equipmentSlot) {
        switch (equipmentSlot) {
            case 10: // 主手武器
                return Arrays.asList(
                    // 剑类
                    Material.WOODEN_SWORD, Material.STONE_SWORD, Material.IRON_SWORD,
                    Material.DIAMOND_SWORD, Material.NETHERITE_SWORD, Material.GOLDEN_SWORD,
                    // 斧类
                    Material.WOODEN_AXE, Material.STONE_AXE, Material.IRON_AXE,
                    Material.DIAMOND_AXE, Material.NETHERITE_AXE, Material.GOLDEN_AXE,
                    // 镐类
                    Material.WOODEN_PICKAXE, Material.STONE_PICKAXE, Material.IRON_PICKAXE,
                    Material.DIAMOND_PICKAXE, Material.NETHERITE_PICKAXE, Material.GOLDEN_PICKAXE,
                    // 锹类
                    Material.WOODEN_SHOVEL, Material.STONE_SHOVEL, Material.IRON_SHOVEL,
                    Material.DIAMOND_SHOVEL, Material.NETHERITE_SHOVEL, Material.GOLDEN_SHOVEL,
                    // 锄类
                    Material.WOODEN_HOE, Material.STONE_HOE, Material.IRON_HOE,
                    Material.DIAMOND_HOE, Material.NETHERITE_HOE, Material.GOLDEN_HOE,
                    // 远程武器
                    Material.BOW, Material.CROSSBOW, Material.TRIDENT,
                    // 特殊武器
                    Material.STICK, Material.BLAZE_ROD, Material.BONE, Material.BAMBOO,
                    Material.MACE, Material.FISHING_ROD, Material.CARROT_ON_A_STICK
                );
            case 12: // 副手物品
                return Arrays.asList(
                    Material.SHIELD, Material.TOTEM_OF_UNDYING, Material.ARROW, Material.SPECTRAL_ARROW,
                    Material.TORCH, Material.SOUL_TORCH, Material.REDSTONE_TORCH,
                    Material.BOOK, Material.ENCHANTED_BOOK, Material.WRITABLE_BOOK, Material.WRITTEN_BOOK,
                    Material.MAP, Material.FILLED_MAP, Material.COMPASS, Material.CLOCK,
                    Material.ENDER_PEARL, Material.ENDER_EYE, Material.SNOWBALL, Material.EGG,
                    Material.FIREWORK_ROCKET, Material.FIRE_CHARGE, Material.TNT
                );
            case 14: // 头盔 - 支持所有材料
                return getAllMaterials();
            case 16: // 胸甲
                return Arrays.asList(
                    Material.LEATHER_CHESTPLATE, Material.CHAINMAIL_CHESTPLATE, Material.IRON_CHESTPLATE,
                    Material.DIAMOND_CHESTPLATE, Material.NETHERITE_CHESTPLATE, Material.GOLDEN_CHESTPLATE,
                    Material.ELYTRA
                );
            case 24: // 护腿
                return Arrays.asList(
                    Material.LEATHER_LEGGINGS, Material.CHAINMAIL_LEGGINGS, Material.IRON_LEGGINGS,
                    Material.DIAMOND_LEGGINGS, Material.NETHERITE_LEGGINGS, Material.GOLDEN_LEGGINGS
                );
            case 26: // 靴子
                return Arrays.asList(
                    Material.LEATHER_BOOTS, Material.CHAINMAIL_BOOTS, Material.IRON_BOOTS,
                    Material.DIAMOND_BOOTS, Material.NETHERITE_BOOTS, Material.GOLDEN_BOOTS
                );
            default:
                return Arrays.asList(Material.STICK);
        }
    }
    
    /**
     * 获取所有材料（用于头盔槽位）
     */
    private List<Material> getAllMaterials() {
        List<Material> materials = new ArrayList<>();

        // 添加常用的头部装备
        materials.addAll(Arrays.asList(
            // 传统头盔
            Material.LEATHER_HELMET, Material.CHAINMAIL_HELMET, Material.IRON_HELMET,
            Material.DIAMOND_HELMET, Material.NETHERITE_HELMET, Material.GOLDEN_HELMET,
            Material.TURTLE_HELMET,
            // 方块类
            Material.JACK_O_LANTERN, Material.CARVED_PUMPKIN, Material.MELON,
            Material.DRAGON_HEAD, Material.ZOMBIE_HEAD, Material.SKELETON_SKULL,
            Material.WITHER_SKELETON_SKULL, Material.CREEPER_HEAD, Material.PIGLIN_HEAD,
            Material.PLAYER_HEAD,
            // 特殊方块
            Material.TNT, Material.OBSIDIAN, Material.BEDROCK, Material.DIAMOND_BLOCK,
            Material.GOLD_BLOCK, Material.IRON_BLOCK, Material.EMERALD_BLOCK,
            Material.BEACON, Material.ENCHANTING_TABLE, Material.ANVIL,
            // 装饰方块
            Material.GLASS, Material.WHITE_STAINED_GLASS, Material.RED_STAINED_GLASS, Material.SEA_LANTERN,
            Material.GLOWSTONE, Material.REDSTONE_LAMP, Material.LANTERN,
            // 自然方块
            Material.GRASS_BLOCK, Material.DIRT, Material.STONE, Material.COBBLESTONE,
            Material.SAND, Material.GRAVEL, Material.CLAY, Material.SNOW_BLOCK,
            // 木材类
            Material.OAK_LOG, Material.BIRCH_LOG, Material.SPRUCE_LOG, Material.JUNGLE_LOG,
            Material.ACACIA_LOG, Material.DARK_OAK_LOG, Material.MANGROVE_LOG, Material.CHERRY_LOG,
            // 花朵类
            Material.POPPY, Material.DANDELION, Material.BLUE_ORCHID, Material.ALLIUM,
            Material.AZURE_BLUET, Material.RED_TULIP, Material.ORANGE_TULIP, Material.WHITE_TULIP,
            Material.PINK_TULIP, Material.OXEYE_DAISY, Material.CORNFLOWER, Material.LILY_OF_THE_VALLEY,
            // 食物类
            Material.APPLE, Material.BREAD, Material.COOKED_BEEF, Material.COOKED_PORKCHOP,
            Material.GOLDEN_APPLE, Material.ENCHANTED_GOLDEN_APPLE, Material.CAKE,
            // 工具类
            Material.COMPASS, Material.CLOCK, Material.SPYGLASS, Material.RECOVERY_COMPASS
        ));

        // 添加所有剩余材料（过滤掉空气和一些不适合的材料）
        for (Material material : Material.values()) {
            if (!materials.contains(material) &&
                material != Material.AIR &&
                material != Material.VOID_AIR &&
                material != Material.CAVE_AIR &&
                !material.name().contains("WALL_") &&
                !material.name().contains("POTTED_") &&
                material.isItem()) {
                materials.add(material);
            }
        }

        return materials;
    }

    /**
     * 创建装备选项
     */
    private ItemStack createEquipmentOption(Material material) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§e" + material.name());
        meta.setLore(Arrays.asList("§7点击选择此装备"));
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW, "§e← 返回", Arrays.asList("§7返回装备编辑器"));
        gui.setItem(49, backButton);

        // 附魔编辑按钮
        ItemStack enchantButton = createButton(Material.ENCHANTING_TABLE, "§d✨ 附魔编辑",
            Arrays.asList("§7为选中的装备添加附魔", "§e先选择装备，再点击此按钮"));
        gui.setItem(53, enchantButton);
    }

    /**
     * 设置控制按钮（带搜索和分页）
     */
    private void setupControlButtons(Inventory gui, int equipmentSlot, String searchTerm, int page) {
        // 计算总页数
        List<Material> allMaterials = getEquipmentMaterials(equipmentSlot);
        List<Material> filteredMaterials = filterMaterialsBySearch(allMaterials, searchTerm);
        int itemsPerPage = 36;
        int totalPages = Math.max(1, (filteredMaterials.size() + itemsPerPage - 1) / itemsPerPage);

        // 搜索按钮 (槽位46)
        ItemStack searchButton = createButton(Material.SPYGLASS, "§e🔍 搜索装备",
            Arrays.asList("§7点击后在聊天框输入搜索关键词",
                         "§7支持模糊搜索材料名称",
                         "§e例如: 输入 'DIAMOND' 查找钻石装备"));
        gui.setItem(46, searchButton);

        // 清除搜索按钮 (槽位47)
        if (!searchTerm.isEmpty()) {
            ItemStack clearButton = createButton(Material.BARRIER, "§c🧹 清除搜索",
                Arrays.asList("§7清除当前搜索条件",
                             "§7当前搜索: §e" + searchTerm,
                             "§7找到 §a" + filteredMaterials.size() + " §7个结果"));
            gui.setItem(47, clearButton);
        }

        // 上一页按钮 (槽位48)
        if (page > 0) {
            ItemStack prevButton = createButton(Material.ARROW, "§e← 上一页",
                Arrays.asList("§7第 " + page + " 页",
                             "§7总共 " + totalPages + " 页"));
            gui.setItem(48, prevButton);
        }

        // 返回按钮 (槽位49) - 保持不变
        ItemStack backButton = createButton(Material.ARROW, "§e← 返回", Arrays.asList("§7返回装备编辑器"));
        gui.setItem(49, backButton);

        // 下一页按钮 (槽位50)
        if (page < totalPages - 1) {
            ItemStack nextButton = createButton(Material.ARROW, "§e下一页 →",
                Arrays.asList("§7第 " + (page + 2) + " 页",
                             "§7总共 " + totalPages + " 页"));
            gui.setItem(50, nextButton);
        }

        // 附魔编辑按钮 (槽位53) - 保持不变
        ItemStack enchantButton = createButton(Material.ENCHANTING_TABLE, "§d✨ 附魔编辑",
            Arrays.asList("§7为选中的装备添加附魔", "§e先选择装备，再点击此按钮"));
        gui.setItem(53, enchantButton);
    }

    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 只填充控制按钮区域
        for (int i = 46; i < 54; i++) {
            if (i != 49) { // 跳过返回按钮位置
                gui.setItem(i, background);
            }
        }
    }
    
    /**
     * 获取装备类型名称
     */
    private String getEquipmentTypeName(int slot) {
        switch (slot) {
            case 10: return "主手武器";
            case 12: return "副手物品";
            case 14: return "头盔";
            case 16: return "胸甲";
            case 24: return "护腿";
            case 26: return "靴子";
            default: return "未知装备";
        }
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE)) {
            return;
        }

        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        String monsterId = editingMonster.get(player);
        Integer equipmentSlot = editingSlot.get(player);
        String currentSearchTerm = searchFilter.getOrDefault(player, "");
        Integer currentPageNum = currentPage.getOrDefault(player, 0);

        if (monsterId == null || equipmentSlot == null) {
            return;
        }

        if (slot == 46) {
            // 搜索按钮
            waitingForSearch.put(player, true);
            player.closeInventory();
            player.sendMessage(ChatColor.YELLOW + "请在聊天框输入搜索关键词:");
            player.sendMessage(ChatColor.GRAY + "例如: DIAMOND, GOLD, HELMET 等");
            player.sendMessage(ChatColor.GRAY + "输入 'cancel' 取消搜索");
        } else if (slot == 47) {
            // 清除搜索按钮
            openEquipmentSelector(player, monsterId, equipmentSlot, "", 0);
        } else if (slot == 48) {
            // 上一页按钮
            if (currentPageNum > 0) {
                openEquipmentSelector(player, monsterId, equipmentSlot, currentSearchTerm, currentPageNum - 1);
            }
        } else if (slot == 49) {
            // 返回装备编辑器
            returnToEquipmentEditor(player, monsterId);
        } else if (slot == 50) {
            // 下一页按钮
            openEquipmentSelector(player, monsterId, equipmentSlot, currentSearchTerm, currentPageNum + 1);
        } else if (slot == 53) {
            // 附魔编辑
            openEnchantmentEditor(player, monsterId, equipmentSlot);
        } else if (slot == 45) {
            // 选择"无装备"
            selectEquipment(player, monsterId, equipmentSlot, null);
        } else if (slot < 36) { // 修改为36，因为我们现在每页显示36个物品
            // 选择装备
            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem != null && clickedItem.getType() != Material.AIR &&
                !clickedItem.getType().equals(Material.STRUCTURE_VOID)) { // 排除"无搜索结果"提示
                selectEquipment(player, monsterId, equipmentSlot, clickedItem.getType());
            }
        }
    }

    /**
     * 处理聊天输入（搜索功能）
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();

        if (!waitingForSearch.containsKey(player)) {
            return;
        }

        event.setCancelled(true);

        String input = event.getMessage().trim();
        String monsterId = editingMonster.get(player);
        Integer equipmentSlot = editingSlot.get(player);

        // 清理搜索状态
        waitingForSearch.remove(player);

        if (monsterId == null || equipmentSlot == null) {
            player.sendMessage(ChatColor.RED + "搜索状态异常，请重新打开装备选择器");
            return;
        }

        if ("cancel".equalsIgnoreCase(input)) {
            player.sendMessage(ChatColor.GRAY + "已取消搜索");
            // 重新打开GUI（不带搜索）
            Bukkit.getScheduler().runTask(plugin, () ->
                openEquipmentSelector(player, monsterId, equipmentSlot, "", 0));
            return;
        }

        // 处理搜索输入
        if (input.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "搜索关键词不能为空，请重新输入");
            // 重新打开GUI（不带搜索）
            Bukkit.getScheduler().runTask(plugin, () ->
                openEquipmentSelector(player, monsterId, equipmentSlot, "", 0));
            return;
        }

        // 执行搜索并重新打开GUI
        player.sendMessage(ChatColor.GREEN + "正在搜索: " + input);
        Bukkit.getScheduler().runTask(plugin, () ->
            openEquipmentSelector(player, monsterId, equipmentSlot, input, 0));
    }

    /**
     * 选择装备
     */
    private void selectEquipment(Player player, String monsterId, int equipmentSlot, Material material) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        
        // 设置装备
        ItemStack equipment = material != null ? new ItemStack(material) : null;
        setEquipmentBySlot(config, equipmentSlot, equipment);
        
        // 保存配置
        idzManager.updateMonsterConfig(monsterId, config);
        
        String equipmentName = material != null ? material.name() : "无";
        player.sendMessage(ChatColor.GREEN + "已设置" + getEquipmentTypeName(equipmentSlot) + ": " + equipmentName);
        
        // 返回装备编辑器
        returnToEquipmentEditor(player, monsterId);
    }
    
    /**
     * 根据槽位设置装备
     */
    private void setEquipmentBySlot(IDZMonsterConfig config, int slot, ItemStack equipment) {
        switch (slot) {
            case 10: config.setMainHand(equipment); break;
            case 12: config.setOffHand(equipment); break;
            case 14: config.setHelmet(equipment); break;
            case 16: config.setChestplate(equipment); break;
            case 24: config.setLeggings(equipment); break;
            case 26: config.setBoots(equipment); break;
        }
    }
    
    /**
     * 打开附魔编辑器
     */
    private void openEnchantmentEditor(Player player, String monsterId, int equipmentSlot) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }

        // 获取当前装备
        ItemStack currentEquipment = getEquipmentBySlot(config, equipmentSlot);
        if (currentEquipment == null) {
            player.sendMessage(ChatColor.RED + "请先选择一个装备再进行附魔编辑！");
            return;
        }

        // 打开附魔编辑器
        enchantmentEditor.openEnchantmentEditor(player, monsterId, equipmentSlot, currentEquipment);
    }

    /**
     * 根据槽位获取装备
     */
    private ItemStack getEquipmentBySlot(IDZMonsterConfig config, int slot) {
        switch (slot) {
            case 10: return config.getMainHand();
            case 12: return config.getOffHand();
            case 14: return config.getHelmet();
            case 16: return config.getChestplate();
            case 24: return config.getLeggings();
            case 26: return config.getBoots();
            default: return null;
        }
    }

    /**
     * 返回装备编辑器
     */
    private void returnToEquipmentEditor(Player player, String monsterId) {
        // 清理所有玩家状态
        editingMonster.remove(player);
        editingSlot.remove(player);
        searchFilter.remove(player);
        currentPage.remove(player);
        waitingForSearch.remove(player);

        player.closeInventory();

        // 重新打开装备编辑器
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            idzManager.getGuiManager().getEquipmentEditor().openEquipmentEditor(player, monsterId);
        }, 1L);
    }

    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        editingSlot.remove(player);
        searchFilter.remove(player);
        currentPage.remove(player);
        waitingForSearch.remove(player);
    }
}
