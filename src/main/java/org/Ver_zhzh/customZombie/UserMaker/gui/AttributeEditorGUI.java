package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.util.*;
import java.util.logging.Logger;

/**
 * 属性编辑器GUI
 * 用于编辑IDZ怪物的基础属性
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AttributeEditorGUI implements Listener {
    
    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager idzManager;
    
    private static final String GUI_TITLE = "§6属性编辑器";
    
    // 等待输入的玩家追踪
    private final Map<Player, String> waitingForInput;
    private final Map<Player, String> inputType;
    private final Map<Player, String> editingMonster;
    
    // 槽位常量
    private static final int HEALTH_SLOT = 10;
    private static final int SPEED_SLOT = 12;
    private static final int DAMAGE_SLOT = 14;
    private static final int ARMOR_SLOT = 16;
    private static final int ENTITY_TYPE_SLOT = 22;
    private static final int BACK_SLOT = 40;
    private static final int SAVE_SLOT = 44;
    
    /**
     * 构造函数
     */
    public AttributeEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.idzManager = idzManager;
        this.waitingForInput = new HashMap<>();
        this.inputType = new HashMap<>();
        this.editingMonster = new HashMap<>();
        
        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开属性编辑器
     */
    public void openAttributeEditor(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        
        editingMonster.put(player, monsterId);
        
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE + " - " + config.getDisplayName());
        
        // 填充背景
        fillBackground(gui);
        
        // 设置属性编辑按钮
        setupAttributeButtons(gui, config);
        
        // 设置控制按钮
        setupControlButtons(gui);
        
        player.openInventory(gui);
        
        logger.info("为玩家 " + player.getName() + " 打开属性编辑器: " + monsterId);
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.LIGHT_BLUE_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 填充边框
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, background);
            gui.setItem(i + 45, background);
        }
        for (int i = 0; i < 54; i += 9) {
            gui.setItem(i, background);
            gui.setItem(i + 8, background);
        }
    }
    
    /**
     * 设置属性编辑按钮
     */
    private void setupAttributeButtons(Inventory gui, IDZMonsterConfig config) {
        // 生命值编辑
        ItemStack healthButton = createAttributeButton(Material.RED_DYE,
            "§c❤ 生命值",
            config.getHealth(),
            Arrays.asList(
                "§7当前值: §c" + config.getHealth(),
                "§7范围: §e1.0 - 2048.0",
                "",
                "§e点击修改生命值"
            ));
        gui.setItem(HEALTH_SLOT, healthButton);
        
        // 移动速度编辑
        ItemStack speedButton = createAttributeButton(Material.SUGAR,
            "§a⚡ 移动速度",
            config.getMovementSpeed(),
            Arrays.asList(
                "§7当前值: §a" + config.getMovementSpeed(),
                "§7范围: §e0.0 - 1.0",
                "§7默认: §70.23",
                "",
                "§e点击修改移动速度"
            ));
        gui.setItem(SPEED_SLOT, speedButton);
        
        // 攻击伤害编辑
        ItemStack damageButton = createAttributeButton(Material.IRON_SWORD,
            "§c⚔ 攻击伤害",
            config.getAttackDamage(),
            Arrays.asList(
                "§7当前值: §c" + config.getAttackDamage(),
                "§7范围: §e0.0 - 2048.0",
                "",
                "§e点击修改攻击伤害"
            ));
        gui.setItem(DAMAGE_SLOT, damageButton);
        
        // 护甲值编辑
        ItemStack armorButton = createAttributeButton(Material.IRON_CHESTPLATE,
            "§9🛡 护甲值",
            config.getArmor(),
            Arrays.asList(
                "§7当前值: §9" + config.getArmor(),
                "§7范围: §e0.0 - 30.0",
                "",
                "§e点击修改护甲值"
            ));
        gui.setItem(ARMOR_SLOT, armorButton);
        
        // 实体类型选择
        ItemStack entityTypeButton = createButton(Material.ZOMBIE_SPAWN_EGG,
            "§b🎭 实体类型",
            Arrays.asList(
                "§7当前类型: §b" + config.getEntityType().name(),
                "§7支持的类型:",
                "§7- ZOMBIE (僵尸)",
                "§7- SKELETON (骷髅)",
                "§7- CREEPER (爬行者)",
                "§7- SPIDER (蜘蛛)",
                "§7- ENDERMAN (末影人)",
                "",
                "§e点击切换实体类型"
            ));
        gui.setItem(ENTITY_TYPE_SLOT, entityTypeButton);
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回主菜单",
            Arrays.asList("§7返回IDZ怪物主编辑器"));
        gui.setItem(BACK_SLOT, backButton);
        
        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存属性",
            Arrays.asList("§7保存当前属性设置"));
        gui.setItem(SAVE_SLOT, saveButton);
    }
    
    /**
     * 创建属性按钮
     */
    private ItemStack createAttributeButton(Material material, String name, double value, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 创建普通按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        if (!title.startsWith(GUI_TITLE)) {
            return;
        }
        
        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        String monsterId = editingMonster.get(player);
        
        if (monsterId == null) {
            return;
        }
        
        switch (slot) {
            case HEALTH_SLOT:
                requestInput(player, "health", "请输入新的生命值 (1.0-2048.0):");
                break;
                
            case SPEED_SLOT:
                requestInput(player, "speed", "请输入新的移动速度 (0.0-1.0):");
                break;
                
            case DAMAGE_SLOT:
                requestInput(player, "damage", "请输入新的攻击伤害 (0.0-2048.0):");
                break;
                
            case ARMOR_SLOT:
                requestInput(player, "armor", "请输入新的护甲值 (0.0-30.0):");
                break;
                
            case ENTITY_TYPE_SLOT:
                cycleEntityType(player, monsterId);
                break;
                
            case BACK_SLOT:
                returnToMainEditor(player, monsterId);
                break;
                
            case SAVE_SLOT:
                saveAttributes(player, monsterId);
                break;
        }
    }
    
    /**
     * 请求玩家输入
     */
    private void requestInput(Player player, String type, String message) {
        waitingForInput.put(player, player.getName());
        inputType.put(player, type);
        
        player.closeInventory();
        player.sendMessage(ChatColor.YELLOW + message);
        player.sendMessage(ChatColor.GRAY + "输入 'cancel' 取消修改");
    }
    
    /**
     * 切换实体类型
     */
    private void cycleEntityType(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;
        
        // 支持的实体类型列表
        EntityType[] supportedTypes = {
            EntityType.ZOMBIE, EntityType.SKELETON, EntityType.CREEPER,
            EntityType.SPIDER, EntityType.ENDERMAN, EntityType.BLAZE,
            EntityType.WITCH, EntityType.VINDICATOR, EntityType.EVOKER
        };
        
        // 找到当前类型的索引
        int currentIndex = 0;
        for (int i = 0; i < supportedTypes.length; i++) {
            if (supportedTypes[i] == config.getEntityType()) {
                currentIndex = i;
                break;
            }
        }
        
        // 切换到下一个类型
        int nextIndex = (currentIndex + 1) % supportedTypes.length;
        config.setEntityType(supportedTypes[nextIndex]);
        
        // 更新配置
        idzManager.updateMonsterConfig(monsterId, config);
        
        // 重新打开界面以刷新显示
        openAttributeEditor(player, monsterId);
        
        player.sendMessage(ChatColor.GREEN + "实体类型已切换为: " + supportedTypes[nextIndex].name());
    }
    
    /**
     * 处理聊天输入
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        
        if (!waitingForInput.containsKey(player)) {
            return;
        }
        
        event.setCancelled(true);
        
        String input = event.getMessage().trim();
        String type = inputType.get(player);
        String monsterId = editingMonster.get(player);
        
        // 清理输入状态
        waitingForInput.remove(player);
        inputType.remove(player);
        
        if ("cancel".equalsIgnoreCase(input)) {
            player.sendMessage(ChatColor.GRAY + "已取消修改");
            // 重新打开GUI
            Bukkit.getScheduler().runTask(plugin, () -> openAttributeEditor(player, monsterId));
            return;
        }
        
        // 处理数值输入
        try {
            double value = Double.parseDouble(input);
            
            IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config == null) {
                player.sendMessage(ChatColor.RED + "怪物配置不存在！");
                return;
            }
            
            boolean success = false;
            String attributeName = "";
            
            switch (type) {
                case "health":
                    if (value >= 1.0 && value <= 2048.0) {
                        config.setHealth(value);
                        attributeName = "生命值";
                        success = true;
                    }
                    break;
                    
                case "speed":
                    if (value >= 0.0 && value <= 1.0) {
                        config.setMovementSpeed(value);
                        attributeName = "移动速度";
                        success = true;
                    }
                    break;
                    
                case "damage":
                    if (value >= 0.0 && value <= 2048.0) {
                        config.setAttackDamage(value);
                        attributeName = "攻击伤害";
                        success = true;
                    }
                    break;
                    
                case "armor":
                    if (value >= 0.0 && value <= 30.0) {
                        config.setArmor(value);
                        attributeName = "护甲值";
                        success = true;
                    }
                    break;
            }
            
            if (success) {
                // 更新配置
                idzManager.updateMonsterConfig(monsterId, config);
                player.sendMessage(ChatColor.GREEN + attributeName + " 已设置为: " + value);
            } else {
                player.sendMessage(ChatColor.RED + "数值超出有效范围！");
            }
            
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "请输入有效的数字！");
        }
        
        // 重新打开GUI
        Bukkit.getScheduler().runTask(plugin, () -> openAttributeEditor(player, monsterId));
    }
    
    /**
     * 返回主编辑器
     */
    private void returnToMainEditor(Player player, String monsterId) {
        editingMonster.remove(player);
        player.closeInventory();
        
        // 通知主GUI管理器重新打开主界面
        // 这里需要与主GUI管理器协调
        player.sendMessage(ChatColor.YELLOW + "返回主编辑器...");
    }
    
    /**
     * 保存属性
     */
    private void saveAttributes(Player player, String monsterId) {
        editingMonster.remove(player);
        player.closeInventory();
        player.sendMessage(ChatColor.GREEN + "属性已保存！");
    }
    
    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        waitingForInput.remove(player);
        inputType.remove(player);
        editingMonster.remove(player);
    }
}
