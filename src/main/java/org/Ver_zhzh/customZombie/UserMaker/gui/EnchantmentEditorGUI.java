package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;

import java.util.*;
import java.util.logging.Logger;

/**
 * 附魔编辑器GUI
 */
public class EnchantmentEditorGUI implements Listener {
    
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final Logger logger;
    
    // GUI配置
    private static final String GUI_TITLE = "§6附魔编辑器";
    private static final int GUI_SIZE = 54;
    
    // 玩家编辑状态
    private final Map<Player, String> editingMonster = new HashMap<>();
    private final Map<Player, Integer> editingSlot = new HashMap<>();
    private final Map<Player, ItemStack> editingItem = new HashMap<>();
    
    public EnchantmentEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        
        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开附魔编辑器
     */
    public void openEnchantmentEditor(Player player, String monsterId, int equipmentSlot, ItemStack item) {
        editingMonster.put(player, monsterId);
        editingSlot.put(player, equipmentSlot);
        editingItem.put(player, item.clone());
        
        String equipmentType = getEquipmentTypeName(equipmentSlot);
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE + " - " + equipmentType);
        
        // 填充背景
        fillBackground(gui);
        
        // 显示当前物品
        setupCurrentItem(gui, item);
        
        // 设置附魔选项
        setupEnchantmentOptions(gui, item);
        
        // 设置控制按钮
        setupControlButtons(gui);
        
        player.openInventory(gui);
        
        logger.info("为玩家 " + player.getName() + " 打开附魔编辑器: " + equipmentType);
    }
    
    /**
     * 显示当前物品
     */
    private void setupCurrentItem(Inventory gui, ItemStack item) {
        ItemStack displayItem = item.clone();
        ItemMeta meta = displayItem.getItemMeta();
        
        List<String> lore = new ArrayList<>();
        lore.add("§7当前物品: §e" + item.getType().name());
        lore.add("");
        
        if (item.getEnchantments().isEmpty()) {
            lore.add("§7无附魔");
        } else {
            lore.add("§7当前附魔:");
            for (Map.Entry<Enchantment, Integer> entry : item.getEnchantments().entrySet()) {
                lore.add("§e" + getEnchantmentName(entry.getKey()) + " " + entry.getValue());
            }
        }
        
        meta.setDisplayName("§6当前装备");
        meta.setLore(lore);
        displayItem.setItemMeta(meta);
        
        gui.setItem(4, displayItem);
    }
    
    /**
     * 设置附魔选项
     */
    private void setupEnchantmentOptions(Inventory gui, ItemStack item) {
        List<Enchantment> availableEnchantments = getAvailableEnchantments(item);
        
        int slot = 9;
        for (Enchantment enchantment : availableEnchantments) {
            if (slot >= 45) break; // 最多显示36个附魔
            
            ItemStack enchantItem = createEnchantmentOption(enchantment, item);
            gui.setItem(slot, enchantItem);
            slot++;
        }
    }
    
    /**
     * 获取可用附魔列表
     */
    private List<Enchantment> getAvailableEnchantments(ItemStack item) {
        List<Enchantment> enchantments = new ArrayList<>();
        
        // 获取所有附魔
        for (Enchantment enchantment : Enchantment.values()) {
            // 检查附魔是否适用于该物品
            if (enchantment.canEnchantItem(item) || item.getType() == Material.BOOK) {
                enchantments.add(enchantment);
            }
        }
        
        // 按名称排序
        enchantments.sort(Comparator.comparing(this::getEnchantmentName));
        
        return enchantments;
    }
    
    /**
     * 创建附魔选项
     */
    private ItemStack createEnchantmentOption(Enchantment enchantment, ItemStack item) {
        ItemStack enchantItem = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = enchantItem.getItemMeta();
        
        String enchantName = getEnchantmentName(enchantment);
        int currentLevel = item.getEnchantmentLevel(enchantment);
        int maxLevel = enchantment.getMaxLevel();
        
        meta.setDisplayName("§e" + enchantName);
        
        List<String> lore = new ArrayList<>();
        lore.add("§7当前等级: §e" + (currentLevel > 0 ? currentLevel : "无"));
        lore.add("§7原版最大等级: §e" + maxLevel);
        lore.add("§7支持等级: §e1 - " + Integer.MAX_VALUE);
        lore.add("");

        if (currentLevel > 0) {
            lore.add("§e左键: §7提升等级");
            lore.add("§e右键: §7移除附魔");
        } else {
            lore.add("§e左键: §7添加附魔");
        }
        
        meta.setLore(lore);
        enchantItem.setItemMeta(meta);
        
        return enchantItem;
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW, "§e← 返回", Arrays.asList("§7返回装备选择器"));
        gui.setItem(45, backButton);
        
        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD, "§a✓ 保存", Arrays.asList("§7保存附魔配置"));
        gui.setItem(53, saveButton);
        
        // 清除所有附魔按钮
        ItemStack clearButton = createButton(Material.BARRIER, "§c清除所有附魔", Arrays.asList("§7移除所有附魔"));
        gui.setItem(49, clearButton);
    }
    
    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 填充特定位置
        int[] backgroundSlots = {0, 1, 2, 3, 5, 6, 7, 8, 46, 47, 48, 50, 51, 52};
        for (int slot : backgroundSlots) {
            gui.setItem(slot, background);
        }
    }
    
    /**
     * 获取附魔名称
     */
    private String getEnchantmentName(Enchantment enchantment) {
        // 简化的附魔名称映射
        switch (enchantment.getKey().getKey()) {
            case "sharpness": return "锋利";
            case "protection": return "保护";
            case "efficiency": return "效率";
            case "unbreaking": return "耐久";
            case "fortune": return "时运";
            case "silk_touch": return "精准采集";
            case "power": return "力量";
            case "punch": return "冲击";
            case "flame": return "火矢";
            case "infinity": return "无限";
            case "looting": return "抢夺";
            case "fire_aspect": return "火焰附加";
            case "knockback": return "击退";
            case "smite": return "亡灵杀手";
            case "bane_of_arthropods": return "节肢杀手";
            case "blast_protection": return "爆炸保护";
            case "fire_protection": return "火焰保护";
            case "projectile_protection": return "弹射物保护";
            case "feather_falling": return "摔落保护";
            case "respiration": return "水下呼吸";
            case "aqua_affinity": return "水下速掘";
            case "thorns": return "荆棘";
            case "depth_strider": return "深海探索者";
            case "frost_walker": return "冰霜行者";
            case "mending": return "经验修补";
            case "curse_of_binding": return "绑定诅咒";
            case "curse_of_vanishing": return "消失诅咒";
            default: return enchantment.getKey().getKey();
        }
    }
    
    /**
     * 获取装备类型名称
     */
    private String getEquipmentTypeName(int slot) {
        switch (slot) {
            case 10: return "主手武器";
            case 12: return "副手物品";
            case 14: return "头盔";
            case 16: return "胸甲";
            case 24: return "护腿";
            case 26: return "靴子";
            default: return "未知装备";
        }
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE)) {
            return;
        }

        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        String monsterId = editingMonster.get(player);
        Integer equipmentSlot = editingSlot.get(player);
        ItemStack item = editingItem.get(player);
        
        if (monsterId == null || equipmentSlot == null || item == null) {
            return;
        }
        
        if (slot == 45) {
            // 返回装备选择器
            returnToEquipmentSelector(player, monsterId, equipmentSlot);
        } else if (slot == 53) {
            // 保存附魔配置
            saveEnchantedItem(player, monsterId, equipmentSlot, item);
        } else if (slot == 49) {
            // 清除所有附魔
            clearAllEnchantments(player, item);
        } else if (slot >= 9 && slot < 45) {
            // 附魔编辑
            handleEnchantmentClick(player, event, item);
        }
    }
    
    /**
     * 处理附魔点击
     */
    private void handleEnchantmentClick(Player player, InventoryClickEvent event, ItemStack item) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() != Material.ENCHANTED_BOOK) {
            return;
        }
        
        String enchantName = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName());
        Enchantment enchantment = getEnchantmentByName(enchantName);
        
        if (enchantment == null) {
            return;
        }
        
        boolean isLeftClick = event.getClick().isLeftClick();
        int currentLevel = item.getEnchantmentLevel(enchantment);
        
        if (isLeftClick) {
            // 左键：添加或提升附魔
            if (currentLevel < Integer.MAX_VALUE) {
                item.addUnsafeEnchantment(enchantment, currentLevel + 1);
                player.sendMessage(ChatColor.GREEN + "已添加/提升附魔: " + enchantName + " " + (currentLevel + 1));
            } else {
                player.sendMessage(ChatColor.RED + "附魔已达到最大等级！");
            }
        } else {
            // 右键：移除附魔
            if (currentLevel > 0) {
                item.removeEnchantment(enchantment);
                player.sendMessage(ChatColor.GREEN + "已移除附魔: " + enchantName);
            }
        }
        
        // 刷新界面
        openEnchantmentEditor(player, editingMonster.get(player), editingSlot.get(player), item);
    }
    
    /**
     * 根据名称获取附魔
     */
    private Enchantment getEnchantmentByName(String name) {
        for (Enchantment enchantment : Enchantment.values()) {
            if (getEnchantmentName(enchantment).equals(name)) {
                return enchantment;
            }
        }
        return null;
    }
    
    /**
     * 清除所有附魔
     */
    private void clearAllEnchantments(Player player, ItemStack item) {
        Set<Enchantment> enchantments = new HashSet<>(item.getEnchantments().keySet());
        for (Enchantment enchantment : enchantments) {
            item.removeEnchantment(enchantment);
        }
        
        player.sendMessage(ChatColor.GREEN + "已清除所有附魔！");
        
        // 刷新界面
        openEnchantmentEditor(player, editingMonster.get(player), editingSlot.get(player), item);
    }
    
    /**
     * 保存附魔物品
     */
    private void saveEnchantedItem(Player player, String monsterId, int equipmentSlot, ItemStack item) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        
        // 设置装备
        setEquipmentBySlot(config, equipmentSlot, item);
        
        // 保存配置
        idzManager.updateMonsterConfig(monsterId, config);
        
        player.sendMessage(ChatColor.GREEN + "附魔装备已保存！");
        
        // 返回装备编辑器
        returnToEquipmentEditor(player, monsterId);
    }
    
    /**
     * 根据槽位设置装备
     */
    private void setEquipmentBySlot(IDZMonsterConfig config, int slot, ItemStack equipment) {
        switch (slot) {
            case 10: config.setMainHand(equipment); break;
            case 12: config.setOffHand(equipment); break;
            case 14: config.setHelmet(equipment); break;
            case 16: config.setChestplate(equipment); break;
            case 24: config.setLeggings(equipment); break;
            case 26: config.setBoots(equipment); break;
        }
    }
    
    /**
     * 返回装备选择器
     */
    private void returnToEquipmentSelector(Player player, String monsterId, int equipmentSlot) {
        cleanupPlayer(player);
        player.closeInventory();
        
        // 重新打开装备选择器
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // 这里需要获取装备选择器的引用
            // 暂时先返回装备编辑器
            returnToEquipmentEditor(player, monsterId);
        }, 1L);
    }
    
    /**
     * 返回装备编辑器
     */
    private void returnToEquipmentEditor(Player player, String monsterId) {
        cleanupPlayer(player);
        player.closeInventory();
        
        // 重新打开装备编辑器
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            idzManager.getGuiManager().getEquipmentEditor().openEquipmentEditor(player, monsterId);
        }, 1L);
    }
    
    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        editingSlot.remove(player);
        editingItem.remove(player);
    }
}
