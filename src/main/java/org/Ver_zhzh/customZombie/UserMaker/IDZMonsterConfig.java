package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.EntityType;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.*;

/**
 * IDZ怪物配置数据类
 * 存储IDZ怪物的所有配置信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZMonsterConfig {
    
    // 基础信息
    private String monsterId;
    private String displayName;
    private String description;
    private EntityType entityType;
    
    // 基础属性
    private double health;
    private double movementSpeed;
    private double attackDamage;
    private double armor;
    private double armorToughness;
    private double knockbackResistance;
    
    // 装备配置
    private ItemStack mainHand;
    private ItemStack offHand;
    private ItemStack helmet;
    private ItemStack chestplate;
    private ItemStack leggings;
    private ItemStack boots;
    
    // 药水效果
    private List<PotionEffect> potionEffects;
    
    // 技能配置 - 新的结构化配置
    private List<SkillConfig> skills;

    // 向后兼容的旧格式（已废弃，仅用于迁移）
    @Deprecated
    private List<String> skillIds;
    @Deprecated
    private Map<String, Object> skillParameters;
    
    // 粒子特效配置
    private String particleType; // 保留向后兼容
    private List<String> particleTypes; // 多粒子支持
    private int particleCount;
    private double particleRange;
    private int particleInterval;
    private int previewDuration; // 预览时间（秒）
    
    // 生成配置
    private boolean naturalSpawn;
    private double spawnChance;
    private List<String> spawnBiomes;
    
    // 行为配置
    private boolean hostile;
    private double followRange;
    private boolean canPickupItems;
    private boolean canBreakDoors;
    private boolean sunlightBurns;
    
    // 创建时间
    private long createdTime;
    private long lastModified;
    
    /**
     * 构造函数 - 创建默认配置
     * 
     * @param monsterId 怪物ID
     * @param displayName 显示名称
     */
    public IDZMonsterConfig(String monsterId, String displayName) {
        this.monsterId = monsterId;
        this.displayName = displayName;
        this.description = "用户自定义的IDZ怪物";
        this.entityType = EntityType.ZOMBIE; // 默认为僵尸
        
        // 设置默认属性
        this.health = 20.0;
        this.movementSpeed = 0.23;
        this.attackDamage = 3.0;
        this.armor = 0.0;
        this.armorToughness = 0.0;
        this.knockbackResistance = 0.0;
        
        // 初始化装备（默认为空）
        this.mainHand = null;
        this.offHand = null;
        this.helmet = null;
        this.chestplate = null;
        this.leggings = null;
        this.boots = null;
        
        // 初始化药水效果列表
        this.potionEffects = new ArrayList<>();
        
        // 初始化技能配置
        this.skills = new ArrayList<>();

        // 向后兼容的旧格式初始化
        this.skillIds = new ArrayList<>();
        this.skillParameters = new HashMap<>();
        
        // 设置默认粒子特效
        this.particleType = "NONE";
        this.particleTypes = new ArrayList<>();
        this.particleCount = 10;
        this.particleRange = 2.0;
        this.particleInterval = 20; // 1秒
        this.previewDuration = 3; // 默认3秒预览
        
        // 设置默认生成配置
        this.naturalSpawn = false;
        this.spawnChance = 0.0;
        this.spawnBiomes = new ArrayList<>();
        
        // 设置默认行为
        this.hostile = true;
        this.followRange = 35.0;
        this.canPickupItems = false;
        this.canBreakDoors = false;
        this.sunlightBurns = false;
        
        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        this.createdTime = currentTime;
        this.lastModified = currentTime;
    }
    
    /**
     * 从配置节创建IDZMonsterConfig
     * 
     * @param section 配置节
     * @return IDZMonsterConfig实例，如果解析失败返回null
     */
    public static IDZMonsterConfig fromConfigurationSection(ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        try {
            String monsterId = section.getString("monster_id");
            String displayName = section.getString("display_name");
            
            if (monsterId == null || displayName == null) {
                return null;
            }
            
            IDZMonsterConfig config = new IDZMonsterConfig(monsterId, displayName);
            
            // 加载基础信息
            config.description = section.getString("description", "用户自定义的IDZ怪物");
            String entityTypeName = section.getString("entity_type", "ZOMBIE");
            try {
                config.entityType = EntityType.valueOf(entityTypeName.toUpperCase());
            } catch (IllegalArgumentException e) {
                config.entityType = EntityType.ZOMBIE;
            }
            
            // 加载基础属性
            config.health = section.getDouble("attributes.health", 20.0);
            config.movementSpeed = section.getDouble("attributes.movement_speed", 0.23);
            config.attackDamage = section.getDouble("attributes.attack_damage", 3.0);
            config.armor = section.getDouble("attributes.armor", 0.0);
            config.armorToughness = section.getDouble("attributes.armor_toughness", 0.0);
            config.knockbackResistance = section.getDouble("attributes.knockback_resistance", 0.0);
            
            // 加载装备配置
            config.loadEquipmentFromConfig(section.getConfigurationSection("equipment"));
            
            // 加载药水效果
            config.loadPotionEffectsFromConfig(section.getConfigurationSection("potion_effects"));
            
            // 加载技能配置 - 支持新格式和旧格式
            config.loadSkillsFromConfig(section.getConfigurationSection("skills"));
            
            // 加载粒子特效配置
            config.particleType = section.getString("particles.type", "NONE");
            config.particleTypes = section.getStringList("particles.types");
            config.particleCount = section.getInt("particles.count", 10);
            config.particleRange = section.getDouble("particles.range", 2.0);
            config.particleInterval = section.getInt("particles.interval", 20);
            config.previewDuration = section.getInt("particles.preview_duration", 3);

            // 向后兼容性处理
            if (config.particleTypes.isEmpty() && !config.particleType.equals("NONE")) {
                config.particleTypes.add(config.particleType);
            }
            
            // 加载生成配置
            config.naturalSpawn = section.getBoolean("spawn.natural_spawn", false);
            config.spawnChance = section.getDouble("spawn.spawn_chance", 0.0);
            config.spawnBiomes = section.getStringList("spawn.biomes");
            
            // 加载行为配置
            config.hostile = section.getBoolean("behavior.hostile", true);
            config.followRange = section.getDouble("behavior.follow_range", 35.0);
            config.canPickupItems = section.getBoolean("behavior.can_pickup_items", false);
            config.canBreakDoors = section.getBoolean("behavior.can_break_doors", false);
            config.sunlightBurns = section.getBoolean("behavior.sunlight_burns", false);
            
            // 加载时间戳
            config.createdTime = section.getLong("timestamps.created", System.currentTimeMillis());
            config.lastModified = section.getLong("timestamps.last_modified", System.currentTimeMillis());
            
            return config;
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 转换为配置节
     * 
     * @return 配置节
     */
    public ConfigurationSection toConfigurationSection() {
        ConfigurationSection section = new MemoryConfiguration();
        
        // 保存基础信息
        section.set("monster_id", monsterId);
        section.set("display_name", displayName);
        section.set("description", description);
        section.set("entity_type", entityType.name());
        
        // 保存基础属性
        section.set("attributes.health", health);
        section.set("attributes.movement_speed", movementSpeed);
        section.set("attributes.attack_damage", attackDamage);
        section.set("attributes.armor", armor);
        section.set("attributes.armor_toughness", armorToughness);
        section.set("attributes.knockback_resistance", knockbackResistance);
        
        // 保存装备配置
        saveEquipmentToConfig(section.createSection("equipment"));
        
        // 保存药水效果
        savePotionEffectsToConfig(section.createSection("potion_effects"));
        
        // 保存技能配置 - 使用新格式
        saveSkillsToConfig(section.createSection("skills"));
        
        // 保存粒子特效配置
        section.set("particles.type", particleType);
        section.set("particles.types", particleTypes);
        section.set("particles.count", particleCount);
        section.set("particles.range", particleRange);
        section.set("particles.interval", particleInterval);
        section.set("particles.preview_duration", previewDuration);
        
        // 保存生成配置
        section.set("spawn.natural_spawn", naturalSpawn);
        section.set("spawn.spawn_chance", spawnChance);
        section.set("spawn.biomes", spawnBiomes);
        
        // 保存行为配置
        section.set("behavior.hostile", hostile);
        section.set("behavior.follow_range", followRange);
        section.set("behavior.can_pickup_items", canPickupItems);
        section.set("behavior.can_break_doors", canBreakDoors);
        section.set("behavior.sunlight_burns", sunlightBurns);
        
        // 保存时间戳
        section.set("timestamps.created", createdTime);
        section.set("timestamps.last_modified", System.currentTimeMillis());
        
        return section;
    }
    
    /**
     * 从配置加载装备
     */
    private void loadEquipmentFromConfig(ConfigurationSection section) {
        if (section == null) return;

        this.mainHand = loadItemFromConfig(section, "main_hand");
        this.offHand = loadItemFromConfig(section, "off_hand");
        this.helmet = loadItemFromConfig(section, "helmet");
        this.chestplate = loadItemFromConfig(section, "chestplate");
        this.leggings = loadItemFromConfig(section, "leggings");
        this.boots = loadItemFromConfig(section, "boots");
    }

    /**
     * 从配置加载单个物品
     */
    private ItemStack loadItemFromConfig(ConfigurationSection section, String key) {
        Object value = section.get(key);

        if (value == null || "NONE".equals(value)) {
            return null;
        }

        // 如果是字符串，说明是旧格式（只有材质）
        if (value instanceof String) {
            String materialName = (String) value;
            try {
                return new ItemStack(Material.valueOf(materialName));
            } catch (IllegalArgumentException e) {
                return null;
            }
        }

        // 如果是ConfigurationSection，说明是新格式
        if (value instanceof ConfigurationSection || section.isConfigurationSection(key)) {
            ConfigurationSection itemSection = section.getConfigurationSection(key);
            if (itemSection == null) {
                return null;
            }

            try {
                // 加载基础物品
                String typeName = itemSection.getString("type");
                if (typeName == null) {
                    return null;
                }

                Material material = Material.valueOf(typeName);
                int amount = itemSection.getInt("amount", 1);
                ItemStack item = new ItemStack(material, amount);

                // 加载附魔
                if (itemSection.isConfigurationSection("enchantments")) {
                    ConfigurationSection enchantSection = itemSection.getConfigurationSection("enchantments");
                    for (String enchantKey : enchantSection.getKeys(false)) {
                        try {
                            Enchantment enchantment = Enchantment.getByKey(NamespacedKey.minecraft(enchantKey));
                            if (enchantment != null) {
                                int level = enchantSection.getInt(enchantKey);
                                item.addUnsafeEnchantment(enchantment, level);
                            }
                        } catch (Exception e) {
                            // 忽略无效的附魔
                        }
                    }
                }

                // 加载物品元数据
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    if (itemSection.contains("display-name")) {
                        meta.setDisplayName(itemSection.getString("display-name"));
                    }
                    if (itemSection.contains("lore")) {
                        meta.setLore(itemSection.getStringList("lore"));
                    }
                    item.setItemMeta(meta);
                }

                return item;

            } catch (Exception e) {
                return null;
            }
        }

        // 如果是Map，尝试使用Bukkit的反序列化（兼容性）
        if (value instanceof Map) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> itemData = (Map<String, Object>) value;
                return ItemStack.deserialize(itemData);
            } catch (Exception e) {
                // 如果反序列化失败，尝试从材质创建基础物品
                Object typeObj = ((Map<?, ?>) value).get("type");
                if (typeObj instanceof String) {
                    try {
                        return new ItemStack(Material.valueOf((String) typeObj));
                    } catch (IllegalArgumentException ignored) {}
                }
                return null;
            }
        }

        return null;
    }
    
    /**
     * 保存装备到配置
     */
    private void saveEquipmentToConfig(ConfigurationSection section) {
        saveItemToConfig(section, "main_hand", mainHand);
        saveItemToConfig(section, "off_hand", offHand);
        saveItemToConfig(section, "helmet", helmet);
        saveItemToConfig(section, "chestplate", chestplate);
        saveItemToConfig(section, "leggings", leggings);
        saveItemToConfig(section, "boots", boots);
    }

    /**
     * 保存单个物品到配置
     */
    private void saveItemToConfig(ConfigurationSection section, String key, ItemStack item) {
        if (item == null) {
            section.set(key, "NONE");
            return;
        }

        // 创建物品配置节
        ConfigurationSection itemSection = section.createSection(key);

        // 保存基础物品信息
        itemSection.set("type", item.getType().name());
        itemSection.set("amount", item.getAmount());

        // 保存附魔信息
        if (!item.getEnchantments().isEmpty()) {
            ConfigurationSection enchantSection = itemSection.createSection("enchantments");
            for (Map.Entry<Enchantment, Integer> entry : item.getEnchantments().entrySet()) {
                enchantSection.set(entry.getKey().getKey().getKey(), entry.getValue());
            }
        }

        // 保存物品元数据（如果有）
        if (item.hasItemMeta()) {
            ItemMeta meta = item.getItemMeta();
            if (meta.hasDisplayName()) {
                itemSection.set("display-name", meta.getDisplayName());
            }
            if (meta.hasLore()) {
                itemSection.set("lore", meta.getLore());
            }
        }
    }
    
    /**
     * 从配置加载药水效果
     */
    private void loadPotionEffectsFromConfig(ConfigurationSection section) {
        if (section == null) return;
        
        this.potionEffects.clear();
        for (String key : section.getKeys(false)) {
            ConfigurationSection effectSection = section.getConfigurationSection(key);
            if (effectSection != null) {
                try {
                    PotionEffectType type = PotionEffectType.getByName(effectSection.getString("type"));
                    int duration = effectSection.getInt("duration", 600);
                    int amplifier = effectSection.getInt("amplifier", 0);
                    boolean ambient = effectSection.getBoolean("ambient", false);
                    boolean particles = effectSection.getBoolean("particles", true);
                    
                    if (type != null) {
                        this.potionEffects.add(new PotionEffect(type, duration, amplifier, ambient, particles));
                    }
                } catch (Exception ignored) {}
            }
        }
    }
    
    /**
     * 保存药水效果到配置
     */
    private void savePotionEffectsToConfig(ConfigurationSection section) {
        for (int i = 0; i < potionEffects.size(); i++) {
            PotionEffect effect = potionEffects.get(i);
            ConfigurationSection effectSection = section.createSection("effect_" + i);
            effectSection.set("type", effect.getType().getName());
            effectSection.set("duration", effect.getDuration());
            effectSection.set("amplifier", effect.getAmplifier());
            effectSection.set("ambient", effect.isAmbient());
            effectSection.set("particles", effect.hasParticles());
        }
    }
    
    // Getter和Setter方法
    public String getMonsterId() { return monsterId; }
    public void setMonsterId(String monsterId) { this.monsterId = monsterId; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public EntityType getEntityType() { return entityType; }
    public void setEntityType(EntityType entityType) { this.entityType = entityType; }
    
    public double getHealth() { return health; }
    public void setHealth(double health) { this.health = health; }
    
    public double getMovementSpeed() { return movementSpeed; }
    public void setMovementSpeed(double movementSpeed) { this.movementSpeed = movementSpeed; }
    
    public double getAttackDamage() { return attackDamage; }
    public void setAttackDamage(double attackDamage) { this.attackDamage = attackDamage; }
    
    public double getArmor() { return armor; }
    public void setArmor(double armor) { this.armor = armor; }
    
    // 新的技能配置方法
    public List<SkillConfig> getSkills() { return new ArrayList<>(skills); }
    public void setSkills(List<SkillConfig> skills) { this.skills = new ArrayList<>(skills); }

    /**
     * 添加技能配置
     */
    public void addSkill(SkillConfig skillConfig) {
        if (skillConfig != null && !skills.contains(skillConfig)) {
            skills.add(skillConfig);
        }
    }

    /**
     * 移除技能配置
     */
    public boolean removeSkill(String skillName) {
        return skills.removeIf(skill -> skill.getSkillName().equals(skillName));
    }

    /**
     * 清空所有技能配置
     */
    public void clearSkills() {
        skills.clear();
    }

    /**
     * 获取指定技能的配置
     */
    public SkillConfig getSkillConfig(String skillName) {
        return skills.stream()
                .filter(skill -> skill.getSkillName().equals(skillName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查是否包含指定技能
     */
    public boolean hasSkill(String skillName) {
        return skills.stream().anyMatch(skill -> skill.getSkillName().equals(skillName));
    }

    // 向后兼容的旧方法（已废弃）
    @Deprecated
    public List<String> getSkillIds() {
        // 如果新格式有数据，从新格式生成
        if (!skills.isEmpty()) {
            return skills.stream().map(SkillConfig::getSkillName).collect(java.util.stream.Collectors.toList());
        }
        return new ArrayList<>(skillIds);
    }

    @Deprecated
    public void setSkillIds(List<String> skillIds) {
        this.skillIds = new ArrayList<>(skillIds);
        // 同时更新新格式
        this.skills.clear();
        for (String skillId : skillIds) {
            this.skills.add(new SkillConfig(skillId));
        }
    }

    @Deprecated
    public Map<String, Object> getSkillParameters() {
        // 如果新格式有数据，从新格式生成
        if (!skills.isEmpty()) {
            Map<String, Object> allParams = new HashMap<>();
            for (SkillConfig skill : skills) {
                for (Map.Entry<String, Object> entry : skill.getParameters().entrySet()) {
                    allParams.put(skill.getSkillName() + "." + entry.getKey(), entry.getValue());
                }
            }
            return allParams;
        }
        return new HashMap<>(skillParameters);
    }

    @Deprecated
    public void setSkillParameters(Map<String, Object> skillParameters) {
        this.skillParameters = new HashMap<>(skillParameters);
        // 同时更新新格式
        updateSkillsFromParameters(skillParameters);
    }
    
    public boolean isHostile() { return hostile; }
    public void setHostile(boolean hostile) { this.hostile = hostile; }

    public String getParticleType() { return particleType; }
    public void setParticleType(String particleType) {
        this.particleType = particleType;
        // 同步到新的多粒子系统
        if (particleTypes.isEmpty() && !particleType.equals("NONE")) {
            particleTypes.clear();
            particleTypes.add(particleType);
        }
    }

    public List<String> getParticleTypes() { return particleTypes; }
    public void setParticleTypes(List<String> particleTypes) {
        this.particleTypes = particleTypes;
        // 向后兼容性处理
        if (particleTypes.size() == 1) {
            this.particleType = particleTypes.get(0);
        } else if (particleTypes.size() > 1) {
            this.particleType = "MULTIPLE";
        } else {
            this.particleType = "NONE";
        }
    }

    public int getParticleCount() { return particleCount; }
    public void setParticleCount(int particleCount) { this.particleCount = particleCount; }

    public double getParticleRange() { return particleRange; }
    public void setParticleRange(double particleRange) { this.particleRange = particleRange; }

    public int getParticleInterval() { return particleInterval; }
    public void setParticleInterval(int particleInterval) { this.particleInterval = particleInterval; }

    public int getPreviewDuration() { return previewDuration; }
    public void setPreviewDuration(int previewDuration) { this.previewDuration = previewDuration; }
    
    /**
     * 从配置节加载技能配置
     */
    private void loadSkillsFromConfig(ConfigurationSection skillsSection) {
        if (skillsSection == null) {
            return;
        }

        // 尝试加载新格式
        if (skillsSection.isList("skills")) {
            List<Map<?, ?>> skillsList = skillsSection.getMapList("skills");
            for (Map<?, ?> skillMap : skillsList) {
                if (skillMap.containsKey("skill_name")) {
                    String skillName = skillMap.get("skill_name").toString();
                    SkillConfig skillConfig = new SkillConfig(skillName);

                    if (skillMap.containsKey("parameters") && skillMap.get("parameters") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> params = (Map<String, Object>) skillMap.get("parameters");
                        skillConfig.setParameters(params);
                    }

                    skills.add(skillConfig);
                }
            }
        } else {
            // 向后兼容：加载旧格式
            skillIds = skillsSection.getStringList("skill_ids");
            if (skillsSection.getConfigurationSection("parameters") != null) {
                skillParameters = skillsSection.getConfigurationSection("parameters").getValues(false);
                // 将旧格式转换为新格式
                updateSkillsFromParameters(skillParameters);
            } else {
                // 如果只有技能ID没有参数，创建空的技能配置
                for (String skillId : skillIds) {
                    skills.add(new SkillConfig(skillId));
                }
            }
        }
    }

    /**
     * 保存技能配置到配置节
     */
    private void saveSkillsToConfig(ConfigurationSection skillsSection) {
        if (skills.isEmpty()) {
            System.out.println("[DEBUG] saveSkillsToConfig: skills list is empty");
            return;
        }

        System.out.println("[DEBUG] saveSkillsToConfig: saving " + skills.size() + " skills");
        List<Map<String, Object>> skillsList = new ArrayList<>();
        for (SkillConfig skill : skills) {
            System.out.println("[DEBUG] Processing skill: " + skill.getSkillName());
            System.out.println("[DEBUG] Skill parameters: " + skill.getParameters());
            System.out.println("[DEBUG] Parameters empty? " + skill.getParameters().isEmpty());

            Map<String, Object> skillMap = new HashMap<>();
            skillMap.put("skill_name", skill.getSkillName());

            if (!skill.getParameters().isEmpty()) {
                skillMap.put("parameters", skill.getParameters());
                System.out.println("[DEBUG] Added parameters to skillMap");
            } else {
                System.out.println("[DEBUG] Skipped parameters - empty");
            }

            skillsList.add(skillMap);
            System.out.println("[DEBUG] Final skillMap: " + skillMap);
        }

        skillsSection.set("skills", skillsList);
        System.out.println("[DEBUG] saveSkillsToConfig completed");
    }

    /**
     * 从旧格式参数更新新格式技能配置
     */
    private void updateSkillsFromParameters(Map<String, Object> parameters) {
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 检查是否是技能特定参数格式 (skillName.paramName)
            if (key.contains(".")) {
                String[] parts = key.split("\\.", 2);
                if (parts.length == 2) {
                    String skillName = parts[0];
                    String paramName = parts[1];

                    // 查找或创建技能配置
                    SkillConfig skillConfig = getSkillConfig(skillName);
                    if (skillConfig == null) {
                        skillConfig = new SkillConfig(skillName);
                        skills.add(skillConfig);
                    }

                    skillConfig.setParameter(paramName, value);
                }
            }
        }
    }

    /**
     * 更新最后修改时间
     */
    public void updateLastModified() {
        this.lastModified = System.currentTimeMillis();
    }

    // ========================================
    // 装备相关的Getter和Setter方法
    // ========================================

    public ItemStack getMainHand() { return mainHand; }
    public void setMainHand(ItemStack mainHand) { this.mainHand = mainHand; }

    public ItemStack getOffHand() { return offHand; }
    public void setOffHand(ItemStack offHand) { this.offHand = offHand; }

    public ItemStack getHelmet() { return helmet; }
    public void setHelmet(ItemStack helmet) { this.helmet = helmet; }

    public ItemStack getChestplate() { return chestplate; }
    public void setChestplate(ItemStack chestplate) { this.chestplate = chestplate; }

    public ItemStack getLeggings() { return leggings; }
    public void setLeggings(ItemStack leggings) { this.leggings = leggings; }

    public ItemStack getBoots() { return boots; }
    public void setBoots(ItemStack boots) { this.boots = boots; }
    
    @Override
    public String toString() {
        return "IDZMonsterConfig{" +
                "monsterId='" + monsterId + '\'' +
                ", displayName='" + displayName + '\'' +
                ", entityType=" + entityType +
                ", health=" + health +
                ", attackDamage=" + attackDamage +
                ", skillCount=" + skillIds.size() +
                '}';
    }
}
