package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;
import org.bukkit.metadata.FixedMetadataValue;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;

/**
 * IDZ自定义怪物管理器
 * 负责IDZ怪物的创建、删除、生成和管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZMonsterManager {
    
    private final Plugin plugin;
    private final Logger logger;

    // GUI管理器
    private IDZMonsterGUI guiManager;
    
    // 配置文件管理
    private File configFile;
    private FileConfiguration config;
    
    // IDZ怪物配置缓存
    private final Map<String, IDZMonsterConfig> monsterConfigs;
    
    // 已生成的IDZ怪物实体追踪
    private final Map<String, Set<LivingEntity>> spawnedMonsters;
    
    // 调试模式
    private boolean debugMode;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public IDZMonsterManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.monsterConfigs = new HashMap<>();
        this.spawnedMonsters = new HashMap<>();
        this.debugMode = false;
        
        // 初始化配置文件
        initializeConfig();
        
        // 加载现有配置
        loadAllConfigs();

        // 初始化GUI管理器
        this.guiManager = new IDZMonsterGUI(plugin, this);

        logger.info("IDZ怪物管理器初始化完成");
    }
    
    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        configFile = new File(plugin.getDataFolder(), "idz_monsters.yml");
        
        // 如果配置文件不存在，创建默认配置
        if (!configFile.exists()) {
            try {
                configFile.getParentFile().mkdirs();
                configFile.createNewFile();
                
                // 创建默认配置内容
                config = YamlConfiguration.loadConfiguration(configFile);
                config.set("version", "1.0");
                config.set("debug_mode", false);
                config.set("monsters", new HashMap<String, Object>());
                
                // 添加配置说明
                config.setComments("version", Arrays.asList(
                    "IDZ自定义怪物配置文件",
                    "版本: 1.0",
                    "此文件存储用户创建的所有IDZ系列怪物配置"
                ));
                
                saveConfig();
                logger.info("创建IDZ怪物配置文件: " + configFile.getPath());
                
            } catch (IOException e) {
                logger.severe("无法创建IDZ怪物配置文件: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            config = YamlConfiguration.loadConfiguration(configFile);
        }
        
        // 加载调试模式设置
        debugMode = config.getBoolean("debug_mode", false);
    }
    
    /**
     * 加载所有IDZ怪物配置
     */
    private void loadAllConfigs() {
        if (config.getConfigurationSection("monsters") == null) {
            if (debugMode) {
                logger.info("没有找到IDZ怪物配置，跳过加载");
            }
            return;
        }
        
        Set<String> monsterIds = config.getConfigurationSection("monsters").getKeys(false);
        int loadedCount = 0;
        
        for (String monsterId : monsterIds) {
            try {
                IDZMonsterConfig monsterConfig = IDZMonsterConfig.fromConfigurationSection(
                    config.getConfigurationSection("monsters." + monsterId)
                );
                
                if (monsterConfig != null) {
                    monsterConfigs.put(monsterId, monsterConfig);
                    loadedCount++;
                    
                    if (debugMode) {
                        logger.info("加载IDZ怪物配置: " + monsterId + " - " + monsterConfig.getDisplayName());
                    }
                }
                
            } catch (Exception e) {
                logger.warning("加载IDZ怪物配置失败: " + monsterId + " - " + e.getMessage());
                if (debugMode) {
                    e.printStackTrace();
                }
            }
        }
        
        logger.info("成功加载 " + loadedCount + " 个IDZ怪物配置");
    }
    
    /**
     * 创建新的IDZ怪物配置
     * 
     * @param monsterId 怪物ID
     * @param displayName 显示名称
     * @return 是否创建成功
     */
    public boolean createMonster(String monsterId, String displayName) {
        if (monsterId == null || monsterId.trim().isEmpty()) {
            logger.warning("怪物ID不能为空");
            return false;
        }
        
        // 检查ID是否已存在
        if (monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物ID已存在: " + monsterId);
            return false;
        }
        
        // 验证ID格式（必须以idz开头）
        if (!monsterId.toLowerCase().startsWith("idz")) {
            logger.warning("IDZ怪物ID必须以'idz'开头: " + monsterId);
            return false;
        }
        
        try {
            // 创建默认配置
            IDZMonsterConfig newConfig = new IDZMonsterConfig(monsterId, displayName);
            
            // 添加到缓存
            monsterConfigs.put(monsterId, newConfig);
            
            // 保存到配置文件
            saveMonsterConfig(monsterId, newConfig);
            
            logger.info("成功创建IDZ怪物: " + monsterId + " - " + displayName);
            return true;
            
        } catch (Exception e) {
            logger.severe("创建IDZ怪物失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 删除IDZ怪物配置
     * 
     * @param monsterId 怪物ID
     * @return 是否删除成功
     */
    public boolean deleteMonster(String monsterId) {
        if (!monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物不存在: " + monsterId);
            return false;
        }
        
        try {
            // 清理已生成的怪物实体
            cleanupSpawnedMonsters(monsterId);
            
            // 从缓存中移除
            IDZMonsterConfig removedConfig = monsterConfigs.remove(monsterId);
            
            // 从配置文件中移除
            config.set("monsters." + monsterId, null);
            saveConfig();
            
            logger.info("成功删除IDZ怪物: " + monsterId + " - " + removedConfig.getDisplayName());
            return true;
            
        } catch (Exception e) {
            logger.severe("删除IDZ怪物失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取IDZ怪物配置
     * 
     * @param monsterId 怪物ID
     * @return 怪物配置，如果不存在返回null
     */
    public IDZMonsterConfig getMonsterConfig(String monsterId) {
        return monsterConfigs.get(monsterId);
    }
    
    /**
     * 获取所有IDZ怪物ID列表
     * 
     * @return 怪物ID列表
     */
    public Set<String> getAllMonsterIds() {
        return new HashSet<>(monsterConfigs.keySet());
    }
    
    /**
     * 获取所有IDZ怪物配置
     * 
     * @return 怪物配置映射
     */
    public Map<String, IDZMonsterConfig> getAllMonsterConfigs() {
        return new HashMap<>(monsterConfigs);
    }
    
    /**
     * 检查IDZ怪物是否存在
     * 
     * @param monsterId 怪物ID
     * @return 是否存在
     */
    public boolean monsterExists(String monsterId) {
        return monsterConfigs.containsKey(monsterId);
    }
    
    /**
     * 更新IDZ怪物配置
     * 
     * @param monsterId 怪物ID
     * @param config 新的配置
     * @return 是否更新成功
     */
    public boolean updateMonsterConfig(String monsterId, IDZMonsterConfig config) {
        if (!monsterConfigs.containsKey(monsterId)) {
            logger.warning("IDZ怪物不存在: " + monsterId);
            return false;
        }
        
        try {
            // 更新缓存
            monsterConfigs.put(monsterId, config);
            
            // 保存到配置文件
            saveMonsterConfig(monsterId, config);
            
            if (debugMode) {
                logger.info("更新IDZ怪物配置: " + monsterId);
            }
            return true;
            
        } catch (Exception e) {
            logger.severe("更新IDZ怪物配置失败: " + monsterId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 保存怪物配置到文件
     * 
     * @param monsterId 怪物ID
     * @param monsterConfig 怪物配置
     */
    private void saveMonsterConfig(String monsterId, IDZMonsterConfig monsterConfig) {
        config.set("monsters." + monsterId, monsterConfig.toConfigurationSection());
        saveConfig();
    }
    
    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            logger.severe("保存IDZ怪物配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 清理指定怪物的所有已生成实体
     * 
     * @param monsterId 怪物ID
     */
    private void cleanupSpawnedMonsters(String monsterId) {
        Set<LivingEntity> entities = spawnedMonsters.get(monsterId);
        if (entities != null) {
            int removedCount = 0;
            for (LivingEntity entity : new HashSet<>(entities)) {
                if (entity != null && !entity.isDead()) {
                    entity.remove();
                    removedCount++;
                }
            }
            entities.clear();
            
            if (debugMode && removedCount > 0) {
                logger.info("清理IDZ怪物实体: " + monsterId + " - " + removedCount + "个");
            }
        }
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        logger.info("重新加载IDZ怪物配置...");
        
        // 清理现有配置
        monsterConfigs.clear();
        
        // 重新初始化配置文件
        initializeConfig();
        
        // 重新加载配置
        loadAllConfigs();
        
        logger.info("IDZ怪物配置重新加载完成");
    }
    
    /**
     * 设置调试模式
     * 
     * @param debug 是否启用调试模式
     */
    public void setDebugMode(boolean debug) {
        this.debugMode = debug;
        config.set("debug_mode", debug);
        saveConfig();
        
        logger.info("IDZ怪物管理器调试模式: " + (debug ? "启用" : "禁用"));
    }
    
    /**
     * 获取调试模式状态
     * 
     * @return 是否启用调试模式
     */
    public boolean isDebugMode() {
        return debugMode;
    }
    
    /**
     * 打开GUI编辑器
     *
     * @param player 玩家
     * @param monsterId 怪物ID
     */
    public void openGUIEditor(org.bukkit.entity.Player player, String monsterId) {
        if (guiManager != null) {
            guiManager.openMainEditor(player, monsterId);
        } else {
            player.sendMessage(org.bukkit.ChatColor.RED + "GUI管理器未初始化！");
        }
    }

    /**
     * 获取GUI管理器
     *
     * @return GUI管理器实例
     */
    public IDZMonsterGUI getGuiManager() {
        return guiManager;
    }

    /**
     * 关闭管理器，清理资源
     */
    public void shutdown() {
        logger.info("关闭IDZ怪物管理器...");

        // 清理GUI管理器
        if (guiManager != null) {
            guiManager.shutdown();
        }

        // 清理所有已生成的怪物实体
        for (String monsterId : spawnedMonsters.keySet()) {
            cleanupSpawnedMonsters(monsterId);
        }

        // 保存配置
        saveConfig();

        logger.info("IDZ怪物管理器已关闭");
    }
}
