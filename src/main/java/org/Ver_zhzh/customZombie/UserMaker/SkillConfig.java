package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;

import java.util.HashMap;
import java.util.Map;

/**
 * 技能配置类
 * 包含技能名称和对应的参数配置
 */
public class SkillConfig {
    
    private String skillName;
    private Map<String, Object> parameters;
    
    /**
     * 构造函数
     *
     * @param skillName 技能名称
     */
    public SkillConfig(String skillName) {
        System.out.println("[DEBUG] SkillConfig constructor (empty): " + skillName);
        this.skillName = skillName;
        this.parameters = new HashMap<>();
    }
    
    /**
     * 构造函数
     *
     * @param skillName 技能名称
     * @param parameters 参数映射
     */
    public SkillConfig(String skillName, Map<String, Object> parameters) {
        System.out.println("[DEBUG] SkillConfig constructor (with params): " + skillName + ", params: " + parameters);
        this.skillName = skillName;
        this.parameters = new HashMap<>(parameters);
        System.out.println("[DEBUG] SkillConfig constructor completed, stored params: " + this.parameters);
    }
    
    /**
     * 从配置节创建SkillConfig
     * 
     * @param section 配置节
     * @return SkillConfig实例，如果解析失败返回null
     */
    public static SkillConfig fromConfigurationSection(ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        try {
            String skillName = section.getString("skill_name");
            if (skillName == null) {
                return null;
            }
            
            SkillConfig skillConfig = new SkillConfig(skillName);
            
            // 加载参数
            ConfigurationSection parametersSection = section.getConfigurationSection("parameters");
            if (parametersSection != null) {
                skillConfig.parameters = parametersSection.getValues(false);
            }
            
            return skillConfig;
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 转换为配置节
     * 
     * @return 配置节
     */
    public ConfigurationSection toConfigurationSection() {
        ConfigurationSection section = new MemoryConfiguration();
        
        section.set("skill_name", skillName);
        
        if (!parameters.isEmpty()) {
            ConfigurationSection parametersSection = section.createSection("parameters");
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                parametersSection.set(entry.getKey(), entry.getValue());
            }
        }
        
        return section;
    }
    
    /**
     * 获取技能名称
     * 
     * @return 技能名称
     */
    public String getSkillName() {
        return skillName;
    }
    
    /**
     * 设置技能名称
     * 
     * @param skillName 技能名称
     */
    public void setSkillName(String skillName) {
        this.skillName = skillName;
    }
    
    /**
     * 获取参数映射
     *
     * @return 参数映射的副本
     */
    public Map<String, Object> getParameters() {
        System.out.println("[DEBUG] getParameters called for skill: " + skillName + ", returning: " + parameters);
        return new HashMap<>(parameters);
    }
    
    /**
     * 设置参数映射
     * 
     * @param parameters 参数映射
     */
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = new HashMap<>(parameters);
    }
    
    /**
     * 获取指定参数的值
     * 
     * @param parameterName 参数名
     * @return 参数值，如果不存在返回null
     */
    public Object getParameter(String parameterName) {
        return parameters.get(parameterName);
    }
    
    /**
     * 获取指定参数的值，如果不存在返回默认值
     * 
     * @param parameterName 参数名
     * @param defaultValue 默认值
     * @return 参数值或默认值
     */
    public Object getParameter(String parameterName, Object defaultValue) {
        return parameters.getOrDefault(parameterName, defaultValue);
    }
    
    /**
     * 设置参数值
     * 
     * @param parameterName 参数名
     * @param value 参数值
     */
    public void setParameter(String parameterName, Object value) {
        parameters.put(parameterName, value);
    }
    
    /**
     * 移除参数
     * 
     * @param parameterName 参数名
     * @return 被移除的参数值，如果不存在返回null
     */
    public Object removeParameter(String parameterName) {
        return parameters.remove(parameterName);
    }
    
    /**
     * 检查是否包含指定参数
     * 
     * @param parameterName 参数名
     * @return 是否包含该参数
     */
    public boolean hasParameter(String parameterName) {
        return parameters.containsKey(parameterName);
    }
    
    /**
     * 获取参数数量
     * 
     * @return 参数数量
     */
    public int getParameterCount() {
        return parameters.size();
    }
    
    /**
     * 清空所有参数
     */
    public void clearParameters() {
        parameters.clear();
    }
    
    @Override
    public String toString() {
        return "SkillConfig{" +
                "skillName='" + skillName + '\'' +
                ", parameterCount=" + parameters.size() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SkillConfig that = (SkillConfig) obj;
        return skillName != null ? skillName.equals(that.skillName) : that.skillName == null;
    }
    
    @Override
    public int hashCode() {
        return skillName != null ? skillName.hashCode() : 0;
    }
}
