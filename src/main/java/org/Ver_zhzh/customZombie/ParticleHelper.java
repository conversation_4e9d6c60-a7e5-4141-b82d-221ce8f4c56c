package org.Ver_zhzh.customZombie;

import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 粒子效果辅助类，使用原生Bukkit API实现粒子效果
 * 提供与原ParticleEffect类似的接口以及更多高级效果
 */
public class ParticleHelper {
    private final Plugin plugin;
    private final List<BukkitTask> activeTasks = new ArrayList<>();

    public ParticleHelper(Plugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 发送基础粒子效果给所有玩家
     * 模拟原ParticleEffect.send方法
     *
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 粒子数量
     * @param particle 粒子类型
     */
    public void displayParticle(Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        if (location.getWorld() == null) return;
        
        location.getWorld().spawnParticle(
            particle,
            location,
            count,
            offsetX,
            offsetY,
            offsetZ,
            speed
        );
    }

    /**
     * 向指定玩家发送基础粒子效果
     *
     * @param player 玩家
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 粒子数量
     * @param particle 粒子类型
     */
    public void displayParticle(Player player, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        player.spawnParticle(
            particle,
            location,
            count,
            offsetX,
            offsetY,
            offsetZ,
            speed
        );
    }

    /**
     * 向多个玩家显示粒子效果(兼容原ParticleEffect.send方法)
     *
     * @param players 玩家集合
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 数量
     * @param particle 粒子类型
     */
    public void displayParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        for (Player player : players) {
            displayParticle(player, location, offsetX, offsetY, offsetZ, speed, count, particle);
        }
    }

    /**
     * 创建球体效果
     *
     * @param location 中心位置
     * @param radius 半径
     * @param density 密度
     * @param particle 粒子类型
     */
    public void displaySphere(Location location, float radius, int density, Particle particle) {
        if (location.getWorld() == null) return;
        
        World world = location.getWorld();
        
        // 在球表面生成粒子
        for (int i = 0; i < density; i++) {
            double phi = Math.random() * Math.PI * 2;
            double theta = Math.random() * Math.PI;
            
            double x = radius * Math.sin(theta) * Math.cos(phi);
            double y = radius * Math.sin(theta) * Math.sin(phi);
            double z = radius * Math.cos(theta);
            
            Location particleLoc = location.clone().add(x, y, z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建立方体效果
     *
     * @param location 中心位置
     * @param size 边长
     * @param particle 粒子类型
     */
    public void displayCube(Location location, float size, Particle particle) {
        if (location.getWorld() == null) return;
        
        World world = location.getWorld();
        float half = size / 2;
        
        // 创建立方体的12条边
        for (float x = -half; x <= half; x += size) {
            for (float y = -half; y <= half; y += size) {
                for (float z = -half; z <= half; z += size) {
                    if ((Math.abs(x) == half ? 1 : 0) + (Math.abs(y) == half ? 1 : 0) + (Math.abs(z) == half ? 1 : 0) >= 2) {
                        Location particleLoc = location.clone().add(x, y, z);
                        world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
                    }
                }
            }
        }
    }

    /**
     * 创建爆炸效果
     *
     * @param location 爆炸位置
     * @param particle 粒子类型
     * @param amount 粒子数量
     */
    public void displayExplosion(Location location, Particle particle, int amount) {
        if (location.getWorld() == null) return;
        
        // 创建一个爆炸效果，粒子从中心向外扩散
        World world = location.getWorld();
        
        // 从中心向外爆炸的效果
        BukkitTask task = new BukkitRunnable() {
            double radius = 0.2;
            int iteration = 0;
            final int maxIterations = 10;
            
            @Override
            public void run() {
                if (iteration >= maxIterations) {
                    cancel();
                    activeTasks.remove(this);
                    return;
                }
                
                // 在球体表面生成粒子
                for (int i = 0; i < amount / maxIterations; i++) {
                    double phi = Math.random() * Math.PI * 2;
                    double theta = Math.random() * Math.PI;
                    
                    double x = radius * Math.sin(theta) * Math.cos(phi);
                    double y = radius * Math.sin(theta) * Math.sin(phi);
                    double z = radius * Math.cos(theta);
                    
                    Location particleLoc = location.clone().add(x, y, z);
                    world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
                }
                
                radius += 0.3;
                iteration++;
            }
        }.runTaskTimer(plugin, 0, 2);
        
        activeTasks.add(task);
    }

    /**
     * 创建粒子圆形
     *
     * @param location 中心位置
     * @param radius 半径
     * @param particle 粒子类型
     */
    public void displayCircle(Location location, float radius, Particle particle) {
        if (location.getWorld() == null) return;
        
        World world = location.getWorld();
        
        // 生成圆形
        for (int i = 0; i < 36; i++) {
            double angle = 2 * Math.PI * i / 36;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            
            Location particleLoc = location.clone().add(x, 0, z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * 创建粒子圆形（带更多参数）
     *
     * @param location 中心位置
     * @param radius 半径
     * @param particle 粒子类型
     * @param offsetY Y轴偏移
     * @param speed 粒子速度
     * @param count 每个位置的粒子数量
     */
    public void displayCircle(Location location, float radius, Particle particle, float offsetY, float speed, int count) {
        if (location.getWorld() == null) return;
        
        World world = location.getWorld();
        
        // 生成圆形
        for (int i = 0; i < 36; i++) {
            double angle = 2 * Math.PI * i / 36;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            
            Location particleLoc = location.clone().add(x, 0, z);
            world.spawnParticle(particle, particleLoc, count, 0, offsetY, 0, speed);
        }
    }

    /**
     * 创建云效果
     *
     * @param location 中心位置
     * @param size 大小
     * @param particle 粒子类型
     */
    public void displayCloud(Location location, float size, Particle particle) {
        if (location.getWorld() == null) return;
        
        // 生成云状效果
        for (int i = 0; i < 50; i++) {
            double x = (Math.random() - 0.5) * size;
            double y = (Math.random() - 0.5) * size * 0.5;
            double z = (Math.random() - 0.5) * size;
            
            Location particleLoc = location.clone().add(x, y, z);
            location.getWorld().spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建火焰效果
     *
     * @param location 位置
     * @param height 高度
     */
    public void displayFlame(Location location, float height) {
        if (location.getWorld() == null) return;
        
        // 持续的火焰效果
        BukkitTask task = new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 20; // 持续1秒
            
            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    cancel();
                    activeTasks.remove(this);
                    return;
                }
                
                // 在底部生成火焰粒子
                for (int i = 0; i < 5; i++) {
                    double x = (Math.random() - 0.5) * 0.5;
                    double z = (Math.random() - 0.5) * 0.5;
                    double y = Math.random() * height;
                    
                    Location particleLoc = location.clone().add(x, y, z);
                    location.getWorld().spawnParticle(Particle.FLAME, particleLoc, 1, 0, 0, 0, 0);
                    
                    // 添加一些烟雾
                    if (Math.random() > 0.7) {
                        location.getWorld().spawnParticle(Particle.SMOKE, particleLoc, 1, 0, 0, 0, 0);
                    }
                }
                
                ticks++;
            }
        }.runTaskTimer(plugin, 0, 1);
        
        activeTasks.add(task);
    }

    /**
     * 创建线效果
     *
     * @param location 起始位置
     * @param target 目标位置
     * @param particle 粒子类型
     */
    public void displayLine(Location location, Location target, Particle particle) {
        if (location.getWorld() == null || target.getWorld() == null || !location.getWorld().equals(target.getWorld())) 
            return;
        
        World world = location.getWorld();
        Vector direction = target.toVector().subtract(location.toVector());
        double length = direction.length();
        direction.normalize();
        
        // 在两点之间创建线
        double step = 0.2; // 粒子间隔
        for (double i = 0; i < length; i += step) {
            Vector position = direction.clone().multiply(i);
            Location particleLoc = location.clone().add(position);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 下面是特定粒子效果的便捷方法
     */

    /**
     * 模拟原ParticleEffect.FLAME.send方法
     */
    public void displayFlameParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        for (Player player : players) {
            player.spawnParticle(Particle.FLAME, location, count, offsetX, offsetY, offsetZ, speed);
        }
    }

    /**
     * 模拟原ParticleEffect.EXPLOSION_HUGE.send方法
     */
    public void displayHugeExplosionParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.EXPLOSION_EMITTER);
    }

    /**
     * 显示烟雾粒子
     * 
     * @param players 可见的玩家
     * @param location 位置
     * @param offsetX X轴偏移量
     * @param offsetY Y轴偏移量
     * @param offsetZ Z轴偏移量
     * @param speed 粒子速度
     * @param count 粒子数量
     */
    public void displaySmokeParticle(Collection<? extends Player> players, Location location, 
            float offsetX, float offsetY, float offsetZ, float speed, int count) {
        for (Player player : players) {
            player.spawnParticle(Particle.SMOKE, location, count, offsetX, offsetY, offsetZ, speed);
        }
    }

    /**
     * 模拟原ParticleEffect.SMOKE_LARGE.send方法
     */
    public void displayLargeSmokeParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.LARGE_SMOKE);
    }

    /**
     * 模拟原ParticleEffect.PORTAL.send方法
     */
    public void displayPortalParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.PORTAL);
    }

    /**
     * 模拟原ParticleEffect.SPELL_MOB.send方法
     */
    public void displaySpellMobParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.ENTITY_EFFECT);
    }

    /**
     * 模拟原ParticleEffect.REDSTONE.send方法
     */
    public void displayRedstoneParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.DUST);
    }

    /**
     * 显示自定义颜色的红石粒子
     *
     * @param players 玩家集合
     * @param location 粒子生成位置
     * @param red 红色分量 (0-255)
     * @param green 绿色分量 (0-255)
     * @param blue 蓝色分量 (0-255)
     * @param count 粒子数量
     */
    public void displayRedstoneParticle(Collection<? extends Player> players, Location location, int red, int green, int blue, int count) {
        if (location.getWorld() == null) return;
        
        // 转换RGB值到0-1范围
        float r = red / 255.0f;
        float g = green / 255.0f;
        float b = blue / 255.0f;
        
        Particle.DustOptions dustOptions = new Particle.DustOptions(Color.fromRGB(red, green, blue), 1.0f);
        
        for (Player player : players) {
            player.spawnParticle(Particle.DUST, location, count, 0, 0, 0, 0, dustOptions);
        }
    }

    /**
     * 模拟原ParticleEffect.SNOW_SHOVEL.send方法
     */
    public void displaySnowShovelParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.ITEM_SNOWBALL);
    }

    /**
     * 模拟原ParticleEffect.CRIT_MAGIC.send方法
     */
    public void displayCritMagicParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.ENCHANTED_HIT);
    }

    /**
     * 清理所有效果，在插件禁用时调用
     */
    public void dispose() {
        // 取消所有活动任务
        for (BukkitTask task : activeTasks) {
            task.cancel();
        }
        activeTasks.clear();
        
        Bukkit.getLogger().info("§a[ParticleHelper] 已清理所有粒子效果任务");
    }
} 