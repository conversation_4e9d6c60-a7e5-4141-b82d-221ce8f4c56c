package org.Ver_zhzh.customZombie;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.IronGolem;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

/**
 * 变异铁傀儡实体类 实现特殊能力： 1. 不再攻击敌对生物而是只攻击玩家类型 2. 向前发射远程声波弹攻击 3.
 * 向玩家发射真实草方块延伸攻击，距离减小但频率增加
 */
public class MutantIronGolem {

    // 存储变异铁傀儡任务
    private final Map<IronGolem, List<BukkitTask>> mutantIronGolemTasks = new HashMap<>();

    // 插件实例和日志记录器
    private final Plugin plugin;
    private final Logger logger;

    // 粒子效果助手
    private final ParticleHelper particleHelper;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param particleHelper 粒子效果助手
     */
    public MutantIronGolem(Plugin plugin, ParticleHelper particleHelper) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.particleHelper = particleHelper;
    }

    /**
     * 生成变异铁傀儡
     *
     * @param location 生成位置
     * @return 生成的变异铁傀儡实体
     */
    public IronGolem spawnMutantIronGolem(Location location) {
        try {
            logger.info("开始生成变异铁傀儡...");
            logger.info("生成位置: " + location.getWorld().getName()
                    + " X:" + location.getX()
                    + " Y:" + location.getY()
                    + " Z:" + location.getZ());

            // 检查世界是否有效
            if (location.getWorld() == null) {
                logger.severe("无法生成变异铁傀儡：世界对象为null");
                return null;
            }

            // 生成铁傀儡实体
            logger.info("尝试在世界 " + location.getWorld().getName() + " 生成铁傀儡实体");
            IronGolem ironGolem = (IronGolem) location.getWorld().spawnEntity(location, EntityType.IRON_GOLEM);

            if (ironGolem == null) {
                logger.severe("铁傀儡实体生成失败，返回了null");
                return null;
            }

            logger.info("铁傀儡实体生成成功，UUID: " + ironGolem.getUniqueId());

            // 设置变异铁傀儡属性
            logger.info("设置变异铁傀儡属性...");
            ironGolem.setCustomName("§8变异铁傀儡");
            ironGolem.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示

            try {
                ironGolem.setMaxHealth(2000.0);
                ironGolem.setHealth(2000.0);
                logger.info("设置生命值成功: 2000.0");
            } catch (Exception e) {
                logger.severe("设置生命值时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 添加速度4和跳跃4效果
            logger.info("添加速度和跳跃效果...");
            try {
                ironGolem.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 3)); // 速度IV
                ironGolem.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 3)); // 跳跃提升IV
                logger.info("添加效果成功");
            } catch (Exception e) {
                logger.severe("添加效果时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 给变异铁傀儡添加元数据标记
            logger.info("添加元数据标记...");
            try {
                ironGolem.setMetadata("mutantIronGolem", new FixedMetadataValue(plugin, true));
                ironGolem.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
                logger.info("添加元数据标记成功");

                // 启用敌对AI，确保主动攻击玩家而不是其他生物
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getHostileAIManager() != null) {
                        dzPlugin.getHostileAIManager().enableHostileAI(ironGolem);
                        logger.info("已为变异铁傀儡启用敌对AI");
                    }
                }
            } catch (Exception e) {
                logger.severe("添加元数据标记时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 启动变异铁傀儡的特殊任务
            logger.info("启动变异铁傀儡的特殊任务...");
            try {
                startMutantIronGolemTasks(ironGolem);
                logger.info("特殊任务启动成功");
            } catch (Exception e) {
                logger.severe("启动特殊任务时出错: " + e.getMessage());
                e.printStackTrace();
            }

            logger.info("变异铁傀儡生成完成");
            return ironGolem;
        } catch (Exception e) {
            logger.severe("生成变异铁傀儡时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启动变异铁傀儡的特殊任务
     *
     * @param ironGolem 变异铁傀儡实体
     */
    private void startMutantIronGolemTasks(IronGolem ironGolem) {
        // 清理之前可能存在的任务
        if (mutantIronGolemTasks.containsKey(ironGolem)) {
            List<BukkitTask> tasks = mutantIronGolemTasks.get(ironGolem);
            for (BukkitTask task : tasks) {
                if (task != null) {
                    task.cancel();
                }
            }
            tasks.clear();
            mutantIronGolemTasks.remove(ironGolem);
        }

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家（不攻击其他敌对生物）
        BukkitTask trackingTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 30);
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player nearestPlayer = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }

                if (nearestPlayer != null) {
                    // 计算方向向量
                    Vector direction = nearestPlayer.getLocation().toVector().subtract(ironGolem.getLocation().toVector()).normalize();

                    // 让铁傀儡面向玩家
                    Location lookLocation = ironGolem.getLocation().clone();
                    lookLocation.setDirection(direction);
                    ironGolem.teleport(lookLocation);

                    // 设置铁傀儡的路径目标 - 更积极地追踪玩家
                    ironGolem.getPathfinder().moveTo(nearestPlayer, 1.5);

                    // 如果距离较近，直接攻击
                    if (minDistance <= 4) { // 增加攻击范围
                        // 近距离攻击玩家
                        nearestPlayer.damage(15.0, ironGolem);

                        // 击飞效果
                        Vector knockback = direction.clone().multiply(2.0).setY(0.8);
                        nearestPlayer.setVelocity(knockback);

                        // 播放攻击音效
                        ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_IRON_GOLEM_ATTACK, 1.0f, 0.8f);
                    }
                }
            }
        }.runTaskTimer(plugin, 5L, 5L); // 每0.25秒执行一次，更频繁地更新目标
        tasks.add(trackingTask);

        // 任务2：发射远程声波弹攻击
        BukkitTask soundWaveTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 25);
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放声波攻击音效
                    ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.5f);

                    // 发射声波弹
                    shootSonicBullet(ironGolem, target);
                }
            }
        }.runTaskTimer(plugin, 60L, 80L); // 3秒后开始，每4秒执行一次
        tasks.add(soundWaveTask);

        // 任务3：发射草方块攻击
        BukkitTask grassBlockTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 25);
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放草方块攻击音效
                    ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 0.5f);

                    // 发射草方块攻击
                    shootGrassBlocks(ironGolem, target);
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // 5秒后开始，每5秒执行一次
        tasks.add(grassBlockTask);

        // 保存任务列表
        mutantIronGolemTasks.put(ironGolem, tasks);
    }

    /**
     * 发射远程声波弹攻击
     *
     * @param ironGolem 变异铁傀儡实体
     * @param target 目标玩家
     */
    private void shootSonicBullet(IronGolem ironGolem, Player target) {
        final Location start = ironGolem.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建声波弹攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 40; // 最大射程
            private final double bulletSize = 0.8; // 声波弹大小

            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead() || distance >= maxDistance) {
                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid()) {
                    // 如果碰到墙壁，停止声波弹
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_BOOM, 0.5f, 1.2f);
                    world.spawnParticle(Particle.EXPLOSION, currentLoc, 3, 0.2, 0.2, 0.2, 0.1);
                    this.cancel();
                    return;
                }

                // 创建声波弹粒子效果
                world.spawnParticle(Particle.SONIC_BOOM, currentLoc, 1, 0, 0, 0, 0);
                world.spawnParticle(Particle.CLOUD, currentLoc, 3, 0.1, 0.1, 0.1, 0.05);

                // 每隔5个tick播放声音
                if (distance % 5 == 0) {
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_CHARGE, 0.5f, 1.2f);
                }

                // 检测是否击中玩家
                for (Entity entity : world.getNearbyEntities(currentLoc, bulletSize, bulletSize, bulletSize)) {
                    if (entity instanceof Player && entity != ironGolem) {
                        Player hitPlayer = (Player) entity;

                        // 造成伤害
                        hitPlayer.damage(20.0, ironGolem);

                        // 击退效果
                        Vector knockback = direction.clone().multiply(2.0);
                        hitPlayer.setVelocity(knockback);

                        // 播放受伤音效
                        hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        // 显示受伤粒子效果
                        hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

                        // 爆炸效果
                        world.spawnParticle(Particle.EXPLOSION, currentLoc, 1, 0, 0, 0, 0);

                        // 停止声波弹
                        this.cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（高速移动）
    }

    /**
     * 发射草方块攻击
     *
     * @param ironGolem 变异铁傀儡实体
     * @param target 目标玩家
     */
    private void shootGrassBlocks(IronGolem ironGolem, Player target) {
        final Location start = ironGolem.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建草方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 20; // 最大射程减小
            private final List<Location> blockLocations = new ArrayList<>();

            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead() || distance >= maxDistance) {
                    // 延伸完成

                    // 移除所有草方块 - 延伸完成1秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.GRASS_BLOCK) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除草方块

                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.GRASS_BLOCK) {
                    // 如果碰到墙壁，停止延伸
                    this.cancel();

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有草方块 - 延伸完成1秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.GRASS_BLOCK) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除草方块

                    return;
                }

                // 在当前位置创建草方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.GRASS_BLOCK);
                    blockLocations.add(currentLoc.clone());

                    // 播放草方块生成音效
                    world.playSound(currentLoc, Sound.BLOCK_GRASS_PLACE, 0.5f, 1.0f);

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != ironGolem) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害 (提高120%)
                            hitPlayer.damage(44.0, ironGolem);

                            // 禁止移动1秒（通过给予缓慢效果实现）
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 100)); // 100级缓慢，相当于无法移动

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次 (最高速度)
    }

    /**
     * 获取指定位置附近的玩家列表
     *
     * @param location 中心位置
     * @param radius 搜索半径
     * @return 附近的玩家列表
     */
    private List<Player> getNearbyPlayers(Location location, double radius) {
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, radius, radius, radius)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                nearbyPlayers.add(player);
            }
        }
        return nearbyPlayers;
    }

    /**
     * 清理所有变异铁傀儡任务
     */
    public void dispose() {
        for (List<BukkitTask> tasks : mutantIronGolemTasks.values()) {
            for (BukkitTask task : tasks) {
                if (task != null) {
                    task.cancel();
                }
            }
        }
        mutantIronGolemTasks.clear();
        logger.info("已清理所有变异铁傀儡任务");
    }
}
