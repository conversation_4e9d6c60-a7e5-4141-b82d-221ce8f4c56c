package org.Ver_zhzh.customZombie;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.IronGolem;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

/**
 * 灵魂坚守者实体类 实现特殊能力： 1. 快速发射坚守者的声波技能，并且声波范围更大 2.
 * 有和变异铁傀儡一样的发射方块技能，向玩家发射黑曜石方块攻击，并且频率更高，范围更大，收回时间很快 3.
 * 发动此技能使玩家被向上击飞5格并且在击飞的路径上产生黑曜石攻击（直接向上延伸直到收回，不是固定的5格距离）
 */
public class SoulGuardian {

    // 存储灵魂坚守者任务
    private final Map<IronGolem, List<BukkitTask>> soulGuardianTasks = new HashMap<>();

    // 插件实例和日志记录器
    private final Plugin plugin;
    private final Logger logger;

    // 粒子效果助手
    private final ParticleHelper particleHelper;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param particleHelper 粒子效果助手
     */
    public SoulGuardian(Plugin plugin, ParticleHelper particleHelper) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.particleHelper = particleHelper;
    }

    /**
     * 生成灵魂坚守者
     *
     * @param location 生成位置
     * @return 生成的灵魂坚守者实体
     */
    public IronGolem spawnSoulGuardian(Location location) {
        try {
            logger.info("开始生成灵魂坚守者...");
            logger.info("生成位置: " + location.getWorld().getName()
                    + " X:" + location.getX()
                    + " Y:" + location.getY()
                    + " Z:" + location.getZ());

            // 检查世界是否有效
            if (location.getWorld() == null) {
                logger.severe("无法生成灵魂坚守者：世界对象为null");
                return null;
            }

            // 尝试生成Warden实体
            Entity entity;
            try {
                // 尝试使用Warden实体类型（Minecraft 1.19+）
                logger.info("尝试在世界 " + location.getWorld().getName() + " 生成Warden实体");
                entity = location.getWorld().spawnEntity(location, EntityType.valueOf("WARDEN"));
                logger.info("Warden实体生成成功，UUID: " + entity.getUniqueId());
            } catch (IllegalArgumentException e) {
                // 如果服务器不支持Warden实体类型，则使用铁傀儡作为后备
                logger.warning("服务器不支持Warden实体类型（需要Minecraft 1.19+），将使用铁傀儡作为替代");
                entity = location.getWorld().spawnEntity(location, EntityType.IRON_GOLEM);
                logger.info("铁傀儡实体（作为Warden替代）生成成功，UUID: " + entity.getUniqueId());
            } catch (Exception e) {
                // 如果生成实体时出现其他异常
                logger.severe("生成实体时出现异常: " + e.getMessage());
                e.printStackTrace();
                return null;
            }

            // 实体生成后可能为null（虽然这种情况很少见）
            if (entity == null) {
                logger.severe("实体生成失败，返回了null");
                return null;
            }

            // 转换为LivingEntity以设置属性
            if (!(entity instanceof LivingEntity)) {
                logger.severe("生成的实体不是LivingEntity类型");
                entity.remove();
                return null;
            }

            LivingEntity livingEntity = (LivingEntity) entity;

            // 设置灵魂坚守者属性
            logger.info("设置灵魂坚守者属性...");
            livingEntity.setCustomName("§5灵魂坚守者");
            livingEntity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示

            try {
                livingEntity.setMaxHealth(1000.0);
                livingEntity.setHealth(1000.0);
                logger.info("设置生命值成功: 1000.0");
            } catch (Exception e) {
                logger.severe("设置生命值时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 添加速度2效果
            logger.info("添加速度效果...");
            try {
                livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
                logger.info("添加速度效果成功");
            } catch (Exception e) {
                logger.severe("添加速度效果时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 添加额外生命30级和伤害吸收30级
            logger.info("添加额外生命和伤害吸收效果...");
            try {
                livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 29)); // 额外生命30级
                livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, 29)); // 伤害吸收30级
                logger.info("添加额外生命和伤害吸收效果成功");
            } catch (Exception e) {
                logger.severe("添加额外生命和伤害吸收效果时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 给灵魂坚守者添加元数据标记
            logger.info("添加元数据标记...");
            try {
                livingEntity.setMetadata("soulGuardian", new FixedMetadataValue(plugin, true));

                // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                livingEntity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                livingEntity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
                logger.info("已为灵魂坚守者添加idcZombieEntity标记");

                // 启用敌对AI，确保主动攻击玩家
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getHostileAIManager() != null) {
                        dzPlugin.getHostileAIManager().enableHostileAI(livingEntity);
                        logger.info("已为灵魂坚守者启用敌对AI");
                    }
                }

                logger.info("添加元数据标记成功");
            } catch (Exception e) {
                logger.severe("添加元数据标记时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 如果是铁傀儡，启动灵魂坚守者的特殊任务
            if (entity instanceof IronGolem) {
                IronGolem ironGolem = (IronGolem) entity;
                logger.info("启动灵魂坚守者的特殊任务...");
                try {
                    startSoulGuardianTasks(ironGolem);
                    logger.info("特殊任务启动成功");
                } catch (Exception e) {
                    logger.severe("启动特殊任务时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                logger.info("灵魂坚守者(铁傀儡)生成完成");
                return ironGolem;
            } else {
                // 如果是Warden或其他实体，启动通用特殊任务
                logger.info("启动灵魂坚守者的特殊任务(通用版)...");
                try {
                    startSoulGuardianTasksForEntity(livingEntity);
                    logger.info("特殊任务启动成功");
                } catch (Exception e) {
                    logger.severe("启动特殊任务时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                logger.info("灵魂坚守者(Warden)生成完成");

                // 由于方法签名要求返回IronGolem，但我们生成了Warden
                // 我们需要在CustomZombie.java中处理这种情况
                return null;
            }
        } catch (Exception e) {
            logger.severe("生成灵魂坚守者时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启动灵魂坚守者的特殊任务
     *
     * @param ironGolem 灵魂坚守者实体
     */
    private void startSoulGuardianTasks(IronGolem ironGolem) {
        // 清理之前可能存在的任务
        if (soulGuardianTasks.containsKey(ironGolem)) {
            List<BukkitTask> tasks = soulGuardianTasks.get(ironGolem);
            for (BukkitTask task : tasks) {
                if (task != null) {
                    task.cancel();
                }
            }
            tasks.clear();
            soulGuardianTasks.remove(ironGolem);
        }

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家
        BukkitTask trackingTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 30);
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player nearestPlayer = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }

                if (nearestPlayer != null) {
                    // 计算方向向量
                    Vector direction = nearestPlayer.getLocation().toVector().subtract(ironGolem.getLocation().toVector()).normalize();

                    // 让铁傀儡面向玩家
                    Location lookLocation = ironGolem.getLocation().clone();
                    lookLocation.setDirection(direction);
                    ironGolem.teleport(lookLocation);

                    // 设置铁傀儡的路径目标 - 更积极地追踪玩家
                    ironGolem.getPathfinder().moveTo(nearestPlayer, 1.5);

                    // 如果距离较近，直接攻击
                    if (minDistance <= 4) { // 增加攻击范围
                        // 近距离攻击玩家
                        nearestPlayer.damage(15.0, ironGolem);

                        // 击飞效果
                        Vector knockback = direction.clone().multiply(2.0).setY(0.8);
                        nearestPlayer.setVelocity(knockback);

                        // 播放攻击音效
                        ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_IRON_GOLEM_ATTACK, 1.0f, 0.8f);
                    }
                }
            }
        }.runTaskTimer(plugin, 5L, 5L); // 每0.25秒执行一次，更频繁地更新目标
        tasks.add(trackingTask);

        // 任务2：发射声波弹攻击（更大范围，更高频率）
        BukkitTask soundWaveTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 30); // 更大范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放声波攻击音效
                    ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.5f);

                    // 发射声波弹
                    shootSonicBullet(ironGolem, target);
                }
            }
        }.runTaskTimer(plugin, 40L, 60L); // 2秒后开始，每3秒执行一次（更高频率）
        tasks.add(soundWaveTask);

        // 任务3：发射黑曜石方块攻击（更大范围，更高频率）
        BukkitTask obsidianBlockTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 30); // 更大范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放黑曜石方块攻击音效
                    ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);

                    // 发射黑曜石方块攻击
                    shootObsidianBlocks(ironGolem, target);
                }
            }
        }.runTaskTimer(plugin, 40L, 40L); // 2秒后开始，每2秒执行一次（频率加倍）
        tasks.add(obsidianBlockTask);

        // 任务4：向上击飞玩家并生成黑曜石柱
        BukkitTask knockupTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(ironGolem.getLocation(), 15); // 较小范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(ironGolem.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放击飞音效
                    ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);

                    // 向上击飞玩家并生成黑曜石柱
                    knockupPlayerWithObsidian(ironGolem, target);
                }
            }
        }.runTaskTimer(plugin, 60L, 60L); // 3秒后开始，每3秒执行一次（频率加倍）
        tasks.add(knockupTask);

        // 保存任务列表
        soulGuardianTasks.put(ironGolem, tasks);
    }

    /**
     * 发射远程声波弹攻击（更大范围）
     *
     * @param ironGolem 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void shootSonicBullet(IronGolem ironGolem, Player target) {
        final Location start = ironGolem.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建声波弹攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 50; // 最大射程更大
            private final double bulletSize = 1.2; // 声波弹大小更大

            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead() || distance >= maxDistance) {
                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid()) {
                    // 如果碰到墙壁，停止声波弹
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_BOOM, 0.5f, 1.2f);
                    world.spawnParticle(Particle.EXPLOSION, currentLoc, 5, 0.3, 0.3, 0.3, 0.1);
                    this.cancel();
                    return;
                }

                // 创建声波弹粒子效果（更大范围）
                world.spawnParticle(Particle.SONIC_BOOM, currentLoc, 2, 0.1, 0.1, 0.1, 0);
                world.spawnParticle(Particle.CLOUD, currentLoc, 5, 0.2, 0.2, 0.2, 0.05);

                // 每隔3个tick播放声音（更频繁）
                if (distance % 3 == 0) {
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_CHARGE, 0.5f, 1.2f);
                }

                // 检测是否击中玩家（更大范围）
                for (Entity entity : world.getNearbyEntities(currentLoc, bulletSize, bulletSize, bulletSize)) {
                    if (entity instanceof Player && entity != ironGolem) {
                        Player hitPlayer = (Player) entity;

                        // 造成更高伤害
                        hitPlayer.damage(30.0, ironGolem);

                        // 更强力的击退效果
                        Vector knockback = direction.clone().multiply(2.5);
                        hitPlayer.setVelocity(knockback);

                        // 播放受伤音效
                        hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        // 显示受伤粒子效果
                        hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                        // 爆炸效果
                        world.spawnParticle(Particle.EXPLOSION, currentLoc, 2, 0, 0, 0, 0);

                        // 停止声波弹
                        this.cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（高速移动）
    }

    /**
     * 发射黑曜石方块攻击
     *
     * @param ironGolem 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void shootObsidianBlocks(IronGolem ironGolem, Player target) {
        final Location start = ironGolem.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建黑曜石方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 25; // 最大射程更大
            private final List<Location> blockLocations = new ArrayList<>();

            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead() || distance >= maxDistance) {
                    // 延伸完成

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后（更快的收回时间）
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到墙壁，停止延伸
                    this.cancel();

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.5f, 1.0f);

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != ironGolem) {
                            Player hitPlayer = (Player) entity;

                            // 造成更高伤害
                            hitPlayer.damage(50.0, ironGolem);

                            // 禁止移动1秒（通过给予缓慢效果实现）
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 100)); // 100级缓慢，相当于无法移动

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次 (最高速度)
    }

    /**
     * 向上击飞玩家并生成黑曜石柱
     *
     * @param ironGolem 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void knockupPlayerWithObsidian(IronGolem ironGolem, Player target) {
        // 向上击飞玩家
        Vector knockup = new Vector(0, 2.5, 0); // 强力向上击飞
        target.setVelocity(knockup);

        // 造成伤害
        target.damage(20.0, ironGolem);

        // 播放击飞音效
        target.getWorld().playSound(target.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 1.2f);

        // 显示击飞粒子效果
        target.getWorld().spawnParticle(Particle.EXPLOSION, target.getLocation(), 5, 0.5, 0.5, 0.5, 0.1);

        // 获取玩家位置
        final Location playerLoc = target.getLocation().clone();
        final World world = playerLoc.getWorld();

        // 创建向上延伸的黑曜石柱任务
        new BukkitRunnable() {
            private final List<Location> blockLocations = new ArrayList<>();
            private int height = 0;
            private final int maxHeight = 20; // 最大高度

            @Override
            public void run() {
                if (height >= maxHeight) {
                    // 延伸完成，等待一段时间后移除黑曜石柱
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 5, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除黑曜石柱

                    this.cancel();
                    return;
                }

                // 计算当前高度的位置
                Location currentLoc = playerLoc.clone().add(0, height, 0);

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到天花板，停止延伸
                    this.cancel();

                    // 等待一段时间后移除黑曜石柱
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 5, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除黑曜石柱

                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    if (height % 3 == 0) { // 每3格播放一次音效，避免声音过多
                        world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.3f, 1.0f);
                    }

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != ironGolem && entity != target) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(30.0, ironGolem);

                            // 向上击飞效果
                            Vector knockback = new Vector(0, 1.5, 0);
                            hitPlayer.setVelocity(knockback);

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }

                // 增加高度
                height++;
            }
        }.runTaskTimer(plugin, 0L, 2L); // 立即开始，每2tick执行一次
    }

    /**
     * 获取指定位置附近的玩家列表
     *
     * @param location 中心位置
     * @param radius 搜索半径
     * @return 附近的玩家列表
     */
    private List<Player> getNearbyPlayers(Location location, double radius) {
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, radius, radius, radius)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                nearbyPlayers.add(player);
            }
        }
        return nearbyPlayers;
    }

    /**
     * 为通用实体启动灵魂坚守者的特殊任务
     *
     * @param entity 灵魂坚守者实体（Warden或其他）
     */
    private void startSoulGuardianTasksForEntity(LivingEntity entity) {
        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家
        BukkitTask trackingTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(entity.getLocation(), 30);
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player nearestPlayer = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(entity.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }

                if (nearestPlayer != null) {
                    // 计算方向向量
                    Vector direction = nearestPlayer.getLocation().toVector().subtract(entity.getLocation().toVector()).normalize();

                    // 让实体面向玩家
                    Location lookLocation = entity.getLocation().clone();
                    lookLocation.setDirection(direction);
                    entity.teleport(lookLocation);

                    // 尝试移动实体朝向玩家
                    // 注意：不是所有实体都支持路径寻找，所以我们使用更通用的方法
                    try {
                        // 如果是Mob类型，可以使用路径寻找
                        if (entity instanceof org.bukkit.entity.Mob) {
                            org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) entity;
                            mob.getPathfinder().moveTo(nearestPlayer, 1.5);
                        } else {
                            // 否则，我们直接设置速度向量
                            entity.setVelocity(direction.clone().multiply(0.5));
                        }
                    } catch (Exception e) {
                        // 如果出错，使用简单的速度向量
                        entity.setVelocity(direction.clone().multiply(0.5));
                    }

                    // 如果距离较近，直接攻击
                    if (minDistance <= 4) { // 增加攻击范围
                        // 近距离攻击玩家
                        nearestPlayer.damage(15.0, entity);

                        // 击飞效果
                        Vector knockback = direction.clone().multiply(2.0).setY(0.8);
                        nearestPlayer.setVelocity(knockback);

                        // 播放攻击音效
                        entity.getWorld().playSound(entity.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);
                    }
                }
            }
        }.runTaskTimer(plugin, 5L, 5L); // 每0.25秒执行一次，更频繁地更新目标
        tasks.add(trackingTask);

        // 任务2：发射声波弹攻击（更大范围，更高频率）
        BukkitTask soundWaveTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(entity.getLocation(), 30); // 更大范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(entity.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放声波攻击音效
                    entity.getWorld().playSound(entity.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.5f);

                    // 发射声波弹
                    shootSonicBulletFromEntity(entity, target);
                }
            }
        }.runTaskTimer(plugin, 40L, 60L); // 2秒后开始，每3秒执行一次（更高频率）
        tasks.add(soundWaveTask);

        // 任务3：发射黑曜石方块攻击（更大范围，更高频率）
        BukkitTask obsidianBlockTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(entity.getLocation(), 30); // 更大范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(entity.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放黑曜石方块攻击音效
                    entity.getWorld().playSound(entity.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);

                    // 发射黑曜石方块攻击
                    shootObsidianBlocksFromEntity(entity, target);
                }
            }
        }.runTaskTimer(plugin, 40L, 40L); // 2秒后开始，每2秒执行一次（频率加倍）
        tasks.add(obsidianBlockTask);

        // 任务4：向上击飞玩家并生成黑曜石柱
        BukkitTask knockupTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead()) {
                    this.cancel();
                    return;
                }

                // 获取附近的玩家
                List<Player> nearbyPlayers = getNearbyPlayers(entity.getLocation(), 15); // 较小范围
                if (nearbyPlayers.isEmpty()) {
                    return;
                }

                // 找到最近的玩家
                Player target = null;
                double minDistance = Double.MAX_VALUE;

                for (Player player : nearbyPlayers) {
                    double distance = player.getLocation().distance(entity.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }

                if (target != null) {
                    // 播放击飞音效
                    entity.getWorld().playSound(entity.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);

                    // 向上击飞玩家并生成黑曜石柱
                    knockupPlayerWithObsidianFromEntity(entity, target);
                }
            }
        }.runTaskTimer(plugin, 60L, 60L); // 3秒后开始，每3秒执行一次（频率加倍）
        tasks.add(knockupTask);

        // 保存任务列表 - 由于我们的Map是针对IronGolem的，这里我们不保存任务列表
        // 但我们需要确保任务在实体死亡时被取消
        // 添加一个监听器来检测实体死亡
        BukkitTask monitorTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead()) {
                    // 实体已死亡，取消所有任务
                    for (BukkitTask task : tasks) {
                        if (task != null && !task.isCancelled()) {
                            task.cancel();
                        }
                    }
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次
        tasks.add(monitorTask);
    }

    /**
     * 从通用实体发射声波弹攻击
     *
     * @param entity 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void shootSonicBulletFromEntity(LivingEntity entity, Player target) {
        final Location start = entity.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建声波弹攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 50; // 最大射程更大
            private final double bulletSize = 1.2; // 声波弹大小更大

            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead() || distance >= maxDistance) {
                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid()) {
                    // 如果碰到墙壁，停止声波弹
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_BOOM, 0.5f, 1.2f);
                    world.spawnParticle(Particle.EXPLOSION, currentLoc, 5, 0.3, 0.3, 0.3, 0.1);
                    this.cancel();
                    return;
                }

                // 创建声波弹粒子效果（更大范围）
                world.spawnParticle(Particle.SONIC_BOOM, currentLoc, 2, 0.1, 0.1, 0.1, 0);
                world.spawnParticle(Particle.CLOUD, currentLoc, 5, 0.2, 0.2, 0.2, 0.05);

                // 每隔3个tick播放声音（更频繁）
                if (distance % 3 == 0) {
                    world.playSound(currentLoc, Sound.ENTITY_WARDEN_SONIC_CHARGE, 0.5f, 1.2f);
                }

                // 检测是否击中玩家（更大范围）
                for (Entity e : world.getNearbyEntities(currentLoc, bulletSize, bulletSize, bulletSize)) {
                    if (e instanceof Player && e != entity) {
                        Player hitPlayer = (Player) e;

                        // 造成更高伤害
                        hitPlayer.damage(30.0, entity);

                        // 更强力的击退效果
                        Vector knockback = direction.clone().multiply(2.5);
                        hitPlayer.setVelocity(knockback);

                        // 播放受伤音效
                        hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        // 显示受伤粒子效果
                        hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                        // 爆炸效果
                        world.spawnParticle(Particle.EXPLOSION, currentLoc, 2, 0, 0, 0, 0);

                        // 停止声波弹
                        this.cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（高速移动）
    }

    /**
     * 从通用实体发射黑曜石方块攻击
     *
     * @param entity 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void shootObsidianBlocksFromEntity(LivingEntity entity, Player target) {
        final Location start = entity.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建黑曜石方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 25; // 最大射程更大
            private final List<Location> blockLocations = new ArrayList<>();

            @Override
            public void run() {
                if (entity == null || !entity.isValid() || entity.isDead() || distance >= maxDistance) {
                    // 延伸完成

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后（更快的收回时间）
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到墙壁，停止延伸
                    this.cancel();

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.5f, 1.0f);

                    // 检测是否击中玩家
                    for (Entity e : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (e instanceof Player && e != entity) {
                            Player hitPlayer = (Player) e;

                            // 造成更高伤害
                            hitPlayer.damage(50.0, entity);

                            // 禁止移动1秒（通过给予缓慢效果实现）
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 100)); // 100级缓慢，相当于无法移动

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次 (最高速度)
    }

    /**
     * 从通用实体向上击飞玩家并生成黑曜石柱
     *
     * @param entity 灵魂坚守者实体
     * @param target 目标玩家
     */
    private void knockupPlayerWithObsidianFromEntity(LivingEntity entity, Player target) {
        // 向上击飞玩家
        Vector knockup = new Vector(0, 2.5, 0); // 强力向上击飞
        target.setVelocity(knockup);

        // 造成伤害
        target.damage(20.0, entity);

        // 播放击飞音效
        target.getWorld().playSound(target.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 1.2f);

        // 显示击飞粒子效果
        target.getWorld().spawnParticle(Particle.EXPLOSION, target.getLocation(), 5, 0.5, 0.5, 0.5, 0.1);

        // 获取玩家位置
        final Location playerLoc = target.getLocation().clone();
        final World world = playerLoc.getWorld();

        // 创建向上延伸的黑曜石柱任务
        new BukkitRunnable() {
            private final List<Location> blockLocations = new ArrayList<>();
            private int height = 0;
            private final int maxHeight = 20; // 最大高度

            @Override
            public void run() {
                if (height >= maxHeight) {
                    // 延伸完成，等待一段时间后移除黑曜石柱
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 5, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除黑曜石柱

                    this.cancel();
                    return;
                }

                // 计算当前高度的位置
                Location currentLoc = playerLoc.clone().add(0, height, 0);

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到天花板，停止延伸
                    this.cancel();

                    // 等待一段时间后移除黑曜石柱
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 5, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除黑曜石柱

                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    if (height % 3 == 0) { // 每3格播放一次音效，避免声音过多
                        world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.3f, 1.0f);
                    }

                    // 检测是否击中玩家
                    for (Entity e : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (e instanceof Player && e != entity && e != target) {
                            Player hitPlayer = (Player) e;

                            // 造成伤害
                            hitPlayer.damage(30.0, entity);

                            // 向上击飞效果
                            Vector knockback = new Vector(0, 1.5, 0);
                            hitPlayer.setVelocity(knockback);

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }

                // 增加高度
                height++;
            }
        }.runTaskTimer(plugin, 0L, 2L); // 立即开始，每2tick执行一次
    }

    /**
     * 清理所有灵魂坚守者任务
     */
    public void dispose() {
        for (List<BukkitTask> tasks : soulGuardianTasks.values()) {
            for (BukkitTask task : tasks) {
                if (task != null) {
                    task.cancel();
                }
            }
        }
        soulGuardianTasks.clear();
        logger.info("已清理所有灵魂坚守者任务");
    }
}
