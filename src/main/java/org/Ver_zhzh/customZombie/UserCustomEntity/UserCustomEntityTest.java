package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * UserCustomEntity系统测试类
 * 用于测试idc1的生成和配置功能
 * 
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class UserCustomEntityTest {
    
    private final Plugin plugin;
    private final Logger logger;
    private final UserCustomEntity userCustomEntity;
    private final DualEntitySystemManager dualSystemManager;
    
    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public UserCustomEntityTest(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        
        // 初始化UserCustomEntity系统
        this.userCustomEntity = new UserCustomEntity(plugin, null);
        this.dualSystemManager = new DualEntitySystemManager(plugin, userCustomEntity, null);
        
        logger.info("UserCustomEntity测试系统初始化完成");
    }
    
    /**
     * 测试idc1的生成
     *
     * @param location 测试位置
     * @return 测试结果
     */
    public boolean testIdc1Spawn(Location location) {
        logger.info("开始测试idc1生成...");
        
        try {
            // 测试直接生成
            LivingEntity entity = userCustomEntity.spawnUserCustomEntityDirect(location, "idc1");
            
            if (entity == null) {
                logger.severe("idc1生成失败：返回null");
                return false;
            }
            
            // 验证实体属性
            boolean success = validateIdc1Entity(entity);
            
            if (success) {
                logger.info("idc1测试成功！");
            } else {
                logger.warning("idc1测试失败：属性验证不通过");
            }
            
            return success;
            
        } catch (Exception e) {
            logger.severe("idc1测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 验证idc1实体的属性
     */
    private boolean validateIdc1Entity(LivingEntity entity) {
        boolean allValid = true;
        
        // 验证实体类型
        if (!entity.getType().name().equals("ZOMBIFIED_PIGLIN")) {
            logger.warning("实体类型错误，期望: ZOMBIFIED_PIGLIN，实际: " + entity.getType().name());
            allValid = false;
        } else {
            logger.info("✓ 实体类型正确: " + entity.getType().name());
        }
        
        // 验证生命值
        double expectedHealth = 120.0; // 根据配置文件
        if (Math.abs(entity.getMaxHealth() - expectedHealth) > 0.1) {
            logger.warning("生命值错误，期望: " + expectedHealth + "，实际: " + entity.getMaxHealth());
            allValid = false;
        } else {
            logger.info("✓ 生命值正确: " + entity.getMaxHealth());
        }
        
        // 验证名称
        String expectedName = "§c§l§k|§r§c§l强化变异僵尸§k|";
        if (!expectedName.equals(entity.getCustomName())) {
            logger.warning("名称错误，期望: " + expectedName + "，实际: " + entity.getCustomName());
            allValid = false;
        } else {
            logger.info("✓ 名称正确: " + entity.getCustomName());
        }
        
        // 验证元数据
        if (!entity.hasMetadata("userCustomEntity")) {
            logger.warning("缺少userCustomEntity元数据");
            allValid = false;
        } else {
            logger.info("✓ userCustomEntity元数据存在");
        }
        
        if (!entity.hasMetadata("idcZombieEntity")) {
            logger.warning("缺少idcZombieEntity元数据");
            allValid = false;
        } else {
            logger.info("✓ idcZombieEntity元数据存在");
        }
        
        if (!entity.hasMetadata("poisonAttacker")) {
            logger.warning("缺少poisonAttacker元数据");
            allValid = false;
        } else {
            logger.info("✓ poisonAttacker元数据存在");
        }
        
        // 验证装备
        if (entity.getEquipment() != null) {
            if (entity.getEquipment().getItemInMainHand().getType().name().contains("SWORD")) {
                logger.info("✓ 武器装备正确");
            } else {
                logger.warning("武器装备错误");
                allValid = false;
            }
            
            if (entity.getEquipment().getHelmet() != null && 
                entity.getEquipment().getHelmet().getType().name().contains("LEATHER")) {
                logger.info("✓ 头盔装备正确");
            } else {
                logger.warning("头盔装备错误");
                allValid = false;
            }
        } else {
            logger.warning("实体装备为null");
            allValid = false;
        }
        
        return allValid;
    }
    
    /**
     * 测试双系统管理器
     */
    public boolean testDualSystemManager(Location location) {
        logger.info("开始测试双系统管理器...");
        
        try {
            // 测试系统状态
            String status = dualSystemManager.getSystemStatus();
            logger.info("系统状态:\n" + status);
            
            // 测试生成
            LivingEntity entity = dualSystemManager.spawnCustomEntityDirect(location, "idc1");
            
            if (entity != null) {
                logger.info("✓ 双系统管理器生成成功");
                return true;
            } else {
                logger.warning("双系统管理器生成失败");
                return false;
            }
            
        } catch (Exception e) {
            logger.severe("双系统管理器测试失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 测试配置重载
     */
    public boolean testConfigReload() {
        logger.info("开始测试配置重载...");
        
        try {
            userCustomEntity.reloadConfig();
            logger.info("✓ 配置重载成功");
            return true;
        } catch (Exception e) {
            logger.severe("配置重载失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests(Location testLocation) {
        logger.info("=== 开始UserCustomEntity系统全面测试 ===");
        
        int totalTests = 0;
        int passedTests = 0;
        
        // 测试1：配置重载
        totalTests++;
        if (testConfigReload()) {
            passedTests++;
        }
        
        // 测试2：idc1生成
        totalTests++;
        if (testIdc1Spawn(testLocation)) {
            passedTests++;
        }
        
        // 测试3：双系统管理器
        totalTests++;
        if (testDualSystemManager(testLocation.clone().add(2, 0, 0))) {
            passedTests++;
        }
        
        // 输出测试结果
        logger.info("=== 测试完成 ===");
        logger.info("总测试数: " + totalTests);
        logger.info("通过测试: " + passedTests);
        logger.info("失败测试: " + (totalTests - passedTests));
        logger.info("成功率: " + String.format("%.1f%%", (passedTests * 100.0 / totalTests)));
        
        if (passedTests == totalTests) {
            logger.info("🎉 所有测试通过！idc1适配成功！");
        } else {
            logger.warning("⚠️ 部分测试失败，需要检查配置和实现");
        }
    }
}
