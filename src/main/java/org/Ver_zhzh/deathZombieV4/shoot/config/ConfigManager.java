package org.Ver_zhzh.deathZombieV4.shoot.config;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 配置管理器，用于管理Shoot模块的配置文件
 */
public class ConfigManager {
    private final DeathZombieV4 plugin;
    private FileConfiguration config;
    private FileConfiguration buyConfig;
    private FileConfiguration playerDataConfig;
    private File configFile;
    private File buyConfigFile;
    private File playerDataFile;

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public ConfigManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.configFile = new File(plugin.getDataFolder(), "shoot/config.yml");
        this.buyConfigFile = new File(plugin.getDataFolder(), "shoot/Buy.yml");
        this.playerDataFile = new File(plugin.getDataFolder(), "shoot/playerData.yml");
        
        // 确保目录存在
        if (!configFile.getParentFile().exists()) {
            configFile.getParentFile().mkdirs();
        }
        
        // 加载配置文件
        reloadConfig();
        reloadBuyConfig();
        reloadPlayerDataConfig();
    }

    /**
     * 重新加载主配置文件
     */
    public void reloadConfig() {
        // 如果配置文件不存在，则保存默认配置
        if (!configFile.exists()) {
            saveDefaultConfig();
        }
        
        // 加载配置文件
        config = YamlConfiguration.loadConfiguration(configFile);
        
        // 加载默认配置
        InputStream defaultConfigStream = plugin.getResource("shoot/config.yml");
        if (defaultConfigStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(
                    new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8));
            config.setDefaults(defaultConfig);
        }
    }

    /**
     * 重新加载Buy配置文件
     */
    public void reloadBuyConfig() {
        // 如果配置文件不存在，则保存默认配置
        if (!buyConfigFile.exists()) {
            saveDefaultBuyConfig();
        }
        
        // 加载配置文件
        buyConfig = YamlConfiguration.loadConfiguration(buyConfigFile);
        
        // 加载默认配置
        InputStream defaultConfigStream = plugin.getResource("shoot/Buy.yml");
        if (defaultConfigStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(
                    new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8));
            buyConfig.setDefaults(defaultConfig);
        }
    }

    /**
     * 重新加载玩家数据配置文件
     */
    public void reloadPlayerDataConfig() {
        // 如果配置文件不存在，则创建空配置
        if (!playerDataFile.exists()) {
            try {
                playerDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建玩家数据文件: " + e.getMessage());
            }
        }
        
        // 加载配置文件
        playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);
    }

    /**
     * 获取主配置文件
     *
     * @return 主配置文件
     */
    public FileConfiguration getConfig() {
        if (config == null) {
            reloadConfig();
        }
        return config;
    }

    /**
     * 获取Buy配置文件
     *
     * @return Buy配置文件
     */
    public FileConfiguration getBuyConfig() {
        if (buyConfig == null) {
            reloadBuyConfig();
        }
        return buyConfig;
    }

    /**
     * 获取玩家数据配置文件
     *
     * @return 玩家数据配置文件
     */
    public FileConfiguration getPlayerDataConfig() {
        if (playerDataConfig == null) {
            reloadPlayerDataConfig();
        }
        return playerDataConfig;
    }

    /**
     * 保存主配置文件
     */
    public void saveConfig() {
        if (config == null || configFile == null) {
            return;
        }
        
        try {
            getConfig().save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存配置文件: " + e.getMessage());
        }
    }

    /**
     * 保存Buy配置文件
     */
    public void saveBuyConfig() {
        if (buyConfig == null || buyConfigFile == null) {
            return;
        }
        
        try {
            getBuyConfig().save(buyConfigFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存Buy配置文件: " + e.getMessage());
        }
    }

    /**
     * 保存玩家数据配置文件
     */
    public void savePlayerDataConfig() {
        if (playerDataConfig == null || playerDataFile == null) {
            return;
        }
        
        try {
            getPlayerDataConfig().save(playerDataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存玩家数据文件: " + e.getMessage());
        }
    }

    /**
     * 保存默认主配置文件
     */
    public void saveDefaultConfig() {
        if (!configFile.exists()) {
            plugin.saveResource("shoot/config.yml", false);
        }
    }

    /**
     * 保存默认Buy配置文件
     */
    public void saveDefaultBuyConfig() {
        if (!buyConfigFile.exists()) {
            plugin.saveResource("shoot/Buy.yml", false);
        }
    }

    /**
     * 保存所有配置文件
     */
    public void saveAllConfigs() {
        saveConfig();
        saveBuyConfig();
        savePlayerDataConfig();
    }
}