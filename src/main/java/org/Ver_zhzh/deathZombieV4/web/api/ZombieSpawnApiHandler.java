package org.Ver_zhzh.deathZombieV4.web.api;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.scheduler.BukkitRunnable;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import java.io.File;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

/**
 * 处理僵尸生成设置相关的API请求
 */
public class ZombieSpawnApiHandler implements HttpHandler {

    private final DeathZombieV4 plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ZombieSpawnApiHandler(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        // 获取请求方法和路径
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();
        String query = exchange.getRequestURI().getQuery();

        // 添加CORS头
        handleCorsHeaders(exchange);

        // 记录请求信息
        plugin.getLogger().info("处理僵尸生成设置API请求: " + path + ", 方法: " + method + ", 查询: " + query);

        // 根据请求方法处理不同的操作
        if (path.equals("/api/zombie-spawn") && method.equalsIgnoreCase("POST")) {
            // 保存僵尸生成设置
            handleSaveZombieSpawn(exchange);
        } else {
            // 未知路径，返回404
            plugin.getLogger().warning("未知的僵尸生成设置API请求路径: " + path);
            sendResponse(exchange, 404, createErrorResponse("未找到请求的资源"));
        }
    }

    /**
     * 处理保存僵尸生成设置的请求
     */
    private void handleSaveZombieSpawn(HttpExchange exchange) throws IOException {
        // 读取请求体
        String requestBody = new BufferedReader(new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));

        try {
            // 解析JSON
            JSONParser parser = new JSONParser();
            JSONObject requestJson = (JSONObject) parser.parse(requestBody);

            // 获取游戏名称
            String gameName = (String) requestJson.get("name");
            if (gameName == null || gameName.isEmpty()) {
                sendResponse(exchange, 400, createErrorResponse("游戏名称不能为空"));
                return;
            }

            // 获取回合数
            int rounds = ((Number) requestJson.get("rounds")).intValue();
            if (rounds <= 0) {
                sendResponse(exchange, 400, createErrorResponse("回合数必须大于0"));
                return;
            }

            // 获取回合模式
            Map<String, Object> roundModes = (Map<String, Object>) requestJson.get("roundModes");
            if (roundModes == null) {
                sendResponse(exchange, 400, createErrorResponse("回合模式不能为空"));
                return;
            }

            // 同步回主线程执行，因为Bukkit API不是线程安全的
            new BukkitRunnable() {
                @Override
                public void run() {
                    try {
                        // 获取游戏管理器
                        GameManager gameManager = plugin.getGameManager();

                        // 检查游戏是否存在
                        if (!gameManager.gameExists(gameName)) {
                            plugin.getLogger().warning("游戏不存在: " + gameName);
                            sendResponseAsync(exchange, 404, createErrorResponse("游戏不存在"));
                            return;
                        }

                        // 直接获取游戏文件并加载配置
                        File gameFile = gameManager.getGameFile(gameName);
                        if (gameFile == null) {
                            plugin.getLogger().warning("无法获取游戏文件: " + gameName);
                            sendResponseAsync(exchange, 500, createErrorResponse("无法获取游戏文件"));
                            return;
                        }

                        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFile);

                        // 设置回合数
                        config.set("rounds", rounds);
                        plugin.getLogger().info("设置回合数: " + rounds);

                        // 设置回合模式
                        for (Map.Entry<String, Object> entry : roundModes.entrySet()) {
                            String roundKey = entry.getKey();
                            Map<String, Object> roundConfig = (Map<String, Object>) entry.getValue();
                            plugin.getLogger().info("处理回合: " + roundKey);

                            // 遍历每个生成点
                            for (Map.Entry<String, Object> spawnEntry : roundConfig.entrySet()) {
                                String spawnName = spawnEntry.getKey();
                                Map<String, Object> spawnConfig = (Map<String, Object>) spawnEntry.getValue();
                                plugin.getLogger().info("处理生成点: " + spawnName);

                                // 遍历每个怪物配置
                                for (Map.Entry<String, Object> monsterEntry : spawnConfig.entrySet()) {
                                    String monsterKey = monsterEntry.getKey();
                                    Map<String, Object> monsterConfig = (Map<String, Object>) monsterEntry.getValue();

                                    // 设置怪物配置
                                    String configPath = "roundModes." + roundKey + "." + spawnName + "." + monsterKey;
                                    config.set(configPath + ".monsterType", monsterConfig.get("monsterType"));
                                    config.set(configPath + ".monsterId", monsterConfig.get("monsterId"));
                                    config.set(configPath + ".count", monsterConfig.get("count"));
                                    config.set(configPath + ".type", monsterConfig.get("type"));
                                    config.set(configPath + ".number", monsterConfig.get("number"));

                                    plugin.getLogger().info("设置配置路径: " + configPath +
                                                          ", 怪物类型: " + monsterConfig.get("monsterType") +
                                                          ", 怪物ID: " + monsterConfig.get("monsterId") +
                                                          ", 数量: " + monsterConfig.get("count"));
                                }
                            }
                        }

                        // 直接保存配置文件
                        try {
                            config.save(gameFile);
                            plugin.getLogger().info("成功保存游戏配置文件: " + gameFile.getAbsolutePath());

                            // 验证保存是否成功
                            FileConfiguration verifyConfig = YamlConfiguration.loadConfiguration(gameFile);
                            int savedRounds = verifyConfig.getInt("rounds", 0);
                            plugin.getLogger().info("验证保存结果 - 回合数: " + savedRounds);

                            // 验证回合模式配置
                            if (verifyConfig.isConfigurationSection("roundModes")) {
                                plugin.getLogger().info("验证成功: roundModes配置节存在");
                                for (String roundKey : verifyConfig.getConfigurationSection("roundModes").getKeys(false)) {
                                    plugin.getLogger().info("验证成功: 找到回合配置 " + roundKey);
                                }
                            } else {
                                plugin.getLogger().warning("验证失败: roundModes配置节不存在");
                            }

                        } catch (IOException e) {
                            plugin.getLogger().severe("保存游戏配置文件失败: " + e.getMessage());
                            e.printStackTrace();
                            sendResponseAsync(exchange, 500, createErrorResponse("保存配置文件失败: " + e.getMessage()));
                            return;
                        }

                        // 发送成功响应
                        JSONObject response = new JSONObject();
                        response.put("success", true);
                        response.put("message", "僵尸生成设置保存成功");
                        sendResponseAsync(exchange, 200, response.toJSONString());

                        plugin.getLogger().info("已保存游戏 " + gameName + " 的僵尸生成设置");
                    } catch (Exception e) {
                        plugin.getLogger().severe("保存僵尸生成设置时出错: " + e.getMessage());
                        e.printStackTrace();
                        sendResponseAsync(exchange, 500, createErrorResponse("服务器错误: " + e.getMessage()));
                    }
                }
            }.runTask(plugin);

        } catch (ParseException e) {
            plugin.getLogger().warning("解析僵尸生成设置JSON失败: " + e.getMessage());
            sendResponse(exchange, 400, createErrorResponse("无效的JSON格式: " + e.getMessage()));
        } catch (Exception e) {
            plugin.getLogger().warning("处理保存僵尸生成设置请求失败: " + e.getMessage());
            sendResponse(exchange, 500, createErrorResponse("服务器错误: " + e.getMessage()));
        }
    }

    /**
     * 处理CORS头
     */
    private void handleCorsHeaders(HttpExchange exchange) {
        exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type, Authorization");
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().add("Content-Type", "application/json; charset=UTF-8");
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    /**
     * 异步发送HTTP响应
     */
    private void sendResponseAsync(HttpExchange exchange, int statusCode, String response) {
        try {
            sendResponse(exchange, statusCode, response);
        } catch (IOException e) {
            plugin.getLogger().severe("发送异步响应时出错: " + e.getMessage());
        }
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        return response.toJSONString();
    }
}
