package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.entity.*;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.*;
import java.util.logging.Logger;

/**
 * 敌对AI管理器
 * 负责管理所有变异实体的敌对AI，确保它们主动攻击玩家而不是其他生物
 */
public class HostileAIManager {
    
    private final Plugin plugin;
    private final Logger logger;
    
    // 存储每个实体的AI任务
    private final Map<Entity, BukkitTask> hostileAITasks = new HashMap<>();
    
    // AI更新间隔（tick）
    private static final long AI_UPDATE_INTERVAL = 8L; // 每0.4秒更新一次，适度提高反应速度

    // 目标搜索范围
    private static final double TARGET_SEARCH_RANGE = 30.0;

    // 攻击范围
    private static final double ATTACK_RANGE = 4.0;
    
    public HostileAIManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 为实体启用敌对AI
     * 
     * @param entity 实体
     */
    public void enableHostileAI(LivingEntity entity) {
        if (entity == null || entity.isDead()) {
            return;
        }
        
        // 如果已经有AI任务，先取消
        disableHostileAI(entity);
        
        // 添加敌对AI标记
        entity.setMetadata("hostileAI", new FixedMetadataValue(plugin, true));
        
        // 创建AI任务
        BukkitTask aiTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || entity.isDead() || !entity.isValid()) {
                    this.cancel();
                    hostileAITasks.remove(entity);
                    return;
                }
                
                updateHostileAI(entity);
            }
        }.runTaskTimer(plugin, 0L, AI_UPDATE_INTERVAL);
        
        hostileAITasks.put(entity, aiTask);

        logger.info("为实体 " + entity.getType() + " 启用敌对AI");
    }
    
    /**
     * 禁用实体的敌对AI
     *
     * @param entity 实体
     */
    public void disableHostileAI(Entity entity) {
        BukkitTask task = hostileAITasks.remove(entity);
        if (task != null) {
            task.cancel();
        }

        if (entity != null) {
            entity.removeMetadata("hostileAI", plugin);
        }
    }
    
    /**
     * 更新实体的敌对AI
     * 
     * @param entity 实体
     */
    private void updateHostileAI(LivingEntity entity) {
        try {
            // 查找最近的玩家目标
            Player target = findNearestPlayer(entity);
            
            if (target == null) {
                return;
            }
            
            double distance = entity.getLocation().distance(target.getLocation());

            // 设置实体面向目标
            Vector direction = target.getLocation().toVector().subtract(entity.getLocation().toVector()).normalize();
            entity.getLocation().setDirection(direction);
            
            // 根据实体类型设置不同的AI行为
            if (entity instanceof IronGolem) {
                handleIronGolemAI((IronGolem) entity, target, distance);
            } else if (entity instanceof PigZombie || isZombifiedPiglin(entity)) {
                handleZombifiedPiglinAI(entity, target, distance);
            } else if (entity instanceof Mob) {
                handleMobAI((Mob) entity, target, distance);
            } else {
                // 通用AI处理
                handleGenericAI(entity, target, distance);
            }
            
        } catch (Exception e) {
            logger.warning("更新实体敌对AI时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理铁傀儡AI
     */
    private void handleIronGolemAI(IronGolem ironGolem, Player target, double distance) {
        // 设置目标为玩家
        ironGolem.setTarget(target);

        // 使用路径寻找移动到玩家
        if (distance > ATTACK_RANGE) {
            ironGolem.getPathfinder().moveTo(target, 1.2);
        }

        // 如果在攻击范围内，进行攻击
        if (distance <= ATTACK_RANGE) {
            // 这里不直接攻击，让变异铁傀儡的自定义攻击逻辑处理
            // 只确保目标正确
            ironGolem.setTarget(target);
        }
    }
    
    /**
     * 处理僵尸猪人AI
     */
    private void handleZombifiedPiglinAI(LivingEntity piglin, Player target, double distance) {
        // 设置目标为玩家
        if (piglin instanceof Mob) {
            Mob mob = (Mob) piglin;
            mob.setTarget(target);

            // 移动到玩家
            if (distance > ATTACK_RANGE) {
                mob.getPathfinder().moveTo(target, 1.0);
            }
        }
    }
    
    /**
     * 处理通用Mob AI
     */
    private void handleMobAI(Mob mob, Player target, double distance) {
        // 设置目标为玩家
        mob.setTarget(target);

        // 移动到玩家
        if (distance > ATTACK_RANGE) {
            mob.getPathfinder().moveTo(target, 1.0);
        }
    }
    
    /**
     * 处理通用AI
     */
    private void handleGenericAI(LivingEntity entity, Player target, double distance) {
        // 对于不是Mob的实体，使用基本的移动逻辑
        if (distance > ATTACK_RANGE) {
            Vector direction = target.getLocation().toVector().subtract(entity.getLocation().toVector()).normalize();
            entity.setVelocity(direction.multiply(0.3));
        }

        // 对于支持目标设置的实体，尝试设置目标
        if (entity instanceof Mob) {
            Mob mob = (Mob) entity;
            mob.setTarget(target);
        }
    }
    
    /**
     * 检查实体是否是僵尸化猪灵（兼容性方法）
     *
     * @param entity 实体
     * @return 是否是僵尸化猪灵
     */
    private boolean isZombifiedPiglin(LivingEntity entity) {
        try {
            // 尝试通过类名检查，避免直接引用可能不存在的类
            return entity.getClass().getSimpleName().equals("ZombifiedPiglin") ||
                   entity.getType().name().equals("ZOMBIFIED_PIGLIN");
        } catch (Exception e) {
            // 如果检查失败，返回false
            return false;
        }
    }

    /**
     * 查找最近的玩家
     *
     * @param entity 实体
     * @return 最近的玩家，如果没有找到返回null
     */
    private Player findNearestPlayer(LivingEntity entity) {
        Player nearestPlayer = null;
        double nearestDistance = TARGET_SEARCH_RANGE;
        
        for (Entity nearby : entity.getNearbyEntities(TARGET_SEARCH_RANGE, TARGET_SEARCH_RANGE, TARGET_SEARCH_RANGE)) {
            if (nearby instanceof Player) {
                Player player = (Player) nearby;
                
                // 只攻击冒险模式的玩家
                if (player.getGameMode() != GameMode.ADVENTURE) {
                    continue;
                }
                
                double distance = entity.getLocation().distance(player.getLocation());
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
        }
        
        return nearestPlayer;
    }
    
    /**
     * 检查实体是否有敌对AI
     * 
     * @param entity 实体
     * @return 是否有敌对AI
     */
    public boolean hasHostileAI(Entity entity) {
        return hostileAITasks.containsKey(entity);
    }
    
    /**
     * 清理所有AI任务
     */
    public void cleanup() {
        for (BukkitTask task : hostileAITasks.values()) {
            if (task != null) {
                task.cancel();
            }
        }
        hostileAITasks.clear();
        logger.info("已清理所有敌对AI任务");
    }
    
    /**
     * 获取当前活跃的AI任务数量
     * 
     * @return AI任务数量
     */
    public int getActiveAICount() {
        return hostileAITasks.size();
    }
}
