package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 全息图交互处理器
 * 处理玩家与全息排行榜的交互
 */
public class HologramInteractionHandler implements Listener {

    private final DeathZombieV4 plugin;

    public HologramInteractionHandler(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 处理玩家交互事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // 只处理右键和左键点击空气的情况
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.LEFT_CLICK_AIR) {
            return;
        }
        
        // 检查玩家是否在全息排行榜附近
        HologramLeaderboardManager holoManager = plugin.getHologramLeaderboardManager();
        if (holoManager == null) {
            return;
        }
        
        String nearbyHolo = holoManager.getNearbyHologram(player);
        if (nearbyHolo != null) {
            // 取消事件以防止其他交互
            event.setCancelled(true);
            
            // 处理交互
            boolean isLeftClick = event.getAction() == Action.LEFT_CLICK_AIR;
            holoManager.handleHologramInteraction(player, nearbyHolo, isLeftClick);
        }
    }
}
