package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.Location;
import org.bukkit.World;

/**
 * 表示一个3D区域
 */
public class Region {

    private final World world;
    private final double minX, minY, minZ;
    private final double maxX, maxY, maxZ;

    /**
     * 从两个位置创建一个区域
     *
     * @param loc1 第一个位置
     * @param loc2 第二个位置
     */
    public Region(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            throw new IllegalArgumentException("两个位置必须在同一个世界");
        }

        this.world = loc1.getWorld();
        this.minX = Math.min(loc1.getX(), loc2.getX());
        this.minY = Math.min(loc1.getY(), loc2.getY());
        this.minZ = Math.min(loc1.getZ(), loc2.getZ());
        this.maxX = Math.max(loc1.getX(), loc2.getX());
        this.maxY = Math.max(loc1.getY(), loc2.getY());
        this.maxZ = Math.max(loc1.getZ(), loc2.getZ());
    }

    /**
     * 从世界和坐标创建一个区域
     *
     * @param world 世界
     * @param minX 最小 X 坐标
     * @param minY 最小 Y 坐标
     * @param minZ 最小 Z 坐标
     * @param maxX 最大 X 坐标
     * @param maxY 最大 Y 坐标
     * @param maxZ 最大 Z 坐标
     */
    public Region(World world, double minX, double minY, double minZ, double maxX, double maxY, double maxZ) {
        this.world = world;
        this.minX = minX;
        this.minY = minY;
        this.minZ = minZ;
        this.maxX = maxX;
        this.maxY = maxY;
        this.maxZ = maxZ;
    }

    /**
     * 检查一个位置是否在该区域内
     *
     * @param loc 要检查的位置
     * @return 如果位置在区域内则返回true
     */
    public boolean contains(Location loc) {
        if (!world.equals(loc.getWorld())) {
            return false;
        }

        double x = loc.getX();
        double y = loc.getY();
        double z = loc.getZ();

        return x >= minX && x <= maxX
                && y >= minY && y <= maxY
                && z >= minZ && z <= maxZ;
    }

    public World getWorld() {
        return world;
    }

    public double getMinX() {
        return minX;
    }

    public double getMinY() {
        return minY;
    }

    public double getMinZ() {
        return minZ;
    }

    public double getMaxX() {
        return maxX;
    }

    public double getMaxY() {
        return maxY;
    }

    public double getMaxZ() {
        return maxZ;
    }
}
