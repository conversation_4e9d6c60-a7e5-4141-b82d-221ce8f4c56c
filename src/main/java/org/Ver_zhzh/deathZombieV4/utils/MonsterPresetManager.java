package org.Ver_zhzh.deathZombieV4.utils;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * 怪物预设管理器 负责管理玩家创建的怪物预设，包括保存、加载和应用预设
 */
public class MonsterPresetManager {

    private final DeathZombieV4 plugin;
    private final File presetsFolder;
    private final Map<String, FileConfiguration> presetConfigs;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public MonsterPresetManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.presetsFolder = new File(plugin.getDataFolder(), "monster-presets");
        this.presetConfigs = new HashMap<>();

        // 确保预设文件夹存在
        if (!presetsFolder.exists()) {
            if (presetsFolder.mkdirs()) {
                plugin.getLogger().info("已创建怪物预设文件夹");
            } else {
                plugin.getLogger().warning("无法创建怪物预设文件夹");
            }
        }

        // 加载所有预设
        loadAllPresets();
    }

    /**
     * 加载所有预设文件
     */
    private void loadAllPresets() {
        presetConfigs.clear();
        File[] files = presetsFolder.listFiles((dir, name) -> name.endsWith(".yml"));

        if (files == null || files.length == 0) {
            plugin.getLogger().info("没有找到怪物预设文件");
            return;
        }

        for (File file : files) {
            try {
                FileConfiguration config = YamlConfiguration.loadConfiguration(file);
                // 使用配置文件中保存的原始名称作为键，而不是文件名
                String originalName = config.getString("name");
                if (originalName != null && !originalName.isEmpty()) {
                    presetConfigs.put(originalName, config);
                } else {
                    // 如果没有保存原始名称，则使用文件名（去掉.yml后缀）
                    String fileName = file.getName().replace(".yml", "");
                    presetConfigs.put(fileName, config);
                    plugin.getLogger().warning("预设文件 " + file.getName() + " 没有保存原始名称，使用文件名作为预设名称");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载预设文件失败: " + file.getName() + ", 错误: " + e.getMessage());
            }
        }

        plugin.getLogger().info("已加载 " + presetConfigs.size() + " 个怪物预设");
    }

    /**
     * 获取所有预设的JSON表示
     *
     * @return 预设列表的JSON数组
     */
    public JSONArray getAllPresetsAsJson() {
        JSONArray presetsArray = new JSONArray();

        for (Map.Entry<String, FileConfiguration> entry : presetConfigs.entrySet()) {
            String presetName = entry.getKey();
            FileConfiguration config = entry.getValue();

            JSONObject presetObj = new JSONObject();
            // 使用配置文件中保存的原始名称，如果没有则使用键名
            String displayName = config.getString("name", presetName);
            presetObj.put("name", displayName);
            presetObj.put("description", config.getString("description", ""));

            // 获取怪物列表
            JSONArray monstersArray = new JSONArray();
            ConfigurationSection monstersSection = config.getConfigurationSection("monsters");

            if (monstersSection != null) {
                for (String key : monstersSection.getKeys(false)) {
                    ConfigurationSection monsterSection = monstersSection.getConfigurationSection(key);
                    if (monsterSection != null) {
                        JSONObject monsterObj = new JSONObject();
                        monsterObj.put("monsterType", monsterSection.getString("monsterType", "zombie"));
                        monsterObj.put("monsterId", monsterSection.getString("monsterId", "id1"));
                        monsterObj.put("count", monsterSection.getString("count", "1"));
                        monstersArray.add(monsterObj);
                    }
                }
            }

            presetObj.put("monsters", monstersArray);
            presetsArray.add(presetObj);
        }

        return presetsArray;
    }

    /**
     * 保存新的预设
     *
     * @param name 预设名称
     * @param description 预设描述
     * @param monsters 怪物列表
     * @return 是否保存成功
     */
    public boolean savePreset(String name, String description, List<Map<String, String>> monsters) {
        return savePreset(name, description, monsters, null);
    }

    /**
     * 保存或更新预设
     *
     * @param name 预设名称
     * @param description 预设描述
     * @param monsters 怪物列表
     * @param originalName 原始预设名称（如果是更新操作）
     * @return 是否保存成功
     */
    public boolean savePreset(String name, String description, List<Map<String, String>> monsters, String originalName) {
        // 验证预设名称
        if (name == null || name.trim().isEmpty()) {
            plugin.getLogger().warning("预设名称不能为空");
            return false;
        }

        // 清理文件名，移除不安全的字符
        String safeFileName = sanitizeFileName(name);
        if (safeFileName.isEmpty()) {
            plugin.getLogger().warning("预设名称包含过多无效字符，无法创建文件: " + name);
            return false;
        }

        // 如果是更新操作且原始名称与新名称不同，则删除原始文件
        if (originalName != null && !originalName.equals(name)) {
            String safeOriginalFileName = sanitizeFileName(originalName);
            File originalFile = new File(presetsFolder, safeOriginalFileName + ".yml");
            if (originalFile.exists()) {
                if (!originalFile.delete()) {
                    plugin.getLogger().warning("无法删除原始预设文件: " + originalName);
                    return false;
                }
                presetConfigs.remove(originalName);
            }
        }

        // 创建新的配置文件
        File presetFile = new File(presetsFolder, safeFileName + ".yml");
        FileConfiguration config = new YamlConfiguration();

        // 设置基本信息
        config.set("name", name);
        config.set("description", description);

        // 保存怪物列表
        for (int i = 0; i < monsters.size(); i++) {
            Map<String, String> monster = monsters.get(i);
            String path = "monsters.monster" + (i + 1);
            config.set(path + ".monsterType", monster.get("monsterType"));
            config.set(path + ".monsterId", monster.get("monsterId"));
            config.set(path + ".count", monster.get("count"));
        }

        // 保存到文件
        try {
            config.save(presetFile);
            presetConfigs.put(name, config);
            if (originalName != null) {
                plugin.getLogger().info("已更新怪物预设: " + name + " (原名: " + originalName + ")");
            } else {
                plugin.getLogger().info("已保存怪物预设: " + name);
            }
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "保存怪物预设失败: " + name, e);
            return false;
        }
    }

    /**
     * 删除预设
     *
     * @param name 预设名称
     * @return 是否删除成功
     */
    public boolean deletePreset(String name) {
        String safeFileName = sanitizeFileName(name);
        File presetFile = new File(presetsFolder, safeFileName + ".yml");
        if (presetFile.exists()) {
            boolean deleted = presetFile.delete();
            if (deleted) {
                presetConfigs.remove(name);
                plugin.getLogger().info("已删除怪物预设: " + name);
                return true;
            } else {
                plugin.getLogger().warning("无法删除怪物预设文件: " + name);
                return false;
            }
        }
        return false;
    }

    /**
     * 清理文件名，移除不安全的字符
     *
     * @param fileName 原始文件名
     * @return 清理后的安全文件名
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "";
        }

        // 移除或替换不安全的字符
        // Windows不允许的字符: < > : " | ? * \ /
        // 同时移除控制字符和其他可能有问题的字符
        String sanitized = fileName
                .replaceAll("[<>:\"|?*\\\\/]", "_")  // 替换不安全字符为下划线
                .replaceAll("[\\x00-\\x1F\\x7F]", "") // 移除控制字符
                .trim(); // 移除首尾空格

        // 移除连续的下划线
        sanitized = sanitized.replaceAll("_{2,}", "_");

        // 移除首尾的下划线
        sanitized = sanitized.replaceAll("^_+|_+$", "");

        // 确保文件名不为空且不超过最大长度
        if (sanitized.isEmpty()) {
            sanitized = "preset_" + System.currentTimeMillis();
        } else if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 100);
        }

        // 确保文件名不是Windows保留名称
        String upperSanitized = sanitized.toUpperCase();
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                                  "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
                                  "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};

        for (String reserved : reservedNames) {
            if (upperSanitized.equals(reserved)) {
                sanitized = sanitized + "_preset";
                break;
            }
        }

        return sanitized;
    }
}
