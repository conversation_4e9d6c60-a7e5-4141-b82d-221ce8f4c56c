package org.Ver_zhzh.deathZombieV4.utils;

import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

/**
 * 管理动作条显示的工具类
 */
public class ActionBarManager {

    private final DeathZombieV4 plugin;
    private final GameSessionManager gameSessionManager;
    private final PlayerInteractionManager playerManager;
    private BukkitTask updateTask;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ActionBarManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameSessionManager = plugin.getGameSessionManager();
        this.playerManager = plugin.getPlayerInteractionManager();
    }

    /**
     * 启动动作条更新任务
     */
    public void startUpdateTask() {
        // 取消现有任务
        if (updateTask != null && !updateTask.isCancelled()) {
            updateTask.cancel();
        }

        // 创建新的动作条更新任务
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                // 为每个在游戏中的玩家更新动作条
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (playerManager.getPlayerStatus(player) == PlayerStatus.IN_GAME) {
                        updatePlayerActionBar(player);
                    }
                }
            }
        }.runTaskTimer(plugin, 20L, 10L); // 每0.5秒更新一次
    }

    /**
     * 更新玩家的动作条
     *
     * @param player 玩家
     */
    private void updatePlayerActionBar(Player player) {
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 计算最近队友距离
        double nearestTeammateDistance = findNearestTeammateDistance(player);

        // 计算最近灵魂距离
        double nearestSoulDistance = findNearestSoulDistance(player);

        // 构建动作条消息
        StringBuilder message = new StringBuilder();

        // 添加队友距离信息
        if (nearestTeammateDistance < Double.MAX_VALUE) {
            message.append(ChatColor.GREEN).append("最近队友: ")
                    .append(ChatColor.YELLOW).append(String.format("%.1f", nearestTeammateDistance))
                    .append(ChatColor.GREEN).append(" 格");
        } else {
            message.append(ChatColor.GREEN).append("最近队友: ")
                    .append(ChatColor.RED).append("无");
        }

        message.append("   ");

        // 添加灵魂距离信息
        if (nearestSoulDistance < Double.MAX_VALUE) {
            message.append(ChatColor.LIGHT_PURPLE).append("最近灵魂: ")
                    .append(ChatColor.YELLOW).append(String.format("%.1f", nearestSoulDistance))
                    .append(ChatColor.LIGHT_PURPLE).append(" 格");
        } else {
            message.append(ChatColor.LIGHT_PURPLE).append("最近灵魂: ")
                    .append(ChatColor.RED).append("无");
        }

        // 发送动作条消息
        player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                net.md_5.bungee.api.chat.TextComponent.fromLegacyText(message.toString()));
    }

    /**
     * 查找最近队友距离
     *
     * @param player 玩家
     * @return 最近队友距离，如果没有队友则返回Double.MAX_VALUE
     */
    private double findNearestTeammateDistance(Player player) {
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return Double.MAX_VALUE;
        }

        double minDistance = Double.MAX_VALUE;
        Location playerLoc = player.getLocation();

        for (UUID teammateId : gameSessionManager.getGameParticipants(gameName)) {
            if (teammateId.equals(player.getUniqueId())) {
                continue; // 跳过自己
            }

            Player teammate = Bukkit.getPlayer(teammateId);
            if (teammate != null && teammate.isOnline()
                    && teammate.getGameMode() != org.bukkit.GameMode.SPECTATOR
                    && teammate.getWorld().equals(player.getWorld())) {

                double distance = playerLoc.distance(teammate.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                }
            }
        }

        return minDistance;
    }

    /**
     * 查找最近灵魂距离
     *
     * @param player 玩家
     * @return 最近灵魂距离，如果没有灵魂则返回Double.MAX_VALUE
     */
    private double findNearestSoulDistance(Player player) {
        double minDistance = Double.MAX_VALUE;
        Location playerLoc = player.getLocation();

        // 查找附近的灵魂NPC（现在使用ArmorStand而非Villager）
        for (Entity entity : player.getWorld().getEntities()) {
            if (entity.hasMetadata("soulNPC") && entity.getWorld().equals(player.getWorld())) {
                double distance = playerLoc.distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                }
            }
        }

        return minDistance;
    }

    /**
     * 停止动作条更新任务
     */
    public void shutdown() {
        if (updateTask != null && !updateTask.isCancelled()) {
            updateTask.cancel();
        }
    }
}
