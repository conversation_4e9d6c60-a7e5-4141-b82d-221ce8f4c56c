package org.Ver_zhzh.deathZombieV4.utils;

import java.io.File;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;

/**
 * 管理购买点悬浮文字的类，替代原来的NPC购买点
 */
public class ShopHologramManager implements Listener {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final HologramHelper hologramHelper;
    private final ShootPluginHelper shootHelper;

    // 存储游戏中的购买点悬浮文字: <游戏名, <购买点ID, 悬浮文字ID>>
    private final Map<String, Map<String, String>> shopHolograms = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ShopHologramManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.hologramHelper = plugin.getHologramHelper();
        this.shootHelper = plugin.getShootPluginHelper();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 为游戏中的购买点创建悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void setupShopHolograms(String gameName) {
        plugin.getLogger().info("为游戏 " + gameName + " 设置购买点悬浮文字");

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("buyPoints")) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何购买点");
            return;
        }

        // 清理之前的悬浮文字（如果有）
        cleanupShopHolograms(gameName);

        // 获取购买点配置
        ConfigurationSection buyPointsSection = config.getConfigurationSection("buyPoints");
        for (String id : buyPointsSection.getKeys(false)) {
            ConfigurationSection pointConfig = buyPointsSection.getConfigurationSection(id);
            if (pointConfig == null) {
                continue;
            }

            String type = pointConfig.getString("type", "wp");
            String itemId = pointConfig.getString("itemId", "id1");
            String name = pointConfig.getString("name", "购买点");

            // 获取位置
            ConfigurationSection locSection = pointConfig.getConfigurationSection("location");
            if (locSection == null) {
                plugin.getLogger().warning("购买点 " + id + " 没有设置位置，跳过创建");
                continue;
            }

            String worldName = locSection.getString("world");
            double x = locSection.getDouble("x");
            double y = locSection.getDouble("y");
            double z = locSection.getDouble("z");
            float yaw = (float) locSection.getDouble("yaw");
            float pitch = (float) locSection.getDouble("pitch");

            if (worldName == null || Bukkit.getWorld(worldName) == null) {
                plugin.getLogger().warning("购买点 " + id + " 的世界 " + worldName + " 不存在，跳过创建");
                continue;
            }

            Location location = new Location(Bukkit.getWorld(worldName), x, y, z, yaw, pitch);

            // 获取物品价格
            int price = shootHelper.getItemPrice(type, itemId);
            if (price <= 0) {
                plugin.getLogger().warning("物品 " + type + "_" + itemId + " 价格无效，使用默认价格100");
                price = 100;
            }

            // 创建购买点悬浮文字
            createShopHologram(gameName, id, location, name, type, itemId, price);
        }
    }

    /**
     * 创建购买点悬浮文字
     *
     * @param gameName 游戏名称
     * @param shopId 购买点ID
     * @param location 位置
     * @param itemName 物品名称
     * @param type 物品类型
     * @param itemId 物品ID
     * @param price 价格
     */
    public void createShopHologram(String gameName, String shopId, Location location, String itemName, String type, String itemId, int price) {
        try {
            // 创建悬浮文字
            String holoId = hologramHelper.createShopHologram(gameName, location, itemName, type, itemId, price,
                    player -> handleShopHologramClick(player, gameName, type, itemId, itemName, price));

            // 存储悬浮文字引用
            if (holoId != null) {
                if (!shopHolograms.containsKey(gameName)) {
                    shopHolograms.put(gameName, new HashMap<>());
                }
                shopHolograms.get(gameName).put(shopId, holoId);

                // 不再创建单独的物品展示悬浮文字，避免重复
                plugin.getLogger().info("已为游戏 " + gameName + " 创建购买点悬浮文字，物品: " + itemName + "，ID: " + holoId);
            }
        } catch (Exception e) {
            plugin.getLogger().severe("创建购买点悬浮文字失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 已移除createItemDisplayHologram方法，物品展示现在直接集成在购买点悬浮文字中
    /**
     * 处理购买点悬浮文字点击事件
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param type 物品类型
     * @param itemId 物品ID
     * @param itemName 物品名称
     * @param price 价格
     */
    private void handleShopHologramClick(Player player, String gameName, String type, String itemId, String itemName, int price) {
        // 特殊处理手枪价格
        if (type.equals("wp") && (itemId.equals("id1") || itemId.equals("1"))) {
            price = 150;
            plugin.getLogger().info("【武器购买调试】特殊处理手枪价格为150金币");
        }
        // 检查玩家是否在游戏中
        if (!plugin.getGameSessionManager().isPlayerInGame(player.getUniqueId(), gameName)) {
            player.sendMessage(ChatColor.RED + "你不在游戏 " + gameName + " 中，无法购买物品！");
            return;
        }

        // 获取玩家余额
        double playerMoney = shootHelper.getPlayerMoney(player);

        if (playerMoney < price) {
            player.sendMessage(ChatColor.RED + "你的余额不足以购买此物品！需要 " + price + "，你只有 " + (int) playerMoney);
            return;
        }

        // 如果是弹药类型，先进行验证，然后直接处理弹药补充
        if (type.equals("it") && itemId.matches("id(3|4|5|6|7|8|9|10|11|12|15|16|17|18|19|20|21|22|23|24|25|26|27)")) {
            plugin.getLogger().info("【商店购买】检测到弹药购买请求: " + itemId + ", 玩家: " + player.getName());
            // 弹药类型 - 检查玩家是否手持对应的武器
            if (!validateAmmoPurchase(player, itemId)) {
                plugin.getLogger().info("【商店购买】弹药购买验证失败，取消购买");
                return; // 验证失败，直接返回
            }
            plugin.getLogger().info("【商店购买】弹药购买验证成功，开始处理弹药补充");

            // 扣除玩家金额
            boolean success = shootHelper.subtractPlayerMoney(player, price);
            if (!success) {
                player.sendMessage(ChatColor.RED + "购买失败，请稍后再试！");
                return;
            }

            // 记录花费金钱统计
            plugin.getPlayerInteractionManager().addPlayerMoneySpent(player, gameName, price);

            // 使用Shoot插件的原生弹药补充方法
            boolean ammoSuccess = shootHelper.replenishPlayerAmmoNative(player);

            if (ammoSuccess) {
                // 弹药补充成功
                player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.YELLOW + itemName + ChatColor.GREEN + "，已为你的武器补充弹药！");
                plugin.getLogger().info("【商店购买】弹药补充成功: " + itemId + ", 玩家: " + player.getName());
                // 播放声音效果
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
            } else {
                // 弹药补充失败，退还金币
                shootHelper.addPlayerMoney(player, price);
                player.sendMessage(ChatColor.RED + "弹药补充失败！请检查你的武器和弹药状态。已退还金币。");
                plugin.getLogger().warning("【商店购买】弹药补充失败: " + itemId + ", 玩家: " + player.getName());
            }

            return; // 弹药购买处理完成，直接返回，不继续执行后续逻辑
        }

        // 如果是武器类型，检查玩家是否在武器槽位或手持枪支
        if (type.equals("wp")) { // 所有武器类型，包括id1（手枪）
            boolean canBuyWeapon = false;
            boolean isHoldingGun = false;
            int weaponSlotIndex = -1;

            // 检查玩家是否手持枪支
            org.bukkit.inventory.ItemStack mainHandItem = player.getInventory().getItemInMainHand();
            if (mainHandItem != null && mainHandItem.getType() != org.bukkit.Material.AIR) {
                if (mainHandItem.hasItemMeta() && mainHandItem.getItemMeta().hasDisplayName()) {
                    // 检查是否是枪支（通过名称判断）
                    isHoldingGun = isGunItem(mainHandItem);
                    if (isHoldingGun) {
                        canBuyWeapon = true;
                    }
                }
            }

            // 如果不是手持枪支，检查是否有武器槽位
            if (!canBuyWeapon) {
                for (int i = 0; i < player.getInventory().getSize(); i++) {
                    org.bukkit.inventory.ItemStack item = player.getInventory().getItem(i);
                    if (item != null && item.getType() != org.bukkit.Material.AIR) {
                        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                            String displayName = org.bukkit.ChatColor.stripColor(item.getItemMeta().getDisplayName());
                            // 检查是否是武器槽位
                            if (displayName.equals("空武器槽位") || displayName.equals("武器槽位")) {
                                canBuyWeapon = true;
                                weaponSlotIndex = i;
                                break;
                            }
                        }
                    }
                }
            }

            if (!canBuyWeapon) {
                player.sendMessage(ChatColor.RED + "你必须手持枪支或有空武器槽位才能购买武器！");
                return;
            }

            // 扣除玩家金额
            boolean success = shootHelper.subtractPlayerMoney(player, price);
            if (!success) {
                player.sendMessage(ChatColor.RED + "购买失败，请稍后再试！");
                return;
            }

            // 记录花费金钱统计
            plugin.getPlayerInteractionManager().addPlayerMoneySpent(player, gameName, price);

            // 获取正确的映射ID
            String mappedId = itemId;
            if (plugin.getGameKitManager() != null) {
                String shootId = plugin.getGameKitManager().getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    mappedId = shootId;
                    plugin.getLogger().info("ShopHologramManager: 购买武器时使用映射ID: " + mappedId + " 替代 " + itemId);
                }
            }

            // 记录购买的武器ID，用于调试
            plugin.getLogger().info("【武器购买调试】玩家 " + player.getName() + " 购买武器: " + mappedId);

            // 特殊处理手枪(id1)购买
            if (mappedId.equals("id1") || mappedId.equals("1")) {
                plugin.getLogger().info("【武器购买调试】特殊处理手枪(id1)购买");

                // 直接使用Shoot插件的giveGunToPlayer方法给予手枪
                Plugin shootPlugin = plugin.getShootPlugin();
                if (shootPlugin != null) {
                    try {
                        // 如果是手持枪支，先清空手中物品
                        if (isHoldingGun) {
                            player.getInventory().setItemInMainHand(null);
                        } else if (weaponSlotIndex >= 0) {
                            // 如果是武器槽位，清空槽位
                            player.getInventory().setItem(weaponSlotIndex, null);
                        }

                        // 直接调用Shoot插件的giveGunToPlayer方法
                        Method method = shootPlugin.getClass().getMethod("giveGunToPlayer", Player.class, String.class);
                        method.invoke(shootPlugin, player, "id1");

                        player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.GOLD + itemName + ChatColor.GREEN + "，已添加到你的物品栏！");
                        plugin.getLogger().info("【武器购买调试】成功使用Shoot插件直接给予手枪(id1)");

                        // 播放声音效果
                        player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
                        return;
                    } catch (Exception e) {
                        plugin.getLogger().warning("【武器购买调试】直接给予手枪失败: " + e.getMessage());
                        e.printStackTrace();
                        // 如果直接给予失败，继续使用常规方法
                    }
                }
            }

            // 常规武器购买处理
            // 如果是手持枪支，替换手中的枪支
            if (isHoldingGun) {
                // 先清空手中物品，避免重复给予
                ItemStack oldItem = player.getInventory().getItemInMainHand();
                player.getInventory().setItemInMainHand(null);

                // 给予新枪支，使用映射后的ID
                boolean gunSuccess = shootHelper.giveGunToPlayer(player, mappedId);

                if (gunSuccess) {
                    player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.GOLD + itemName + ChatColor.GREEN + "，已替换手中的枪支！");
                } else {
                    // 如果给予失败，恢复原来的物品
                    player.getInventory().setItemInMainHand(oldItem);
                    player.sendMessage(ChatColor.RED + "购买失败，请稍后再试！");
                }
            } else if (weaponSlotIndex >= 0) {
                // 如果是武器槽位，替换槽位中的物品
                // 先保存原来的物品，以防给予失败需要恢复
                ItemStack oldItem = player.getInventory().getItem(weaponSlotIndex);
                player.getInventory().setItem(weaponSlotIndex, null); // 清空槽位

                // 使用映射后的ID创建枪支
                plugin.getLogger().info("【武器购买调试】尝试创建枪支物品: " + mappedId);
                org.bukkit.inventory.ItemStack gunItem = createGunItem(mappedId);

                if (gunItem != null) {
                    // 成功创建枪支物品，放入武器槽位
                    player.getInventory().setItem(weaponSlotIndex, gunItem);
                    player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.GOLD + itemName + ChatColor.GREEN + "，已放入武器槽位！");
                    plugin.getLogger().info("【武器购买调试】成功创建并放入武器槽位: " + mappedId);
                } else {
                    // 如果创建枪支物品失败，恢复原来的物品
                    player.getInventory().setItem(weaponSlotIndex, oldItem);
                    player.sendMessage(ChatColor.RED + "购买失败，请稍后再试！");
                    plugin.getLogger().warning("【武器购买调试】创建枪支物品失败: " + mappedId);
                }
            }

            // 播放声音效果
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
            return;
        }

        // 如果是特殊功能类型，先检查电源按钮状态
        if (type.equals("sp")) {
            // 检查全局电源按钮是否解锁
            boolean powerButtonUnlocked = isPowerButtonUnlocked(gameName);
            if (!powerButtonUnlocked) {
                player.sendMessage(ChatColor.RED + "全局电源按钮未解锁！请先解锁电源按钮才能购买增益效果。");
                player.sendMessage(ChatColor.YELLOW + "在 动力室 里找到 电力开关 以激活 增益机器！");
                return;
            }
        }

        // 扣除玩家金额（非武器类型）
        boolean success = shootHelper.subtractPlayerMoney(player, price);
        if (!success) {
            player.sendMessage(ChatColor.RED + "购买失败，请稍后再试！");
            return;
        }

        // 记录花费金钱统计
        plugin.getPlayerInteractionManager().addPlayerMoneySpent(player, gameName, price);

        // 根据类型和itemId处理购买逻辑
        boolean purchased = false;

        if (type.equals("ar")) {
            // 获取正确的映射ID
            String mappedId = itemId;
            if (plugin.getGameKitManager() != null) {
                String shootId = plugin.getGameKitManager().getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    mappedId = shootId;
                    plugin.getLogger().info("ShopHologramManager: 购买护甲时使用映射ID: " + mappedId + " 替代 " + itemId);
                }
            }

            // 护甲类型购买
            player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.AQUA + itemName + ChatColor.GREEN + "！");
            shootHelper.giveArmorToPlayer(player, mappedId);
            purchased = true;
        } else if (type.equals(
                "it")) {
            // 道具类型购买 - 为不同类型道具提供专门的提示
            // 注意：弹药类型已在前面单独处理，这里不会再处理弹药
            if (itemId.equals("id1") || itemId.equals("id2")) {
                // 药水效果提示
                player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.LIGHT_PURPLE + itemName + ChatColor.GREEN + " 效果！");
            } else {
                // 普通物品提示（排除弹药类型）
                if (!itemId.matches("id(3|4|5|6|7|8|9|10|11|12|15|16|17|18|19|20|21|22|23|24|25|26|27)")) {
                    player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.AQUA + itemName + ChatColor.GREEN + "，已添加到你的物品栏！");
                }
            }
            // 获取正确的映射ID
            String mappedId = itemId;
            if (plugin.getGameKitManager() != null) {
                String shootId = plugin.getGameKitManager().getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    mappedId = shootId;
                    plugin.getLogger().info("ShopHologramManager: 购买道具时使用映射ID: " + mappedId + " 替代 " + itemId);
                }
            }

            // 查找空的物品槽位（槽位6-8）
            int emptySlot = findEmptyItemSlot(player);
            if (emptySlot != -1) {
                // 创建物品
                ItemStack item = shootHelper.createItem("it", mappedId);
                if (item != null) {
                    // 放入物品槽位
                    player.getInventory().setItem(emptySlot, item);
                    player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.AQUA + itemName + ChatColor.GREEN + "，已放入物品槽位！");
                    plugin.getLogger().info("玩家 " + player.getName() + " 购买物品 " + mappedId + " 已放入槽位 " + emptySlot);
                } else {
                    // 如果创建物品失败，取消购买
                    player.sendMessage(ChatColor.RED + "购买失败：无法创建物品");
                    plugin.getLogger().warning("无法创建物品 " + mappedId + "，已取消购买");
                    purchased = false;
                    return; // 直接返回，不继续执行
                }
            } else {
                // 如果没有空的物品槽位，取消购买
                player.sendMessage(ChatColor.RED + "购买失败：你的物品槽位已满");
                purchased = false;
                return; // 直接返回，不继续执行
            }
        } else if (type.equals("sp")) {
            // 特殊功能类型购买（电源检查已在前面完成）
            // 获取正确的映射ID
            String mappedId = itemId;
            if (plugin.getGameKitManager() != null) {
                String shootId = plugin.getGameKitManager().getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    mappedId = shootId;
                    plugin.getLogger().info("ShopHologramManager: 激活特殊功能时使用映射ID: " + mappedId + " 替代 " + itemId);
                }
            }

            player.sendMessage(ChatColor.GREEN + "你已激活特殊功能: " + ChatColor.GOLD + itemName + ChatColor.GREEN + "！");
            shootHelper.activateSpecialFunction(player, mappedId);
            purchased = true;
        }

        if (purchased) {
            // 发送声音效果
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
        }
    }

    /**
     * 验证弹药购买是否合法（增强版，包含详细调试信息）
     *
     * @param player 玩家
     * @param itemId 弹药物品ID
     * @return 是否可以购买
     */
    private boolean validateAmmoPurchase(Player player, String itemId) {
        plugin.getLogger().info("【弹药购买验证】开始验证玩家 " + player.getName() + " 购买弹药 " + itemId);

        // 获取当前手持物品
        ItemStack handItem = player.getInventory().getItemInMainHand();

        if (handItem == null) {
            plugin.getLogger().info("【弹药购买验证】玩家手持物品为null");
            player.sendMessage(ChatColor.RED + "你必须手持武器才能购买弹药！");
            return false;
        }

        if (handItem.getType() == Material.AIR) {
            plugin.getLogger().info("【弹药购买验证】玩家手持物品为空气");
            player.sendMessage(ChatColor.RED + "你必须手持武器才能购买弹药！");
            return false;
        }

        if (!handItem.hasItemMeta()) {
            plugin.getLogger().info("【弹药购买验证】玩家手持物品没有ItemMeta");
            player.sendMessage(ChatColor.RED + "你必须手持武器才能购买弹药！");
            return false;
        }

        if (!handItem.getItemMeta().hasDisplayName()) {
            plugin.getLogger().info("【弹药购买验证】玩家手持物品没有显示名称");
            player.sendMessage(ChatColor.RED + "你必须手持武器才能购买弹药！");
            return false;
        }

        // 记录手持物品信息
        String displayName = ChatColor.stripColor(handItem.getItemMeta().getDisplayName());
        String materialType = handItem.getType().toString();
        plugin.getLogger().info("【弹药购买验证】玩家手持物品: " + displayName + ", 材质: " + materialType);

        // 检查是否手持枪支（使用增强的检测逻辑）
        boolean isGun = isGunItem(handItem);
        plugin.getLogger().info("【弹药购买验证】枪支检测结果: " + isGun);

        if (!isGun) {
            plugin.getLogger().info("【弹药购买验证】验证失败: 手持物品不是枪支");
            player.sendMessage(ChatColor.RED + "你必须手持枪支才能购买弹药！当前手持: " + displayName);
            return false;
        }

        plugin.getLogger().info("【弹药购买验证】验证成功: 玩家手持枪支 " + displayName);
        return true;
    }

    /**
     * 获取弹药对应的枪支类型
     *
     * @param ammoId 弹药物品ID
     * @return 对应的枪支ID
     */
    private String getExpectedGunId(String ammoId) {
        Map<String, String> ammoToGunMap = new HashMap<>();
        // 弹药映射
        ammoToGunMap.put("id3", "id1"); // 手枪弹药 -> 手枪(id1)
        ammoToGunMap.put("id4", "id2"); // 步枪弹药 -> 步枪(id2)
        ammoToGunMap.put("id5", "id3"); // 霰弹枪弹药 -> 霰弹枪(id3)
        ammoToGunMap.put("id6", "id4"); // 机枪弹药 -> 机枪(id4)
        ammoToGunMap.put("id7", "id5"); // 火箭筒弹药 -> 火箭筒(id5)
        ammoToGunMap.put("id8", "id6"); // 电击枪弹药 -> 电击枪(id6)
        ammoToGunMap.put("id9", "id7"); // 狙击步枪弹药 -> 狙击步枪(id7)
        ammoToGunMap.put("id10", "id8"); // 冰冻枪弹药 -> 冰冻枪(id8)
        ammoToGunMap.put("id11", "id9"); // 雷击枪弹药 -> 雷击枪(id9)
        ammoToGunMap.put("id12", "id10"); // 压强枪弹药 -> 压强枪(id10)
        // 新增弹药映射
        ammoToGunMap.put("id15", "id10"); // 压强枪弹药
        ammoToGunMap.put("id16", "id13"); // 等离子枪弹药
        ammoToGunMap.put("id17", "id14"); // 死神收割者弹药
        ammoToGunMap.put("id18", "id15"); // 毁灭者弹药
        ammoToGunMap.put("id19", "id16"); // 超级激光炮弹药
        ammoToGunMap.put("id20", "id17"); // 黑洞吞噬者弹药
        ammoToGunMap.put("id21", "id18"); // 音轨步枪弹药
        ammoToGunMap.put("id22", "id19"); // 能量脉冲枪弹药
        ammoToGunMap.put("id23", "id20"); // 彩虹喷射器弹药
        ammoToGunMap.put("id24", "id21"); // 传送枪弹药
        ammoToGunMap.put("id25", "id22"); // 星辉主孰者弹药
        ammoToGunMap.put("id26", "id23"); // 虚空星尘弹药
        ammoToGunMap.put("id27", "id24"); // 循声炮弹药

        return ammoToGunMap.get(ammoId);
    }

    /**
     * 检查物品是否是枪支（与Shoot插件保持一致的检测逻辑）
     *
     * @param item 物品
     * @return 是否是枪支
     */
    private boolean isGunItem(org.bukkit.inventory.ItemStack item) {
        if (item == null) {
            plugin.getLogger().info("【枪支检测】物品为null");
            return false;
        }

        // 检查物品是否有元数据和显示名称
        org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            plugin.getLogger().info("【枪支检测】物品没有ItemMeta或显示名称");
            return false;
        }

        // 获取物品显示名称
        String displayName = org.bukkit.ChatColor.stripColor(meta.getDisplayName());
        String materialType = item.getType().toString();

        plugin.getLogger().info("【枪支检测】检测物品: " + displayName + ", 材质: " + materialType);

        // 检查是否是僵尸猎人之剑（不算枪支）
        if (displayName.equals("僵尸猎人之剑")) {
            plugin.getLogger().info("【枪支检测】识别为僵尸猎人之剑，不是枪支");
            return false;
        }

        // 检查是否是弹药耗尽的武器（钻石）
        if (item.getType() == org.bukkit.Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
            // 检查是否有lore中包含gun_id标记
            if (meta.hasLore()) {
                java.util.List<String> lore = meta.getLore();
                if (lore != null) {
                    for (String line : lore) {
                        if (line.contains("gun_id:")) {
                            plugin.getLogger().info("【枪支检测】识别为弹药耗尽的武器: " + displayName);
                            return true; // 这是一个弹药耗尽的武器
                        }
                    }
                }
            }
        }

        // 尝试通过Shoot插件的配置检查是否是已定义的枪支
        try {
            org.bukkit.plugin.Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 使用反射获取Shoot插件的配置
                java.lang.reflect.Field configField = shootPlugin.getClass().getDeclaredField("config");
                configField.setAccessible(true);
                org.bukkit.configuration.file.FileConfiguration shootConfig =
                    (org.bukkit.configuration.file.FileConfiguration) configField.get(shootPlugin);

                if (shootConfig != null) {
                    org.bukkit.configuration.ConfigurationSection gunsSection = shootConfig.getConfigurationSection("guns");
                    if (gunsSection != null) {
                        // 检查名称是否匹配配置中的枪支
                        for (String id : gunsSection.getKeys(false)) {
                            String gunName = shootConfig.getString("guns." + id + ".name", "未知武器");
                            if (displayName.equals(gunName)) {
                                plugin.getLogger().info("【枪支检测】通过Shoot配置识别为枪支: " + displayName + " (ID: " + id + ")");
                                return true;
                            }
                        }

                        // 检查物品类型是否匹配配置中的枪支材质
                        org.bukkit.Material itemType = item.getType();
                        for (String id : gunsSection.getKeys(false)) {
                            String materialName = shootConfig.getString("guns." + id + ".material", "");
                            if (!materialName.isEmpty()) {
                                org.bukkit.Material gunMaterial = org.bukkit.Material.getMaterial(materialName);
                                if (gunMaterial != null && gunMaterial == itemType) {
                                    // 还需要检查名称是否也匹配，避免误判
                                    String gunName = shootConfig.getString("guns." + id + ".name", "未知武器");
                                    if (displayName.equals(gunName)) {
                                        plugin.getLogger().info("【枪支检测】通过Shoot配置材质+名称识别为枪支: " + displayName + " (ID: " + id + ", 材质: " + materialName + ")");
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("【枪支检测】访问Shoot插件配置时出错: " + e.getMessage());
        }

        // 如果无法通过Shoot插件配置验证，使用备用的关键词检测
        boolean isGunName = displayName.contains("枪")
                || displayName.contains("步枪")
                || displayName.contains("手枪")
                || displayName.contains("机枪")
                || displayName.contains("霰弹枪")
                || displayName.contains("狙击枪")
                || displayName.contains("冲锋枪")
                || displayName.contains("电击枪")
                || displayName.contains("冰冻枪")
                || displayName.contains("雷击枪")
                || displayName.contains("压强枪")
                || displayName.contains("等离子枪")
                || displayName.contains("收割者")
                || displayName.contains("毁灭者")
                || displayName.contains("激光炮")
                || displayName.contains("吞噬者")
                || displayName.contains("脉冲枪")
                || displayName.contains("喷射器")
                || displayName.contains("传送枪")
                || displayName.contains("主宰者")
                || displayName.contains("星尘")
                || displayName.contains("循声炮")
                || displayName.contains("火箭筒")
                // 添加更多可能的关键词
                || displayName.contains("AK")
                || displayName.contains("M4")
                || displayName.contains("AWP")
                || displayName.contains("Gun")
                || displayName.contains("Rifle")
                || displayName.contains("Pistol")
                || displayName.contains("Shotgun")
                || displayName.contains("Sniper")
                || displayName.toLowerCase().contains("gun")
                || displayName.toLowerCase().contains("rifle")
                || displayName.toLowerCase().contains("pistol");

        if (isGunName) {
            plugin.getLogger().info("【枪支检测】通过关键词识别为枪支: " + displayName);
            return true;
        } else {
            plugin.getLogger().info("【枪支检测】不是枪支: " + displayName + " (名称和材质都不匹配)");
            return false;
        }
    }

    /**
     * 创建枪支物品
     *
     * @param itemId 物品ID
     * @return 枪支物品
     */
    private org.bukkit.inventory.ItemStack createGunItem(String itemId) {
        try {
            // 记录原始ID，用于日志
            String originalId = itemId;
            plugin.getLogger().info("【武器购买调试】createGunItem: 原始武器ID: " + originalId);

            // 特殊处理id1（手枪）和id11（突击步枪）
            if (itemId.equals("id1") || itemId.equals("1")) {
                // 确保id1是手枪
                plugin.getLogger().info("【武器购买调试】createGunItem: 特殊处理id1为手枪");
                itemId = "id1"; // 确保使用正确的手枪ID
            } else if (itemId.equals("id11") || itemId.equals("11")) {
                // 确保id11是突击步枪
                plugin.getLogger().info("【武器购买调试】createGunItem: 特殊处理id11为突击步枪");
                itemId = "id11"; // 确保使用正确的突击步枪ID
            } else {
                // 从gameKit.yml获取正确的Shoot插件ID映射
                if (plugin.getGameKitManager() != null) {
                    String shootId = plugin.getGameKitManager().getShootId(itemId);
                    if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                        plugin.getLogger().info("【武器购买调试】createGunItem: 从gameKit.yml获取映射ID: " + shootId + " 替代 " + itemId);
                        itemId = shootId;
                    }
                }
            }

            // 确保ID格式正确
            String mappedId = itemId;
            if (!mappedId.startsWith("id") && mappedId.matches("\\d+")) {
                String oldId = mappedId;
                mappedId = "id" + mappedId;
                plugin.getLogger().info("【武器购买调试】createGunItem: 转换纯数字ID " + oldId + " 为Shoot格式: " + mappedId);
            }

            // 提取数字ID部分
            int gunId;
            if (mappedId.startsWith("id") && mappedId.length() > 2) {
                gunId = Integer.parseInt(mappedId.substring(2));
            } else {
                // 如果不是id格式，尝试直接解析为数字
                gunId = Integer.parseInt(mappedId);
            }

            // 检查是否是手枪或突击步枪，确保使用正确的ID
            if (mappedId.equals("id1")) {
                plugin.getLogger().info("【武器购买调试】createGunItem: 最终确认：创建手枪(id1), gunId=" + gunId);
                // 确保使用字符串ID "id1" 而不是数字ID 1
                return shootHelper.createGun(1);
            } else if (mappedId.equals("id11")) {
                plugin.getLogger().info("【武器购买调试】createGunItem: 最终确认：创建突击步枪(id11), gunId=" + gunId);
                // 确保使用字符串ID "id11" 而不是数字ID 11
                return shootHelper.createGun(11);
            } else {
                plugin.getLogger().info("【武器购买调试】createGunItem: 最终确认：创建武器: " + mappedId + ", gunId=" + gunId);
                // 使用映射后的ID创建枪支
                return shootHelper.createGun(gunId);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("【武器购买调试】创建枪支物品失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 添加新的购买点
     *
     * @param player 执行命令的玩家
     * @param typeAndId 物品类型和ID（例如 ar_id1, wp_id2）
     * @param gameName 游戏名称
     * @return 命令执行结果
     */
    public boolean addBuyPoint(Player player, String typeAndId, String gameName) {
        plugin.getLogger().info("添加购买点: typeAndId=" + typeAndId + ", gameName=" + gameName);

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            player.sendMessage(ChatColor.RED + "游戏 " + ChatColor.YELLOW + gameName + ChatColor.RED + " 不存在！");
            return false;
        }

        // 解析物品类型和ID
        String[] parts = typeAndId.split("_", 2); // 限制分割为最多2部分
        if (parts.length != 2) {
            player.sendMessage(ChatColor.RED + "物品格式错误！正确格式: 类型_ID (例如: ar_id1, wp_id2)");
            return false;
        }

        String type = parts[0].toLowerCase();
        String itemId = parts[1].toLowerCase();

        // 验证类型
        if (!type.equals("ar") && !type.equals("wp") && !type.equals("it") && !type.equals("sp")) {
            player.sendMessage(ChatColor.RED + "无效的物品类型！有效类型: ar (护甲), wp (武器), it (道具), sp (特殊功能)");
            return false;
        }

        // 检查物品是否存在
        if (!shootHelper.itemExists(type, itemId)) {
            player.sendMessage(ChatColor.RED + "物品 " + type + "_" + itemId + " 在Shoot插件中不存在！");
            plugin.getLogger().warning("物品不存在: type=" + type + ", itemId=" + itemId);
            player.sendMessage(ChatColor.YELLOW + "请确认此物品在Shoot插件的Buy.yml中定义。");
            return false;
        }

        // 获取玩家当前位置
        Location location = player.getLocation();

        // 获取物品名称和价格
        String itemName = shootHelper.getItemName(type, itemId);
        if (itemName == null) {
            itemName = type + "_" + itemId;
        }
        int price = shootHelper.getItemPrice(type, itemId);

        // 更新游戏配置
        File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
        if (!gameFile.exists()) {
            player.sendMessage(ChatColor.RED + "游戏配置文件不存在！路径: " + gameFile.getAbsolutePath());
            return false;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 设置购买点信息
        String buyPointId = UUID.randomUUID().toString();
        ConfigurationSection buyPointsSection = gameConfig.getConfigurationSection("buyPoints");
        if (buyPointsSection == null) {
            buyPointsSection = gameConfig.createSection("buyPoints");
        }

        ConfigurationSection buyPointSection = buyPointsSection.createSection(buyPointId);
        buyPointSection.set("type", type);
        buyPointSection.set("itemId", itemId);
        buyPointSection.set("name", itemName);
        buyPointSection.set("location.world", location.getWorld().getName());
        buyPointSection.set("location.x", location.getX());
        buyPointSection.set("location.y", location.getY());
        buyPointSection.set("location.z", location.getZ());
        buyPointSection.set("location.yaw", location.getYaw());
        buyPointSection.set("location.pitch", location.getPitch());

        try {
            gameConfig.save(gameFile);
            player.sendMessage(ChatColor.GREEN + "成功创建购买点: " + ChatColor.GOLD + itemName + ChatColor.GREEN + " (价格: " + price + ")");

            // 创建悬浮文字
            createShopHologram(gameName, buyPointId, location, itemName, type, itemId, price);

            return true;
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "保存购买点配置失败: " + e.getMessage());
            plugin.getLogger().severe("保存购买点配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 清理指定游戏的所有购买点悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void cleanupShopHolograms(String gameName) {
        if (!shopHolograms.containsKey(gameName)) {
            return;
        }

        for (String holoId : shopHolograms.get(gameName).values()) {
            hologramHelper.removeHologram(holoId);
        }

        shopHolograms.get(gameName).clear();
        plugin.getLogger().info("已清理游戏 " + gameName + " 的所有购买点悬浮文字");
    }

    /**
     * 清理所有购买点悬浮文字
     */
    public void cleanupAllHolograms() {
        for (String gameName : shopHolograms.keySet()) {
            cleanupShopHolograms(gameName);
        }
        shopHolograms.clear();
        plugin.getLogger().info("已清理所有购买点悬浮文字");
    }

    /**
     * 当游戏结束时清理悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void onGameEnd(String gameName) {
        cleanupShopHolograms(gameName);
    }

    /**
     * 获取所有购买点悬浮文字的映射
     *
     * @return 购买点悬浮文字映射 <游戏名, <购买点ID, 悬浮文字ID>>
     */
    public Map<String, Map<String, String>> getShopHolograms() {
        return shopHolograms;
    }

    /**
     * 检查游戏中的全局电源按钮是否已解锁
     *
     * @param gameName 游戏名称
     * @return 如果全局电源按钮已解锁或未设置电源按钮返回true，否则返回false
     */
    private boolean isPowerButtonUnlocked(String gameName) {
        // 获取游戏配置文件 - 修正路径从"games/"到"game/"
        File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
        if (!gameFile.exists()) {
            plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
            return false;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 检查电源按钮是否存在
        if (!gameConfig.isConfigurationSection("power_button")) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置电源按钮，允许直接购买特殊效果");
            return true; // 如果没有设置电源按钮，允许直接购买特殊效果
        }

        boolean unlocked = gameConfig.getBoolean("power_button.unlocked", false);
        plugin.getLogger().info("游戏 " + gameName + " 的电源按钮解锁状态: " + unlocked);
        return unlocked;
    }

    /**
     * 查找玩家物品栏中的空物品槽位（槽位6-8）
     *
     * @param player 玩家
     * @return 空物品槽位的索引，如果没有找到则返回-1
     */
    private int findEmptyItemSlot(Player player) {
        // 检查槽位6-8是否有空物品槽或物品槽位
        for (int i = 6; i <= 8; i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item == null) {
                // 空槽位，可以放置物品
                return i;
            } else if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                String displayName = ChatColor.stripColor(item.getItemMeta().getDisplayName());
                // 检查是否是物品槽位或空物品槽
                if (displayName.equals("物品槽位") || displayName.equals("空物品槽")) {
                    return i;
                }
            }
        }
        return -1;
    }
}
