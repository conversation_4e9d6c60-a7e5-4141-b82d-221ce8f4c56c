package org.Ver_zhzh.deathZombieV4.utils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;

/**
 * 管理玩家与游戏中物体的交互
 * 注意：玩家状态管理已移至GameSessionManager
 */
public class PlayerInteractionManager implements Listener {

    private final DeathZombieV4 plugin;

    // 存储玩家最后交互的门信息: <玩家UUID, <游戏名, 门名>>
    private final Map<UUID, Map.Entry<String, String>> lastInteractedDoors;

    // 存储玩家当前正在编辑的游戏: <玩家UUID, 游戏名>
    private final Map<UUID, String> editingGames;

    // 存储玩家临时数据: <玩家UUID, <键, 值>>
    private final Map<UUID, Map<String, String>> playerData;

    // 存储玩家游戏统计数据: <玩家UUID, <游戏名, 统计数据>>
    private final Map<UUID, Map<String, PlayerGameStatistics>> playerGameStats;

    /**
     * 玩家游戏统计数据类
     */
    public static class PlayerGameStatistics {
        private int zombieKills = 0;        // 击杀僵尸数
        private double moneyEarned = 0.0;   // 获得金钱
        private int doorsOpened = 0;        // 开启门数
        private double moneySpent = 0.0;    // 花费金钱
        private int windowsRepaired = 0;    // 修复窗户数

        public int getZombieKills() { return zombieKills; }
        public void setZombieKills(int zombieKills) { this.zombieKills = zombieKills; }
        public void addZombieKills(int kills) { this.zombieKills += kills; }

        public double getMoneyEarned() { return moneyEarned; }
        public void setMoneyEarned(double moneyEarned) { this.moneyEarned = moneyEarned; }
        public void addMoneyEarned(double money) { this.moneyEarned += money; }

        public int getDoorsOpened() { return doorsOpened; }
        public void setDoorsOpened(int doorsOpened) { this.doorsOpened = doorsOpened; }
        public void addDoorsOpened(int doors) { this.doorsOpened += doors; }

        public double getMoneySpent() { return moneySpent; }
        public void setMoneySpent(double moneySpent) { this.moneySpent = moneySpent; }
        public void addMoneySpent(double money) { this.moneySpent += money; }

        public int getWindowsRepaired() { return windowsRepaired; }
        public void setWindowsRepaired(int windowsRepaired) { this.windowsRepaired = windowsRepaired; }
        public void addWindowsRepaired(int windows) { this.windowsRepaired += windows; }

        public void reset() {
            zombieKills = 0;
            moneyEarned = 0.0;
            doorsOpened = 0;
            moneySpent = 0.0;
            windowsRepaired = 0;
        }
    }

    public PlayerInteractionManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.lastInteractedDoors = new HashMap<>();
        this.editingGames = new HashMap<>();
        this.playerData = new HashMap<>();
        this.playerGameStats = new HashMap<>();
    }

    /**
     * 设置玩家最后交互的门
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param doorName 门名称
     */
    public void setLastInteractedDoor(Player player, String gameName, String doorName) {
        lastInteractedDoors.put(player.getUniqueId(), Map.entry(gameName, doorName));
    }

    /**
     * 获取玩家最后交互的门
     *
     * @param player 玩家
     * @return 游戏名和门名的键值对，如果不存在则返回null
     */
    public Map.Entry<String, String> getLastInteractedDoor(Player player) {
        return lastInteractedDoors.get(player.getUniqueId());
    }

    /**
     * 清除玩家最后交互的门信息
     *
     * @param player 玩家
     */
    public void clearLastInteractedDoor(Player player) {
        lastInteractedDoors.remove(player.getUniqueId());
    }

    /**
     * 设置玩家当前正在编辑的游戏
     *
     * @param playerUUID 玩家UUID
     * @param gameName 游戏名称
     */
    public void setEditingGame(UUID playerUUID, String gameName) {
        editingGames.put(playerUUID, gameName);
    }

    /**
     * 设置玩家当前正在编辑的游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    public void setEditingGame(Player player, String gameName) {
        setEditingGame(player.getUniqueId(), gameName);
    }

    /**
     * 获取玩家当前正在编辑的游戏
     *
     * @param playerUUID 玩家UUID
     * @return 游戏名称，如果不存在则返回null
     */
    public String getEditingGame(UUID playerUUID) {
        return editingGames.get(playerUUID);
    }

    /**
     * 获取玩家当前正在编辑的游戏
     *
     * @param player 玩家
     * @return 游戏名称，如果不存在则返回null
     */
    public String getEditingGame(Player player) {
        return getEditingGame(player.getUniqueId());
    }

    /**
     * 清除玩家当前正在编辑的游戏信息
     *
     * @param playerUUID 玩家UUID
     */
    public void clearEditingGame(UUID playerUUID) {
        editingGames.remove(playerUUID);
    }

    /**
     * 清除玩家当前正在编辑的游戏信息
     *
     * @param player 玩家
     */
    public void clearEditingGame(Player player) {
        clearEditingGame(player.getUniqueId());
    }



    /**
     * 设置玩家临时数据
     *
     * @param player 玩家
     * @param key 键
     * @param value 值
     */
    public void setPlayerData(Player player, String key, String value) {
        UUID playerUUID = player.getUniqueId();
        playerData.putIfAbsent(playerUUID, new HashMap<>());
        playerData.get(playerUUID).put(key, value);
    }

    /**
     * 获取玩家临时数据
     *
     * @param player 玩家
     * @param key 键
     * @return 值，如果不存在则返回null
     */
    public String getPlayerData(Player player, String key) {
        UUID playerUUID = player.getUniqueId();
        if (!playerData.containsKey(playerUUID)) {
            return null;
        }
        return playerData.get(playerUUID).get(key);
    }

    /**
     * 清除玩家临时数据
     *
     * @param player 玩家
     * @param key 键，如果为null则清除所有数据
     */
    public void clearPlayerData(Player player, String key) {
        UUID playerUUID = player.getUniqueId();
        if (!playerData.containsKey(playerUUID)) {
            return;
        }

        if (key == null) {
            playerData.remove(playerUUID);
        } else {
            playerData.get(playerUUID).remove(key);
        }
    }

    /**
     * 移除玩家临时数据
     *
     * @param player 玩家
     * @param key 键
     */
    public void removePlayerData(Player player, String key) {
        clearPlayerData(player, key);
    }

    /**
     * 处理玩家破坏方块事件，用于设置全局电源按钮
     *
     * @param event 方块破坏事件
     */
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();

        // 检查玩家是否正在设置全局电源按钮
        String settingPowerButton = getPlayerData(player, "setting_power_button");
        if (settingPowerButton != null && settingPowerButton.equals("true")) {
            // 获取正在编辑的游戏名称
            String gameName = getEditingGame(player);
            if (gameName == null) {
                player.sendMessage(ChatColor.RED + "请先选择要编辑的游戏！");
                event.setCancelled(true);
                return;
            }

            // 获取电源按钮价格
            String priceStr = getPlayerData(player, "power_button_price");
            int price = 0;
            if (priceStr != null && !priceStr.isEmpty()) {
                try {
                    price = Integer.parseInt(priceStr);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析电源按钮价格: " + priceStr);
                }
            }

            // 获取破坏的方块位置
            Block block = event.getBlock();
            Location location = block.getLocation();

            // 保存全局电源按钮位置到游戏配置文件 - 修正路径从"games/"到"game/"
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                player.sendMessage(ChatColor.RED + "游戏配置文件不存在！");
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                event.setCancelled(true);
                return;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

            // 保存电源按钮位置和价格
            gameConfig.set("power_button.world", location.getWorld().getName());
            gameConfig.set("power_button.x", location.getX());
            gameConfig.set("power_button.y", location.getY());
            gameConfig.set("power_button.z", location.getZ());
            gameConfig.set("power_button.price", price);
            gameConfig.set("power_button.unlocked", false);

            try {
                gameConfig.save(gameFile);
                player.sendMessage(ChatColor.GREEN + "全局电源按钮设置成功！价格: " + price);

                // 如果游戏正在运行，创建全局电源按钮效果
                if (plugin.getGameSessionManager().getGameState(gameName) == org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
                    if (plugin.isDecentHologramsAvailable() && plugin.getPowerButtonEffectManager() != null) {
                        plugin.getPowerButtonEffectManager().createPowerButtonEffects(gameName);
                        plugin.getLogger().info("已为正在运行的游戏 " + gameName + " 创建全局电源按钮效果");
                    }
                }
            } catch (IOException e) {
                player.sendMessage(ChatColor.RED + "保存游戏配置文件失败！");
                plugin.getLogger().severe("保存游戏配置文件失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 清除玩家数据
            clearPlayerData(player, "setting_power_button");
            clearPlayerData(player, "power_button_price");

            // 取消方块破坏事件
            event.setCancelled(true);
        }
    }

    /**
     * 重置玩家数据 清除所有相关数据
     *
     * @param player 玩家
     */
    public void resetPlayerData(Player player) {
        // 清除玩家最后交互的门信息
        clearLastInteractedDoor(player);

        // 清除玩家正在编辑的游戏信息
        clearEditingGame(player);

        // 清除玩家临时数据
        clearPlayerData(player, null);

        // 清除玩家的发光效果
        player.setGlowing(false);

        plugin.getLogger().info("已重置玩家 " + player.getName() + " 的数据");
    }

    // ==================== 游戏统计数据管理方法 ====================

    /**
     * 获取玩家在指定游戏中的统计数据
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 统计数据对象
     */
    public PlayerGameStatistics getPlayerGameStatistics(Player player, String gameName) {
        UUID playerUUID = player.getUniqueId();
        playerGameStats.putIfAbsent(playerUUID, new HashMap<>());
        playerGameStats.get(playerUUID).putIfAbsent(gameName, new PlayerGameStatistics());
        return playerGameStats.get(playerUUID).get(gameName);
    }

    /**
     * 重置玩家在指定游戏中的统计数据
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    public void resetPlayerGameStatistics(Player player, String gameName) {
        UUID playerUUID = player.getUniqueId();
        playerGameStats.putIfAbsent(playerUUID, new HashMap<>());
        playerGameStats.get(playerUUID).put(gameName, new PlayerGameStatistics());
    }

    /**
     * 清除玩家的所有统计数据
     *
     * @param player 玩家
     */
    public void clearPlayerGameStatistics(Player player) {
        playerGameStats.remove(player.getUniqueId());
    }

    /**
     * 增加玩家击杀僵尸数
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param kills 击杀数量
     */
    public void addPlayerZombieKills(Player player, String gameName, int kills) {
        getPlayerGameStatistics(player, gameName).addZombieKills(kills);
    }

    /**
     * 增加玩家获得金钱数
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param money 金钱数量
     */
    public void addPlayerMoneyEarned(Player player, String gameName, double money) {
        getPlayerGameStatistics(player, gameName).addMoneyEarned(money);
    }

    /**
     * 增加玩家开启门数
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param doors 门数量
     */
    public void addPlayerDoorsOpened(Player player, String gameName, int doors) {
        getPlayerGameStatistics(player, gameName).addDoorsOpened(doors);
    }

    /**
     * 增加玩家花费金钱数
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param money 金钱数量
     */
    public void addPlayerMoneySpent(Player player, String gameName, double money) {
        getPlayerGameStatistics(player, gameName).addMoneySpent(money);
    }

    /**
     * 增加玩家修复窗户数
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param windows 窗户数量
     */
    public void addPlayerWindowsRepaired(Player player, String gameName, int windows) {
        getPlayerGameStatistics(player, gameName).addWindowsRepaired(windows);
    }

    // ==================== 兼容性方法（向后兼容） ====================
    // 注意：这些方法主要用于向后兼容，实际的玩家状态管理已移至GameSessionManager

    /**
     * 设置玩家状态（兼容性方法）
     * 注意：这个方法不会实际保存状态，只是为了向后兼容
     *
     * @param player 玩家
     * @param status 玩家状态
     * @param saveImmediately 是否立即保存（此参数被忽略）
     */
    @Deprecated
    public void setPlayerStatus(Player player, PlayerStatus status, boolean saveImmediately) {
        // 这个方法现在是空的，因为状态管理已移至GameSessionManager
        // 保留此方法只是为了向后兼容，避免编译错误
        plugin.getLogger().fine("setPlayerStatus调用已被忽略，状态管理已移至GameSessionManager");
    }

    /**
     * 设置玩家状态（兼容性方法）
     *
     * @param player 玩家
     * @param status 玩家状态
     */
    @Deprecated
    public void setPlayerStatus(Player player, PlayerStatus status) {
        setPlayerStatus(player, status, false);
    }

    /**
     * 获取玩家状态（兼容性方法）
     * 注意：这个方法总是返回NOT_JOINED，因为状态管理已移至GameSessionManager
     *
     * @param player 玩家
     * @return 总是返回NOT_JOINED
     */
    @Deprecated
    public PlayerStatus getPlayerStatus(Player player) {
        // 尝试从GameSessionManager获取实际状态
        String gameName = plugin.getGameSessionManager().getPlayerGame(player);
        if (gameName != null) {
            // 如果玩家在游戏中，根据游戏状态返回相应的状态
            var gameState = plugin.getGameSessionManager().getGameState(gameName);
            if (gameState != null) {
                switch (gameState) {
                    case WAITING:
                        return PlayerStatus.WAITING;
                    case RUNNING:
                        return PlayerStatus.IN_GAME;
                    default:
                        return PlayerStatus.NOT_JOINED;
                }
            }
        }
        return PlayerStatus.NOT_JOINED;
    }

    /**
     * 获取玩家状态（兼容性方法）
     *
     * @param playerUUID 玩家UUID
     * @return 总是返回NOT_JOINED
     */
    @Deprecated
    public PlayerStatus getPlayerStatus(UUID playerUUID) {
        Player player = plugin.getServer().getPlayer(playerUUID);
        if (player != null) {
            return getPlayerStatus(player);
        }
        return PlayerStatus.NOT_JOINED;
    }

    /**
     * 检查玩家是否处于特定状态（兼容性方法）
     *
     * @param player 玩家
     * @param status 要检查的状态
     * @return 如果玩家处于指定状态则返回true
     */
    @Deprecated
    public boolean isPlayerInStatus(Player player, PlayerStatus status) {
        return getPlayerStatus(player) == status;
    }

    /**
     * 清除玩家状态信息（兼容性方法）
     *
     * @param player 玩家
     * @param saveImmediately 是否立即保存（此参数被忽略）
     */
    @Deprecated
    public void clearPlayerStatus(Player player, boolean saveImmediately) {
        // 这个方法现在是空的，因为状态管理已移至GameSessionManager
        plugin.getLogger().fine("clearPlayerStatus调用已被忽略，状态管理已移至GameSessionManager");
    }

    /**
     * 清除玩家状态信息（兼容性方法）
     *
     * @param player 玩家
     */
    @Deprecated
    public void clearPlayerStatus(Player player) {
        clearPlayerStatus(player, false);
    }
}
