package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.PlayerStatisticsManager.PlayerStatistics;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 排行榜管理器
 * 处理玩家统计数据的排序和排行榜显示
 */
public class LeaderboardManager {

    private final DeathZombieV4 plugin;
    private final PlayerStatisticsManager statisticsManager;

    public LeaderboardManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.statisticsManager = plugin.getPlayerStatisticsManager();
    }

    /**
     * 统计类型枚举
     */
    public enum StatType {
        TOTAL_GAMES("总游戏次数", "games"),
        TOTAL_ZOMBIE_KILLS("累计击杀僵尸数", "kills"),
        TOTAL_ROUNDS_SURVIVED("累计生存回合数", "rounds"),
        TOTAL_TEAMMATE_RESCUES("累计救援队友数", "rescues"),
        HIGHEST_MONEY_EARNED("累计最高金钱数", "money"),
        MAP_MAX_ROUNDS("指定地图生存回合最大值", "maprounds"),
        MAP_FASTEST_TIME("最快单地图游戏耗时", "maptime");

        private final String displayName;
        private final String alias;

        StatType(String displayName, String alias) {
            this.displayName = displayName;
            this.alias = alias;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getAlias() {
            return alias;
        }

        public static StatType fromDisplayName(String input) {
            for (StatType type : values()) {
                if (type.getDisplayName().equals(input) || type.getAlias().equalsIgnoreCase(input)) {
                    return type;
                }
            }
            return null;
        }

        public static List<String> getAllDisplayNames() {
            List<String> names = new ArrayList<>();
            for (StatType type : values()) {
                names.add(type.getDisplayName());
            }
            return names;
        }

        public static List<String> getAllAliases() {
            List<String> aliases = new ArrayList<>();
            for (StatType type : values()) {
                aliases.add(type.getAlias());
            }
            return aliases;
        }
    }

    /**
     * 排行榜条目类
     */
    public static class LeaderboardEntry {
        private final UUID playerUuid;
        private final String playerName;
        private final double value;
        private final String mapName; // 仅用于地图相关统计

        public LeaderboardEntry(UUID playerUuid, String playerName, double value, String mapName) {
            this.playerUuid = playerUuid;
            this.playerName = playerName;
            this.value = value;
            this.mapName = mapName;
        }

        public UUID getPlayerUuid() { return playerUuid; }
        public String getPlayerName() { return playerName; }
        public double getValue() { return value; }
        public String getMapName() { return mapName; }
    }

    /**
     * 获取排行榜数据
     */
    public List<LeaderboardEntry> getLeaderboard(StatType statType, String mapName, int limit) {
        List<LeaderboardEntry> entries = new ArrayList<>();

        // 获取所有玩家的统计数据
        for (UUID uuid : getAllPlayerUuids()) {
            PlayerStatistics stats = statisticsManager.getPlayerStatistics(uuid);
            String playerName = getPlayerName(uuid);
            
            double value = 0;
            
            switch (statType) {
                case TOTAL_GAMES:
                    value = stats.getTotalGamesPlayed();
                    break;
                case TOTAL_ZOMBIE_KILLS:
                    value = stats.getTotalZombieKills();
                    break;
                case TOTAL_ROUNDS_SURVIVED:
                    value = stats.getTotalRoundsSurvived();
                    break;
                case TOTAL_TEAMMATE_RESCUES:
                    value = stats.getTotalTeammateRescues();
                    break;
                case HIGHEST_MONEY_EARNED:
                    value = stats.getHighestMoneyEarned();
                    break;
                case MAP_MAX_ROUNDS:
                    if (mapName != null && stats.getMapRecords().containsKey(mapName)) {
                        value = stats.getMapRecords().get(mapName).getMaxRoundsSurvived();
                    }
                    break;
                case MAP_FASTEST_TIME:
                    if (mapName != null && stats.getMapRecords().containsKey(mapName)) {
                        long time = stats.getMapRecords().get(mapName).getFastestCompletionTime();
                        value = time > 0 ? time : Double.MAX_VALUE; // 未完成的设为最大值
                    } else {
                        value = Double.MAX_VALUE;
                    }
                    break;
            }

            // 只添加有效数据
            if (value > 0 || (statType == StatType.MAP_FASTEST_TIME && value != Double.MAX_VALUE)) {
                entries.add(new LeaderboardEntry(uuid, playerName, value, mapName));
            }
        }

        // 排序
        if (statType == StatType.MAP_FASTEST_TIME) {
            // 最快时间：升序排列（时间越短越好）
            entries.sort(Comparator.comparingDouble(LeaderboardEntry::getValue));
        } else {
            // 其他统计：降序排列（数值越大越好）
            entries.sort((a, b) -> Double.compare(b.getValue(), a.getValue()));
        }

        // 限制数量
        return entries.stream().limit(limit).collect(Collectors.toList());
    }

    /**
     * 显示排行榜给玩家
     */
    public void showLeaderboard(Player player, StatType statType, String mapName, int limit) {
        List<LeaderboardEntry> leaderboard = getLeaderboard(statType, mapName, limit);
        
        if (leaderboard.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "暂无排行榜数据！");
            return;
        }

        // 构建标题
        String title = statType.getDisplayName();
        if (mapName != null && !mapName.isEmpty()) {
            title += " - " + mapName;
        }
        
        player.sendMessage(ChatColor.GOLD + "========== " + title + " ==========");
        
        // 显示排行榜
        for (int i = 0; i < leaderboard.size(); i++) {
            LeaderboardEntry entry = leaderboard.get(i);
            int rank = i + 1;
            
            String rankColor = getRankColor(rank);
            String valueStr = formatValue(statType, entry.getValue());
            
            player.sendMessage(rankColor + rank + ". " + ChatColor.WHITE + entry.getPlayerName() + 
                             ChatColor.GRAY + " - " + ChatColor.YELLOW + valueStr);
        }
        
        // 显示玩家自己的排名
        showPlayerRank(player, statType, mapName);
    }

    /**
     * 显示玩家自己的排名
     */
    private void showPlayerRank(Player player, StatType statType, String mapName) {
        List<LeaderboardEntry> fullLeaderboard = getLeaderboard(statType, mapName, Integer.MAX_VALUE);
        
        for (int i = 0; i < fullLeaderboard.size(); i++) {
            LeaderboardEntry entry = fullLeaderboard.get(i);
            if (entry.getPlayerUuid().equals(player.getUniqueId())) {
                int rank = i + 1;
                String valueStr = formatValue(statType, entry.getValue());
                player.sendMessage(ChatColor.AQUA + "你的排名: " + ChatColor.YELLOW + rank + 
                                 ChatColor.GRAY + " (" + valueStr + ")");
                return;
            }
        }
        
        player.sendMessage(ChatColor.GRAY + "你暂未上榜");
    }

    /**
     * 获取排名颜色
     */
    private String getRankColor(int rank) {
        switch (rank) {
            case 1: return ChatColor.GOLD.toString();
            case 2: return ChatColor.GRAY.toString();
            case 3: return ChatColor.YELLOW.toString();
            default: return ChatColor.WHITE.toString();
        }
    }

    /**
     * 格式化数值显示
     */
    private String formatValue(StatType statType, double value) {
        switch (statType) {
            case MAP_FASTEST_TIME:
                if (value == Double.MAX_VALUE) {
                    return "未完成";
                }
                return formatTime((long) value);
            case HIGHEST_MONEY_EARNED:
                return String.format("%.1f", value) + " 金币";
            default:
                return String.valueOf((int) value);
        }
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 获取所有玩家UUID
     */
    private Set<UUID> getAllPlayerUuids() {
        return statisticsManager.getAllPlayerUuids();
    }

    /**
     * 获取玩家名称
     */
    private String getPlayerName(UUID uuid) {
        Player player = Bukkit.getPlayer(uuid);
        if (player != null) {
            return player.getName();
        }
        
        // 如果玩家不在线，尝试从缓存获取名称
        // TODO: 实现离线玩家名称缓存
        return "Unknown";
    }
}
