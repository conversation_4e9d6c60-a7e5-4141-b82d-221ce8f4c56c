package org.Ver_zhzh.deathZombieV4.utils;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Handler;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.Logger;

/**
 * 全局日志管理器 - 负责拦截所有Bukkit日志并将DeathZombieV4相关的日志重定向到单独的文件
 */
public class GlobalLogManager {

    private final DeathZombieV4 plugin;
    private final Logger rootLogger;
    private File logFolder;
    private File logFile;
    private PrintWriter logWriter;
    private boolean enabled;
    private Level logLevel;
    private boolean dailyFiles;
    private boolean consoleOutput;
    private PluginLogHandler logHandler;
    private Handler[] originalHandlers;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public GlobalLogManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.rootLogger = Logger.getLogger("");
        initialize();
    }

    /**
     * 初始化日志管理器
     */
    private void initialize() {
        // 从配置文件加载设置
        this.enabled = plugin.getConfig().getBoolean("logging.enabled", true);
        String levelStr = plugin.getConfig().getString("logging.level", "INFO");
        this.logLevel = parseLogLevel(levelStr);
        this.dailyFiles = plugin.getConfig().getBoolean("logging.daily_files", false);
        this.consoleOutput = plugin.getConfig().getBoolean("logging.console_output", false);

        if (!enabled) {
            plugin.getLogger().info("日志文件输出已禁用");
            return;
        }

        // 创建日志文件夹
        String folderPath = plugin.getConfig().getString("logging.folder", "logs");
        this.logFolder = new File(plugin.getDataFolder(), folderPath);
        if (!logFolder.exists()) {
            if (logFolder.mkdirs()) {
                plugin.getLogger().info("已创建日志文件夹: " + logFolder.getAbsolutePath());
            } else {
                plugin.getLogger().warning("无法创建日志文件夹: " + logFolder.getAbsolutePath());
                enabled = false;
                return;
            }
        }

        // 创建日志文件
        createLogFile();

        // 保存原始处理器
        this.originalHandlers = rootLogger.getHandlers();

        // 如果不需要控制台输出，移除所有原始处理器
        if (!consoleOutput) {
            for (Handler handler : originalHandlers) {
                rootLogger.removeHandler(handler);
            }
        }

        // 添加自定义日志处理器
        this.logHandler = new PluginLogHandler();
        rootLogger.addHandler(logHandler);

        // 设置日志级别
        logHandler.setLevel(logLevel);

        plugin.getLogger().info("全局日志管理器已初始化，DeathZombieV4日志将输出到: " + logFile.getAbsolutePath());
    }

    /**
     * 创建日志文件
     */
    private void createLogFile() {
        try {
            if (dailyFiles) {
                // 使用日期作为文件名
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String fileName = dateFormat.format(new Date()) + ".log";
                this.logFile = new File(logFolder, fileName);
            } else {
                // 使用固定的latest.log文件名
                this.logFile = new File(logFolder, "latest.log");
            }

            // 创建或覆盖日志文件
            this.logWriter = new PrintWriter(new FileOutputStream(logFile, false));

            // 写入日志头
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logWriter.println("=== DeathZombieV4 日志 ===");
            logWriter.println("开始时间: " + timeFormat.format(new Date()));
            logWriter.println("日志级别: " + logLevel.getName());
            logWriter.println("===========================");
            logWriter.flush();

        } catch (IOException e) {
            plugin.getLogger().warning("创建日志文件失败: " + e.getMessage());
            enabled = false;
        }
    }

    /**
     * 解析日志级别字符串
     *
     * @param levelStr 日志级别字符串
     * @return 日志级别
     */
    private Level parseLogLevel(String levelStr) {
        switch (levelStr.toUpperCase()) {
            case "SEVERE":
                return Level.SEVERE;
            case "WARNING":
                return Level.WARNING;
            case "INFO":
            default:
                return Level.INFO;
        }
    }

    /**
     * 记录日志消息
     *
     * @param level 日志级别
     * @param message 日志消息
     */
    public void log(Level level, String message) {
        if (!enabled || logWriter == null) {
            return;
        }

        // 检查日志级别
        if (level.intValue() < logLevel.intValue()) {
            return;
        }

        // 格式化日志消息
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
        String formattedMessage = String.format("[%s %s] %s",
                timeFormat.format(new Date()),
                level.getName(),
                message);

        // 写入日志文件
        logWriter.println(formattedMessage);
        logWriter.flush();

        // 如果需要控制台输出，也输出到控制台
        if (consoleOutput) {
            if (level == Level.SEVERE) {
                plugin.getLogger().severe(message);
            } else if (level == Level.WARNING) {
                plugin.getLogger().warning(message);
            } else {
                plugin.getLogger().info(message);
            }
        }
    }

    /**
     * 关闭日志管理器
     */
    public void shutdown() {
        if (!enabled) {
            return;
        }

        if (logHandler != null) {
            rootLogger.removeHandler(logHandler);
        }

        // 恢复原始处理器
        if (!consoleOutput && originalHandlers != null) {
            for (Handler handler : originalHandlers) {
                rootLogger.addHandler(handler);
            }
        }

        if (logWriter != null) {
            // 写入日志尾
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logWriter.println("=== 日志结束 ===");
            logWriter.println("结束时间: " + timeFormat.format(new Date()));
            logWriter.println("=================");
            logWriter.flush();
            logWriter.close();
            plugin.getLogger().info("日志文件已关闭");
        }
    }

    /**
     * 插件日志处理器
     */
    private class PluginLogHandler extends Handler {

        @Override
        public void publish(LogRecord record) {
            if (!enabled || logWriter == null) {
                return;
            }

            // 检查日志级别
            if (record.getLevel().intValue() < logLevel.intValue()) {
                return;
            }

            // 检查是否是DeathZombieV4的日志
            String loggerName = record.getLoggerName();
            if (loggerName == null || !loggerName.contains("DeathZombieV4")) {
                return;
            }

            // 排除启动信息
            String message = record.getMessage();
            if (message != null && (message.contains("Enabling DeathZombieV4")
                    || message.contains("____  ____")
                    || message.contains("Version:")
                    || message.contains("Author:")
                    || message.contains("支持多种僵尸类型")
                    || message.contains("插件已启用"))) {
                return;
            }

            // 格式化日志消息
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            String formattedMessage = String.format("[%s %s] %s",
                    timeFormat.format(new Date(record.getMillis())),
                    record.getLevel().getName(),
                    record.getMessage());

            // 写入日志文件
            logWriter.println(formattedMessage);
            logWriter.flush();

            // 如果有异常，也记录异常堆栈
            Throwable thrown = record.getThrown();
            if (thrown != null) {
                logWriter.println("Exception: " + thrown.toString());
                for (StackTraceElement element : thrown.getStackTrace()) {
                    logWriter.println("\tat " + element.toString());
                }
                logWriter.flush();
            }
        }

        @Override
        public void flush() {
            if (logWriter != null) {
                logWriter.flush();
            }
        }

        @Override
        public void close() throws SecurityException {
            // 关闭操作在shutdown方法中处理
        }
    }
}
