package org.Ver_zhzh.deathZombieV4.commands;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.configuration.file.YamlConfiguration;
import java.io.File;
import org.bukkit.entity.Zombie;
import java.util.Map;

/**
 * 处理/czm命令的执行器类
 */
public class CZMCommandExecutor implements CommandExecutor {

    private final DeathZombieV4 plugin;
    private final ZombieHelper zombieHelper;
    private final CZMTabCompleter tabCompleter;

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public CZMCommandExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.zombieHelper = plugin.getZombieHelper();
        this.tabCompleter = new CZMTabCompleter(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 允许非玩家（控制台、命令方块等）使用此命令
        Player player = null;
        if (sender instanceof Player) {
            player = (Player) sender;
        }

        if (args.length < 1) {
            sendHelpMessage(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "make":
                return handleMakeCommand(sender, args);

            case "zombie":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定僵尸名称或ID! 用法: /czm zombie <名称|ID>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm zombie 普通僵尸 或 /czm zombie id1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String zombieInput = args[1];
                String zombieId = convertZombieNameToId(zombieInput);

                if (zombieId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的僵尸名称或ID: " + zombieInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的僵尸名称");
                    return true;
                }

                Zombie zombie = zombieHelper.spawnCustomZombie(player.getLocation(), zombieId);

                if (zombie != null) {
                    String zombieDisplayName = getZombieDisplayName(zombieInput, zombieId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成自定义僵尸: " + zombieDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成自定义僵尸失败");
                }
                return true;

            case "other":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定实体名称或ID! 用法: /czm other <名称|IDC>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm other 变异僵尸01 或 /czm other idc1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String entityInput = args[1];
                String entityId = convertEntityNameToId(entityInput);

                if (entityId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的实体名称或ID: " + entityInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的实体名称");
                    return true;
                }

                // 调用CustomZombie的spawnOtherEntity方法，传入Player参数
                zombieHelper.getCustomZombie().spawnOtherEntity(player, entityId);
                String entityDisplayName = getEntityDisplayName(entityInput, entityId);
                sender.sendMessage(ChatColor.GREEN + "成功生成实体: " + entityDisplayName);
                return true;

            case "npc":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定NPC名称或ID! 用法: /czm npc <名称|IDN>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                // 检查Citizens插件是否可用
                if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                    sender.sendMessage(ChatColor.RED + "无法生成NPC! 服务器未安装Citizens插件。");
                    return true;
                }

                String npcInput = args[1];
                String npcId = convertNpcNameToId(npcInput);

                if (npcId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的NPC名称或ID: " + npcInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的NPC名称");
                    return true;
                }

                boolean npcSuccess = spawnNPC(player, npcId);

                if (npcSuccess) {
                    String npcDisplayName = getNpcDisplayName(npcInput, npcId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成NPC: " + npcDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成NPC失败: " + npcId);
                }
                return true;

            case "gui":
                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要打开GUI界面!");
                    return true;
                }

                // 尝试打开GUI菜单
                boolean guiOpened = zombieHelper.openGUI(player);
                if (!guiOpened) {
                    sender.sendMessage(ChatColor.RED + "GUI管理器未初始化，无法打开GUI界面");
                }
                return true;

            case "spawnmode":
                // spawnmode命令需要管理员权限
                if (player != null && !player.hasPermission("deathzombiev4.admin")) {
                    sender.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 4) {
                    sender.sendMessage(ChatColor.RED + "用法: /czm spawnmode <Zombie|Entity> <User|Normal|InGame> <on|off>");
                    sender.sendMessage(ChatColor.YELLOW + "Zombie - 僵尸系统模式切换");
                    sender.sendMessage(ChatColor.YELLOW + "Entity - 实体系统模式切换");
                    sender.sendMessage(ChatColor.YELLOW + "User - 用户自定义模式");
                    sender.sendMessage(ChatColor.YELLOW + "Normal - 默认模式");
                    sender.sendMessage(ChatColor.YELLOW + "InGame - 游戏中用户实体生成开关（仅Entity系统）");
                    return true;
                }
                return handleSpawnModeCommand(sender, args[1], args[2], args[3]);

            case "help":
            default:
                sendHelpMessage(sender);
                return true;
        }
    }

    /**
     * 根据NPC ID生成相应的NPC
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 是否成功生成
     */
    private boolean spawnNPC(Player player, String npcId) {
        switch (npcId) {
            case "idn1":
                // 感染者1
                return zombieHelper.createInfectedNPC1(player.getLocation());
            case "idn2":
                // 感染者2号
                return zombieHelper.createInfectedNPC2(player.getLocation());
            case "idn3":
                // 感染者农民
                return zombieHelper.createInfectedFarmer(player.getLocation());
            case "idn4":
                // 感染者居民
                return zombieHelper.createInfectedResident(player.getLocation());
            case "idn5":
                // 感染猪
                return zombieHelper.createInfectedPig(player.getLocation());
            default:
                return false;
        }
    }

    /**
     * 处理SpawnMode指令 - 快速切换僵尸/实体系统模式
     */
    private boolean handleSpawnModeCommand(CommandSender sender, String systemType, String modeType, String action) {
        // 验证参数
        if (!systemType.equalsIgnoreCase("Zombie") && !systemType.equalsIgnoreCase("Entity")) {
            sender.sendMessage(ChatColor.RED + "系统类型必须是 'Zombie' 或 'Entity'！");
            return true;
        }

        if (!modeType.equalsIgnoreCase("User") && !modeType.equalsIgnoreCase("Normal") && !modeType.equalsIgnoreCase("InGame")) {
            sender.sendMessage(ChatColor.RED + "模式类型必须是 'User'、'Normal' 或 'InGame'！");
            return true;
        }

        if (!action.equalsIgnoreCase("on") && !action.equalsIgnoreCase("off")) {
            sender.sendMessage(ChatColor.RED + "操作必须是 'on' 或 'off'！");
            return true;
        }

        boolean isZombieSystem = systemType.equalsIgnoreCase("Zombie");
        boolean isUserMode = modeType.equalsIgnoreCase("User");
        boolean isInGameMode = modeType.equalsIgnoreCase("InGame");
        boolean enableMode = action.equalsIgnoreCase("on");

        try {
            if (isZombieSystem) {
                // 僵尸系统不支持InGame模式
                if (isInGameMode) {
                    sender.sendMessage(ChatColor.RED + "僵尸系统不支持InGame模式！");
                    return true;
                }
                // 处理僵尸系统模式切换
                return handleZombieSystemModeSwitch(sender, isUserMode, enableMode);
            } else {
                // 处理实体系统模式切换
                return handleEntitySystemModeSwitch(sender, isUserMode, isInGameMode, enableMode);
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("SpawnMode指令执行失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 处理僵尸系统模式切换
     */
    private boolean handleZombieSystemModeSwitch(CommandSender sender, boolean isUserMode, boolean enableMode) {
        try {
            // 读取zombie.yml配置
            File zombieConfigFile = new File(plugin.getDataFolder(), "zombie.yml");
            if (!zombieConfigFile.exists()) {
                sender.sendMessage(ChatColor.RED + "zombie.yml配置文件不存在！");
                return true;
            }

            YamlConfiguration zombieConfig = YamlConfiguration.loadConfiguration(zombieConfigFile);

            if (enableMode) {
                // 开启指定模式，关闭另一个模式
                if (isUserMode) {
                    zombieConfig.set("system_settings.use_user_custom_settings", true);
                    zombieConfig.set("system_settings.use_default_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换僵尸系统到用户自定义模式！");
                } else {
                    zombieConfig.set("system_settings.use_default_settings", true);
                    zombieConfig.set("system_settings.use_user_custom_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换僵尸系统到默认模式！");
                }
            } else {
                // 关闭指定模式，开启另一个模式
                if (isUserMode) {
                    zombieConfig.set("system_settings.use_user_custom_settings", false);
                    zombieConfig.set("system_settings.use_default_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭僵尸用户自定义模式，切换到默认模式！");
                } else {
                    zombieConfig.set("system_settings.use_default_settings", false);
                    zombieConfig.set("system_settings.use_user_custom_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭僵尸默认模式，切换到用户自定义模式！");
                }
            }

            // 保存配置文件
            zombieConfig.save(zombieConfigFile);

            // 重载双僵尸系统配置
            if (plugin.getDualZombieSystemManager() != null) {
                plugin.getDualZombieSystemManager().reloadConfig();
                // 只在debug模式下输出到日志，不再给玩家显示
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("僵尸系统配置已重新加载");
                }
            }

            return true;
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "僵尸系统模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("僵尸系统模式切换失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 处理实体系统模式切换
     */
    private boolean handleEntitySystemModeSwitch(CommandSender sender, boolean isUserMode, boolean isInGameMode, boolean enableMode) {
        try {
            // 读取entity.yml配置
            File entityConfigFile = new File(plugin.getDataFolder(), "entity.yml");
            if (!entityConfigFile.exists()) {
                sender.sendMessage(ChatColor.RED + "entity.yml配置文件不存在！");
                return true;
            }

            YamlConfiguration entityConfig = YamlConfiguration.loadConfiguration(entityConfigFile);

            if (isInGameMode) {
                // 处理游戏中用户实体生成开关
                entityConfig.set("system_settings.enable_in_game_user_entities", enableMode);
                if (enableMode) {
                    sender.sendMessage(ChatColor.GREEN + "已启用游戏中用户自定义实体生成！");
                } else {
                    sender.sendMessage(ChatColor.GREEN + "已禁用游戏中用户自定义实体生成！");
                }
            } else if (enableMode) {
                // 开启指定模式，关闭另一个模式
                if (isUserMode) {
                    entityConfig.set("system_settings.use_user_custom_settings", true);
                    entityConfig.set("system_settings.use_default_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换实体系统到用户自定义模式！");
                } else {
                    entityConfig.set("system_settings.use_default_settings", true);
                    entityConfig.set("system_settings.use_user_custom_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换实体系统到默认模式！");
                }
            } else {
                // 关闭指定模式，开启另一个模式
                if (isUserMode) {
                    entityConfig.set("system_settings.use_user_custom_settings", false);
                    entityConfig.set("system_settings.use_default_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭实体用户自定义模式，切换到默认模式！");
                } else {
                    entityConfig.set("system_settings.use_default_settings", false);
                    entityConfig.set("system_settings.use_user_custom_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭实体默认模式，切换到用户自定义模式！");
                }
            }

            // 保存配置文件
            entityConfig.save(entityConfigFile);

            // 重载实体系统配置
            if (plugin.getZombieHelper() != null &&
                plugin.getZombieHelper().getCustomZombie() != null &&
                plugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {
                plugin.getZombieHelper().getCustomZombie().getEntityIntegration().reloadConfig();
                // 只在debug模式下输出到日志，不再给玩家显示
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("实体系统配置已重新加载");
                }
            }

            return true;
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "实体系统模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("实体系统模式切换失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 发送帮助信息
     *
     * @param sender 命令发送者
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "======= CustomZombie 帮助 =======");
        sender.sendMessage(ChatColor.GREEN + "/czm zombie <名称|ID> - 生成指定的僵尸 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 普通僵尸, 小僵尸, 路障僵尸, 钻斧僵尸...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm zombie 普通僵尸 或 /czm zombie id1");
        sender.sendMessage(ChatColor.GREEN + "/czm other <名称|IDC> - 生成指定的其他实体 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 变异僵尸01, 变异僵尸02, 变异烈焰人...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm other 变异僵尸01 或 /czm other idc1");
        sender.sendMessage(ChatColor.GREEN + "/czm npc <名称|IDN> - 生成指定的NPC (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 感染者史蒂夫, 感染者艾利克斯, 感染者农民...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
        sender.sendMessage(ChatColor.GREEN + "/czm gui - 打开自定义僵尸GUI菜单 (需要玩家执行)");
        sender.sendMessage(ChatColor.AQUA + "/czm make <create|gui|list|delete|spawn> - IDZ自定义怪物系统");
        sender.sendMessage(ChatColor.YELLOW + "  /czm make create <名称> - 创建新的IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "  /czm make gui <名称> - 编辑IDZ怪物属性");
        sender.sendMessage(ChatColor.YELLOW + "  /czm make list - 查看所有IDZ怪物");
        sender.sendMessage(ChatColor.GREEN + "/czm spawnmode <Zombie|Entity> <User|Normal|InGame> <on|off> - 快速切换系统模式");
        sender.sendMessage(ChatColor.GREEN + "/czm help - 显示帮助信息");
        sender.sendMessage(ChatColor.GRAY + "提示: 使用Tab键可以自动补全中文名称");
    }

    /**
     * 转换僵尸名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertZombieNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("id")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getZombieIdByName(input);
        return id;
    }

    /**
     * 转换实体名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertEntityNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idc")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getEntityIdByName(input);
        return id;
    }

    /**
     * 转换NPC名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertNpcNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idn")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getNpcIdByName(input);
        return id;
    }

    /**
     * 获取僵尸显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getZombieDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("id")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.zombieNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取实体显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getEntityDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idc")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.entityNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取NPC显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getNpcDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idn")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.npcNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 处理make子命令
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 是否成功处理命令
     */
    private boolean handleMakeCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sendMakeHelpMessage(sender);
            return true;
        }

        // 获取IDZ怪物管理器
        org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager = plugin.getIDZMonsterManager();
        if (idzManager == null) {
            sender.sendMessage(ChatColor.RED + "IDZ怪物管理器未初始化！");
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "create":
                return handleMakeCreate(sender, args, idzManager);

            case "gui":
                return handleMakeGUI(sender, args, idzManager);

            case "list":
                return handleMakeList(sender, idzManager);

            case "delete":
                return handleMakeDelete(sender, args, idzManager);

            case "spawn":
                return handleMakeSpawn(sender, args, idzManager);

            default:
                sendMakeHelpMessage(sender);
                return true;
        }
    }

    /**
     * 处理make create命令
     */
    private boolean handleMakeCreate(CommandSender sender, String[] args,
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "请指定怪物名称! 用法: /czm make create <怪物名称>");
            return true;
        }

        String monsterName = args[2];

        // 生成IDZ ID（基于名称）
        String monsterId = generateIDZId(monsterName);

        // 检查ID是否已存在
        if (idzManager.monsterExists(monsterId)) {
            sender.sendMessage(ChatColor.RED + "IDZ怪物已存在: " + monsterName + " (ID: " + monsterId + ")");
            return true;
        }

        // 创建新的IDZ怪物
        if (idzManager.createMonster(monsterId, monsterName)) {
            sender.sendMessage(ChatColor.GREEN + "成功创建IDZ怪物: " + monsterName + " (ID: " + monsterId + ")");
            sender.sendMessage(ChatColor.YELLOW + "使用 /czm make gui " + monsterName + " 来编辑怪物属性");
        } else {
            sender.sendMessage(ChatColor.RED + "创建IDZ怪物失败: " + monsterName);
        }

        return true;
    }

    /**
     * 处理make gui命令
     */
    private boolean handleMakeGUI(CommandSender sender, String[] args,
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "此命令只能由玩家执行！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "请指定怪物名称! 用法: /czm make gui <怪物名称>");
            return true;
        }

        String monsterName = args[2];
        String monsterId = generateIDZId(monsterName);

        if (!idzManager.monsterExists(monsterId)) {
            sender.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterName);
            sender.sendMessage(ChatColor.YELLOW + "使用 /czm make create " + monsterName + " 来创建怪物");
            return true;
        }

        // 打开GUI界面
        Player player = (Player) sender;
        idzManager.openGUIEditor(player, monsterId);
        player.sendMessage(ChatColor.GREEN + "正在打开IDZ怪物编辑器...");

        return true;
    }

    /**
     * 处理make list命令
     */
    private boolean handleMakeList(CommandSender sender,
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager) {
        java.util.Set<String> monsterIds = idzManager.getAllMonsterIds();

        if (monsterIds.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "没有找到任何IDZ怪物");
            sender.sendMessage(ChatColor.GRAY + "使用 /czm make create <名称> 来创建新的IDZ怪物");
            return true;
        }

        sender.sendMessage(ChatColor.GREEN + "=== IDZ自定义怪物列表 ===");
        for (String monsterId : monsterIds) {
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config != null) {
                sender.sendMessage(ChatColor.YELLOW + "- " + config.getDisplayName() +
                    ChatColor.GRAY + " (ID: " + monsterId + ")");
            }
        }
        sender.sendMessage(ChatColor.GREEN + "总计: " + monsterIds.size() + " 个IDZ怪物");

        return true;
    }

    /**
     * 处理make delete命令
     */
    private boolean handleMakeDelete(CommandSender sender, String[] args,
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "请指定怪物名称! 用法: /czm make delete <怪物名称>");
            return true;
        }

        String monsterName = args[2];
        String monsterId = generateIDZId(monsterName);

        if (!idzManager.monsterExists(monsterId)) {
            sender.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterName);
            return true;
        }

        // 删除IDZ怪物
        if (idzManager.deleteMonster(monsterId)) {
            sender.sendMessage(ChatColor.GREEN + "成功删除IDZ怪物: " + monsterName + " (ID: " + monsterId + ")");
        } else {
            sender.sendMessage(ChatColor.RED + "删除IDZ怪物失败: " + monsterName);
        }

        return true;
    }

    /**
     * 处理make spawn命令
     */
    private boolean handleMakeSpawn(CommandSender sender, String[] args,
            org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager idzManager) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "此命令只能由玩家执行！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "请指定怪物名称! 用法: /czm make spawn <怪物名称>");
            return true;
        }

        String monsterName = args[2];
        String monsterId = generateIDZId(monsterName);

        if (!idzManager.monsterExists(monsterId)) {
            sender.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterName);
            return true;
        }

        // TODO: 生成IDZ怪物（将在阶段5实现）
        sender.sendMessage(ChatColor.YELLOW + "IDZ怪物生成功能将在后续版本中实现");
        sender.sendMessage(ChatColor.GRAY + "怪物: " + monsterName + " (ID: " + monsterId + ")");

        return true;
    }

    /**
     * 生成IDZ ID
     *
     * @param monsterName 怪物名称
     * @return IDZ ID
     */
    private String generateIDZId(String monsterName) {
        // 简单的ID生成策略：idz + 名称的哈希值
        int hash = Math.abs(monsterName.hashCode());
        return "idz" + (hash % 10000); // 限制在4位数字内
    }

    /**
     * 发送make命令帮助信息
     */
    private void sendMakeHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GREEN + "=== IDZ自定义怪物系统 ===");
        sender.sendMessage(ChatColor.YELLOW + "/czm make create <怪物名称>" + ChatColor.GRAY + " - 创建新的IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm make gui <怪物名称>" + ChatColor.GRAY + " - 打开怪物编辑界面");
        sender.sendMessage(ChatColor.YELLOW + "/czm make list" + ChatColor.GRAY + " - 查看所有IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm make delete <怪物名称>" + ChatColor.GRAY + " - 删除IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm make spawn <怪物名称>" + ChatColor.GRAY + " - 生成IDZ怪物");
    }
}
