package org.Ver_zhzh.deathZombieV4.listeners;

import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.metadata.MetadataValue;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 专门处理游戏实体掉落物的监听器
 * 确保所有游戏相关实体死亡时不会掉落任何物品
 */
public class EntityDropListener implements Listener {

    private final DeathZombieV4 plugin;

    public EntityDropListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 处理实体死亡掉落物事件
     * 优先级设置为HIGHEST，确保在其他监听器之前处理
     *
     * @param event 实体死亡事件
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        // 跳过玩家死亡
        if (entity instanceof Player) {
            return;
        }

        // 检查实体是否属于游戏
        if (isGameEntity(entity)) {
            // 清除所有掉落物
            event.getDrops().clear();
            
            // 清除经验掉落
            event.setDroppedExp(0);
            
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("EntityDropListener: 已清除游戏实体 " + entity.getType() + 
                    " (名称: " + (entity.getCustomName() != null ? entity.getCustomName() : "无") + ") 的所有掉落物");
            }
        }
    }

    /**
     * 检查实体是否属于游戏
     * 使用与ZombieAttackListener相同的逻辑确保一致性
     *
     * @param entity 实体
     * @return 是否是游戏实体
     */
    private boolean isGameEntity(Entity entity) {
        // 跳过玩家
        if (entity instanceof Player) {
            return false;
        }

        // 检查是否是生物实体
        if (!(entity instanceof LivingEntity)) {
            return false;
        }

        // 检查是否有idcZombieEntity标记，如果有则视为游戏实体
        if (entity.hasMetadata("idcZombieEntity")) {
            return true;
        }

        // 检查是否有not_round_zombie标记，如果有则不计入回合生成的僵尸
        if (entity.hasMetadata("not_round_zombie")) {
            return false;
        }

        // 检查是否有游戏标记元数据
        if (entity.hasMetadata("gameEntity")) {
            return true;
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            return true;
        }

        // 检查是否是DeathZombie插件生成的NPC
        if (entity.hasMetadata("DeathZombieNPC")) {
            return true;
        }

        // 检查是否是自定义僵尸类型
        if (entity.hasMetadata("customZombieType")) {
            for (MetadataValue meta : entity.getMetadata("customZombieType")) {
                if (meta.getOwningPlugin().equals(plugin)) {
                    return true;
                }
            }
        }

        // 检查各种变异实体标记
        if (entity.hasMetadata("mutantZombie01") ||
            entity.hasMetadata("isMutantBlaze") ||
            entity.hasMetadata("isMutantCreeper") ||
            entity.hasMetadata("swampBoggedMonster") ||
            entity.hasMetadata("mutantEntity")) {
            return true;
        }

        // 额外检查：实体名称是否有特定前缀或包含特定字符串
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            
            // 检查是否是游戏实体的命名格式
            if (customName.startsWith("游戏_")) {
                return true;
            }

            // 检查是否包含变异、感染等关键词
            if (customName.contains("变异") || 
                customName.contains("感染") ||
                customName.contains("idc") || 
                customName.contains("id")) {
                return true;
            }

            // 检查是否包含zombie或僵尸标识
            if (customName.contains("zombie") || customName.contains("僵尸")) {
                return true;
            }
        }

        // 检查实体类型 - 对于普通僵尸，如果没有特殊标记说明不是游戏实体，则不清除掉落物
        // 这样可以避免影响非游戏相关的僵尸
        
        return false;
    }
}
