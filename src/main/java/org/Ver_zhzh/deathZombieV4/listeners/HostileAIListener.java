package org.Ver_zhzh.deathZombieV4.listeners;

import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityTargetEvent;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.deathZombieV4.utils.HostileAIManager;

import java.util.logging.Logger;

/**
 * 敌对AI监听器
 * 防止变异实体的仇恨被转移到其他生物上
 */
public class HostileAIListener implements Listener {
    
    private final Plugin plugin;
    private final Logger logger;
    private final HostileAIManager hostileAIManager;
    
    public HostileAIListener(Plugin plugin, HostileAIManager hostileAIManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.hostileAIManager = hostileAIManager;
    }
    
    /**
     * 处理实体目标改变事件
     * 防止变异实体攻击其他生物，强制只攻击玩家
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEntityTarget(EntityTargetEvent event) {
        Entity entity = event.getEntity();
        Entity target = event.getTarget();

        // 检查是否是游戏实体（包括有敌对AI的实体和其他游戏实体）
        if (!isGameEntity(entity)) {
            return;
        }

        // 如果目标不是冒险模式的玩家，强制取消
        if (!(target instanceof Player) ||
            (target instanceof Player && ((Player) target).getGameMode() != org.bukkit.GameMode.ADVENTURE)) {

            event.setCancelled(true);

            if (plugin.getConfig().getBoolean("debug", false)) {
                logger.info("阻止游戏实体 " + entity.getType() + " 攻击非冒险模式目标: " +
                    (target != null ? target.getType() : "null"));
            }

            // 延迟重新寻找冒险模式玩家目标
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                if (entity instanceof Mob && entity.isValid() && !entity.isDead()) {
                    Mob mob = (Mob) entity;
                    Player nearestPlayer = findNearestAdventurePlayer(entity);
                    if (nearestPlayer != null) {
                        mob.setTarget(nearestPlayer);
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            logger.info("重新设置游戏实体 " + entity.getType() + " 的目标为玩家 " + nearestPlayer.getName());
                        }
                    }
                }
            }, 2L);
        }
    }
    
    /**
     * 处理实体伤害事件
     * 防止游戏实体因被攻击而转移仇恨到其他生物
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        Entity damager = event.getDamager();
        Entity victim = event.getEntity();

        // 检查是否是游戏实体攻击其他游戏实体（技能误伤）
        if (isGameEntity(damager) && isGameEntity(victim)) {
            // 如果是游戏实体的技能误伤其他游戏实体，取消伤害
            if (isSkillDamage(damager, victim)) {
                event.setCancelled(true);
                if (plugin.getConfig().getBoolean("debug", false)) {
                    logger.info("阻止游戏实体 " + damager.getType() + " 的技能误伤游戏实体 " + victim.getType());
                }
                return;
            }
        }

        // 检查是否是任何实体攻击游戏实体（可能导致仇恨转移）
        if (isGameEntity(victim) && victim instanceof Mob) {
            Mob mob = (Mob) victim;

            // 延迟检查并重置目标，确保游戏实体只攻击玩家
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                if (mob.isValid() && !mob.isDead()) {
                    Entity currentTarget = mob.getTarget();

                    // 如果当前目标不是冒险模式玩家，重新寻找玩家目标
                    if (currentTarget == null ||
                        !(currentTarget instanceof Player) ||
                        (currentTarget instanceof Player && ((Player) currentTarget).getGameMode() != org.bukkit.GameMode.ADVENTURE)) {

                        Player nearestPlayer = findNearestAdventurePlayer(mob);
                        if (nearestPlayer != null) {
                            mob.setTarget(nearestPlayer);
                            if (plugin.getConfig().getBoolean("debug", false)) {
                                logger.info("重置游戏实体 " + mob.getType() + " 的目标为玩家 " + nearestPlayer.getName() + "，防止仇恨转移");
                            }
                        } else {
                            mob.setTarget(null);
                        }
                    }
                }
            }, 1L);

            // 额外的延迟检查，确保目标稳定
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                if (mob.isValid() && !mob.isDead()) {
                    Entity finalTarget = mob.getTarget();
                    if (finalTarget != null &&
                        (!(finalTarget instanceof Player) ||
                         (finalTarget instanceof Player && ((Player) finalTarget).getGameMode() != org.bukkit.GameMode.ADVENTURE))) {

                        Player nearestPlayer = findNearestAdventurePlayer(mob);
                        if (nearestPlayer != null) {
                            mob.setTarget(nearestPlayer);
                        } else {
                            mob.setTarget(null);
                        }
                    }
                }
            }, 5L);
        }
    }
    
    /**
     * 检查实体是否是有敌对AI的实体
     */
    private boolean isHostileAIEntity(Entity entity) {
        if (entity == null) {
            return false;
        }
        
        // 检查是否有敌对AI标记
        if (entity.hasMetadata("hostileAI")) {
            return true;
        }
        
        // 检查是否是变异实体
        if (entity.hasMetadata("idcZombieEntity") || 
            entity.hasMetadata("mutantIronGolem") ||
            entity.hasMetadata("mutationKing") ||
            entity.hasMetadata("soulGuardian") ||
            entity.hasMetadata("mutantZombie04")) {
            return true;
        }
        
        // 检查实体名称
        if (entity.getCustomName() != null) {
            String name = entity.getCustomName();
            if (name.contains("变异") || name.contains("idc") || 
                name.contains("异变") || name.contains("灵魂")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查实体是否是游戏实体
     */
    private boolean isGameEntity(Entity entity) {
        if (entity == null) {
            return false;
        }
        
        // 跳过玩家
        if (entity instanceof Player) {
            return false;
        }
        
        // 检查是否是生物实体
        if (!(entity instanceof LivingEntity)) {
            return false;
        }
        
        // 检查各种游戏实体标记
        if (entity.hasMetadata("idcZombieEntity") ||
            entity.hasMetadata("gameEntity") ||
            entity.hasMetadata("customZombieType") ||
            entity.hasMetadata("NPC")) {
            return true;
        }
        
        // 检查实体名称
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            if (customName.startsWith("游戏_") ||
                customName.contains("僵尸") ||
                customName.contains("zombie") ||
                customName.contains("id") ||
                customName.contains("idc")) {
                return true;
            }
        }
        
        // 检查实体类型
        if (entity instanceof Zombie) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否是技能伤害（而不是直接攻击）
     */
    private boolean isSkillDamage(Entity damager, Entity victim) {
        // 检查距离，如果距离较远可能是技能伤害
        double distance = damager.getLocation().distance(victim.getLocation());
        
        // 如果距离超过5格，很可能是技能伤害
        if (distance > 5.0) {
            return true;
        }
        
        // 检查是否是特定的变异实体（它们有远程技能）
        if (damager.hasMetadata("mutantIronGolem") ||
            damager.hasMetadata("mutantZombie04") ||
            damager.hasMetadata("mutationKing")) {
            return true;
        }
        
        // 检查实体名称
        if (damager.getCustomName() != null) {
            String name = damager.getCustomName();
            if (name.contains("变异铁傀儡") || 
                name.contains("变异僵尸04") ||
                name.contains("异变之王")) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 处理实体被攻击事件
     * 确保游戏实体被攻击后只会反击玩家
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityAttacked(EntityDamageByEntityEvent event) {
        Entity victim = event.getEntity();
        Entity attacker = event.getDamager();

        // 只处理游戏实体被攻击的情况
        if (!isGameEntity(victim) || !(victim instanceof Mob)) {
            return;
        }

        Mob mob = (Mob) victim;

        // 延迟处理，确保在原生反击逻辑之后执行
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            if (mob.isValid() && !mob.isDead()) {
                Entity currentTarget = mob.getTarget();

                // 如果当前目标不是冒险模式玩家，重新寻找玩家目标或清除目标
                if (currentTarget == null ||
                    !(currentTarget instanceof Player) ||
                    (currentTarget instanceof Player && ((Player) currentTarget).getGameMode() != org.bukkit.GameMode.ADVENTURE)) {

                    Player nearestPlayer = findNearestAdventurePlayer(mob);
                    if (nearestPlayer != null) {
                        mob.setTarget(nearestPlayer);
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            logger.info("游戏实体 " + mob.getType() + " 被攻击后重新设置目标为玩家 " + nearestPlayer.getName());
                        }
                    } else {
                        mob.setTarget(null);
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            logger.info("游戏实体 " + mob.getType() + " 被攻击后清除目标（无冒险模式玩家）");
                        }
                    }
                }
            }
        }, 2L);
    }

    /**
     * 查找最近的冒险模式玩家
     *
     * @param entity 实体
     * @return 最近的冒险模式玩家，如果没有找到返回null
     */
    private Player findNearestAdventurePlayer(Entity entity) {
        Player nearestPlayer = null;
        double nearestDistance = 30.0; // 搜索范围

        for (Entity nearby : entity.getNearbyEntities(30.0, 30.0, 30.0)) {
            if (nearby instanceof Player) {
                Player player = (Player) nearby;

                // 只考虑冒险模式的玩家
                if (player.getGameMode() != org.bukkit.GameMode.ADVENTURE) {
                    continue;
                }

                double distance = entity.getLocation().distance(player.getLocation());
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
        }

        return nearestPlayer;
    }
}
