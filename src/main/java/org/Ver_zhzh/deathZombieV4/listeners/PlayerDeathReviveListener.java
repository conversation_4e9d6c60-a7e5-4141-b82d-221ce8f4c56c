package org.Ver_zhzh.deathZombieV4.listeners;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.EulerAngle;

/**
 * 处理玩家死亡和复活逻辑的监听器
 */
public class PlayerDeathReviveListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;
    private final GameSessionManager gameSessionManager;

    // 存储玩家灵魂NPC: <玩家UUID, NPC UUID>
    private final Map<UUID, UUID> playerSoulNPCs = new HashMap<>();

    // 存储玩家灵魂全息图: <玩家UUID, 全息图UUID>
    private final Map<UUID, UUID> playerSoulHolograms = new HashMap<>();

    // 存储正在救援的玩家: <被救援玩家UUID, 救援玩家UUID>
    private final Map<UUID, UUID> playerBeingRevived = new HashMap<>();

    // 存储救援进度: <被救援玩家UUID, 当前救援时间(秒)>
    private final Map<UUID, Integer> reviveProgress = new HashMap<>();

    // 存储玩家死亡任务: <玩家UUID, 任务>
    private final Map<UUID, BukkitTask> playerDeathTasks = new HashMap<>();

    // 存储玩家救援任务: <救援玩家UUID, 任务>
    private final Map<UUID, BukkitTask> playerReviveTasks = new HashMap<>();

    // 存储NPC点击次数: <NPC UUID, 点击次数>
    private final Map<UUID, Integer> npcReviveClicks = new HashMap<>();

    // 存储玩家状态: <玩家UUID, 状态(downed/dead/alive)>
    private final Map<UUID, String> playerStatus = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public PlayerDeathReviveListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameSessionManager = plugin.getGameSessionManager();
    }

    /**
     * 从配置文件获取救援范围
     */
    private double getRescueRange() {
        return plugin.getConfig().getDouble("rescue_system.rescue_range", 2.0);
    }

    /**
     * 从配置文件获取救援所需时间
     */
    private int getRescueTimeRequired() {
        return plugin.getConfig().getInt("rescue_system.rescue_time_required", 5);
    }

    /**
     * 从配置文件获取最大救援等待时间
     */
    private int getMaxRescueWaitTime() {
        return plugin.getConfig().getInt("rescue_system.max_rescue_wait_time", 30);
    }

    /**
     * 替换消息中的变量
     */
    private String replaceVariables(String message, Map<String, String> variables) {
        String result = message;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            result = result.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return ChatColor.translateAlternateColorCodes('&', result);
    }

    /**
     * 发送可配置的Title
     */
    private void sendConfigurableTitle(Player player, String configPath, Map<String, String> variables) {
        try {
            String main = plugin.getConfig().getString(configPath + ".main", "");
            String subtitle = plugin.getConfig().getString(configPath + ".subtitle", "");
            int fadeIn = plugin.getConfig().getInt(configPath + ".fade_in", 10);
            int stay = plugin.getConfig().getInt(configPath + ".stay", 70);
            int fadeOut = plugin.getConfig().getInt(configPath + ".fade_out", 20);

            if (!main.isEmpty() || !subtitle.isEmpty()) {
                player.sendTitle(
                        replaceVariables(main, variables),
                        replaceVariables(subtitle, variables),
                        fadeIn, stay, fadeOut
                );
            }
        } catch (Exception e) {
            plugin.getLogger().warning("发送Title失败: " + e.getMessage());
        }
    }

    /**
     * 发送可配置的消息列表
     */
    private void sendConfigurableMessages(Player player, String configPath, Map<String, String> variables) {
        List<String> messages = plugin.getConfig().getStringList(configPath);
        for (String message : messages) {
            player.sendMessage(replaceVariables(message, variables));
        }
    }

    /**
     * 获取玩家当前状态
     *
     * @param playerUUID 玩家UUID
     * @return 玩家状态字符串，如果没有状态则返回null
     */
    public String getPlayerStatus(UUID playerUUID) {
        return playerStatus.get(playerUUID);
    }

    /**
     * 处理玩家死亡事件（无论是被僵尸击杀还是其他原因）
     *
     * @param player 死亡的玩家
     * @param killer 击杀者实体（可能为null，如环境伤害）
     */
    public void handlePlayerKilledByZombie(Player player, Entity killer) {
        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查游戏是否正在运行
        if (gameSessionManager.getGameState(gameName) != GameState.RUNNING) {
            return;
        }

        // 检查玩家当前状态，防止重复处理
        String currentStatus = playerStatus.get(player.getUniqueId());
        if ("downed".equals(currentStatus) || "dead".equals(currentStatus)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 已处于 " + currentStatus + " 状态，跳过重复处理");
            return;
        }

        // 将玩家设置为旁观者模式
        player.setGameMode(GameMode.SPECTATOR);

        // 设置玩家状态为倒下（同时存储到两个地方以确保兼容性）
        playerStatus.put(player.getUniqueId(), "downed");
        playerManager.setPlayerData(player, "status", "downed");

        // 发送死亡Title
        if (killer != null && killer instanceof Player) {
            // 如果是被玩家击杀
            Player killerPlayer = (Player) killer;
            sendDeathTitle(player, "被 " + killerPlayer.getName() + " 击杀");
        } else if (killer != null && isGameEntity(killer)) {
            // 如果是被游戏实体（僵尸/变异生物）击杀
            sendDeathTitle(player, "被僵尸击杀");
        } else {
            // 其他死亡原因
            sendDeathTitle(player, "死亡");
        }

        // 创建灵魂NPC
        createSoulNPC(player);

        // 设置死亡计时器，如果指定时间内没有被复活，则永久死亡
        startDeathTimer(player);

        // 检查是否所有玩家都死亡
        checkAllPlayersDead(gameName);
    }

    /**
     * 发送死亡Title给玩家
     *
     * @param player 玩家
     * @param deathReason 死亡原因描述
     */
    private void sendDeathTitle(Player player, String deathReason) {
        Map<String, String> variables = new HashMap<>();
        variables.put("remaining_time", String.valueOf(getMaxRescueWaitTime()));
        variables.put("rescue_time", String.valueOf(getRescueTimeRequired()));

        // 发送倒下玩家的Title
        sendConfigurableTitle(player, "rescue_system.downed_display.player_title", variables);

        // 发送倒下玩家的消息
        sendConfigurableMessages(player, "rescue_system.downed_display.player_messages", variables);
    }

    /**
     * 创建玩家灵魂NPC（倒地形式）
     *
     * @param player 玩家
     */
    private void createSoulNPC(Player player) {
        try {
            Location location = player.getLocation();

            // 直接使用盔甲架代替NPC，确保显示正确的倒下姿势
            createFallbackSoulNPC(player, location);

            // 获取玩家所在的游戏
            String gameName = gameSessionManager.getPlayerGame(player);
            if (gameName != null) {
                Map<String, String> variables = new HashMap<>();
                variables.put("player_name", player.getName());
                variables.put("rescue_time", String.valueOf(getRescueTimeRequired()));

                // 向所有游戏中的玩家发送Title通知和消息
                for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                    Player p = Bukkit.getPlayer(playerId);
                    if (p != null && p.isOnline() && !p.equals(player)) {
                        // 发送队友Title
                        sendConfigurableTitle(p, "rescue_system.downed_display.team_title", variables);
                        // 发送队友消息
                        sendConfigurableMessages(p, "rescue_system.downed_display.team_messages", variables);
                    }
                }

                // 注意：不重复设置玩家状态，因为在handlePlayerKilledByZombie方法中已经设置了
                // playerManager.setPlayerData(player, "status", "downed"); // 已在第188行设置

                // 更新计分板
                plugin.getScoreboardManager().updatePlayerScoreboard(player);

                // 为所有玩家更新计分板，显示灵魂状态
                for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                    Player p = Bukkit.getPlayer(playerId);
                    if (p != null && p.isOnline()) {
                        plugin.getScoreboardManager().updatePlayerScoreboard(p);
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("为玩家 " + player.getName() + " 创建倒地灵魂时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 开始死亡计时器
     *
     * @param player 玩家
     */
    private void startDeathTimer(Player player) {
        // 取消之前的任务（如果有）
        if (playerDeathTasks.containsKey(player.getUniqueId())) {
            playerDeathTasks.get(player.getUniqueId()).cancel();
        }

        // 创建新任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 只有在玩家还是倒下状态时才处理永久死亡
                if (player.isOnline() && player.getGameMode() == GameMode.SPECTATOR
                    && "downed".equals(playerStatus.get(player.getUniqueId()))) {

                    // 设置玩家状态为永久死亡（同时存储到两个地方以确保兼容性）
                    playerStatus.put(player.getUniqueId(), "dead");
                    playerManager.setPlayerData(player, "status", "dead");

                    // 移除灵魂NPC
                    removeSoulNPC(player.getUniqueId());

                    // 发送永久死亡消息
                    String timeoutMessage = plugin.getConfig().getString("rescue_system.rescue_failure.target_messages.timeout", "&c你没有被及时救援，已永久死亡！");
                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', timeoutMessage));

                    // 设置玩家临时数据，标记为已死亡状态
                    String gameName = gameSessionManager.getPlayerGame(player);
                    if (gameName != null) {
                        playerManager.setPlayerData(player, "status", "dead");

                        // 向所有游戏中的玩家发送死亡通知
                        for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                            Player p = Bukkit.getPlayer(playerId);
                            if (p != null && p.isOnline()) {
                                try {
                                    p.sendTitle(
                                            ChatColor.DARK_RED + player.getName() + " 已死亡",
                                            ChatColor.RED + "无法被救援",
                                            10, 70, 20
                                    );
                                } catch (Exception e) {
                                    plugin.getLogger().warning("向玩家 " + p.getName() + " 发送死亡通知Title失败: " + e.getMessage());
                                }
                            }
                        }

                        // 更新所有玩家的计分板
                        for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                            Player p = Bukkit.getPlayer(playerId);
                            if (p != null && p.isOnline()) {
                                plugin.getScoreboardManager().updatePlayerScoreboard(p);
                            }
                        }

                        // 检查是否所有玩家都死亡
                        checkAllPlayersDead(gameName);
                    }
                }

                // 移除任务引用
                playerDeathTasks.remove(player.getUniqueId());
            }
        }.runTaskLater(plugin, getMaxRescueWaitTime() * 20L); // 转换为tick

        // 存储任务引用
        playerDeathTasks.put(player.getUniqueId(), task);
    }

    /**
     * 移除灵魂NPC
     *
     * @param playerUUID 玩家UUID
     */
    private void removeSoulNPC(UUID playerUUID) {
        if (playerSoulNPCs.containsKey(playerUUID)) {
            UUID npcUUID = playerSoulNPCs.get(playerUUID);
            Entity npc = Bukkit.getEntity(npcUUID);

            if (npc != null) {
                // 检查是否是玩家实体，避免使用remove()方法
                if (npc instanceof Player) {
                    plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + npc.getName());
                } else {
                    npc.remove();
                }
            }

            playerSoulNPCs.remove(playerUUID);
            npcReviveClicks.remove(npcUUID);
        }
    }

    /**
     * 复活玩家
     *
     * @param playerUUID 玩家UUID
     * @param npcLocation NPC位置
     */
    private void revivePlayer(UUID playerUUID, Location npcLocation) {
        Player player = Bukkit.getPlayer(playerUUID);

        if (player != null && player.isOnline()) {
            // 取消死亡计时器
            if (playerDeathTasks.containsKey(playerUUID)) {
                playerDeathTasks.get(playerUUID).cancel();
                playerDeathTasks.remove(playerUUID);
            }

            // 设置玩家为冒险模式
            player.setGameMode(GameMode.ADVENTURE);

            // 传送玩家到NPC位置
            player.teleport(npcLocation);

            // 恢复玩家生命值
            player.setHealth(player.getMaxHealth());

            // 添加发光效果
            player.setGlowing(true);

            // 清除玩家的倒下状态（同时清除两个地方的状态数据）
            playerManager.removePlayerData(player, "status");
            playerStatus.put(playerUUID, "alive");

            // 获取救援者信息
            UUID rescuerUUID = playerBeingRevived.get(playerUUID);
            String rescuerName = rescuerUUID != null ? Bukkit.getOfflinePlayer(rescuerUUID).getName() : "队友";

            // 记录救援统计数据
            if (rescuerUUID != null) {
                Player rescuer = Bukkit.getPlayer(rescuerUUID);
                if (rescuer != null) {
                    plugin.getPlayerStatisticsManager().addTeammateRescues(rescuer, 1);
                    plugin.getLogger().info("已为救援者 " + rescuer.getName() + " 记录救援统计");
                }
            }

            // 发送复活消息和Title
            Map<String, String> variables = new HashMap<>();
            variables.put("rescuer_name", rescuerName);
            variables.put("target_name", player.getName());

            sendConfigurableMessages(player, "rescue_system.rescue_success.target_messages", variables);
            sendConfigurableTitle(player, "rescue_system.rescue_success.target_title", variables);

            // 广播复活消息
            String gameName = gameSessionManager.getPlayerGame(player);
            if (gameName != null) {
                for (UUID pid : gameSessionManager.getGameParticipants(gameName)) {
                    Player p = Bukkit.getPlayer(pid);
                    if (p != null && p.isOnline() && !p.equals(player)) {
                        p.sendMessage(ChatColor.GREEN + player.getName() + " 已被复活！");
                    }
                }

                // 更新计分板
                plugin.getScoreboardManager().updatePlayerScoreboard(player);

                // 为所有玩家更新计分板
                for (UUID pid : gameSessionManager.getGameParticipants(gameName)) {
                    Player p = Bukkit.getPlayer(pid);
                    if (p != null && p.isOnline()) {
                        plugin.getScoreboardManager().updatePlayerScoreboard(p);
                    }
                }
            }
        }

        // 清理救援悬浮文字
        if (playerSoulHolograms.containsKey(playerUUID)) {
            UUID holoUUID = playerSoulHolograms.get(playerUUID);

            // 如果使用DecentHolograms
            if (plugin.isDecentHologramsAvailable()) {
                try {
                    // 尝试将UUID转换为字符串作为悬浮文字ID
                    String holoId = holoUUID.toString();
                    eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                    if (hologram != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(holoId);
                        plugin.getLogger().info("已清理玩家 " + playerUUID + " 的救援悬浮文字");
                    }
                } catch (Exception e) {
                    // 如果不是DecentHolograms的悬浮文字，尝试常规方式移除
                    Entity holo = Bukkit.getEntity(holoUUID);
                    if (holo != null) {
                        // 检查是否是玩家实体，避免使用remove()方法
                        if (holo instanceof Player) {
                            plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                        } else {
                            holo.remove();
                            plugin.getLogger().info("已清理玩家 " + playerUUID + " 的救援悬浮文字");
                        }
                    }
                }
            } else {
                // 使用传统方式清理悬浮文字
                Entity holo = Bukkit.getEntity(holoUUID);
                if (holo != null) {
                    // 检查是否是玩家实体，避免使用remove()方法
                    if (holo instanceof Player) {
                        plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                    } else {
                        holo.remove();
                        plugin.getLogger().info("已清理玩家 " + playerUUID + " 的救援悬浮文字");
                    }
                }
            }

            // 从映射中移除
            playerSoulHolograms.remove(playerUUID);
        }

        // 移除灵魂NPC
        removeSoulNPC(playerUUID);

        // 清理救援相关数据
        playerBeingRevived.remove(playerUUID);
        reviveProgress.remove(playerUUID);
    }

    /**
     * 检查是否所有玩家都死亡
     *
     * @param gameName 游戏名称
     */
    private void checkAllPlayersDead(String gameName) {
        boolean allDead = true;

        for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
            Player player = Bukkit.getPlayer(playerId);

            // 如果有任何一个玩家不是旁观者模式，说明还有活着的玩家
            if (player != null && player.isOnline() && player.getGameMode() != GameMode.SPECTATOR) {
                allDead = false;
                break;
            }
        }

        // 如果所有玩家都死亡，结束游戏
        if (allDead) {
            // 广播游戏结束消息
            for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    player.sendMessage(ChatColor.RED + "所有玩家都已死亡，游戏结束！");

                    try {
                        // 获取游戏耗时和坚持回合数
                        String gameDuration = plugin.getZombieSpawnManager().getGameDurationString(gameName);
                        int survivedRounds = plugin.getZombieSpawnManager().getCurrentRound(gameName);

                        // 使用MessageManager发送游戏失败Title
                        plugin.getMessageManager().sendGameEndTitleMessage(player, "defeat", gameName, gameDuration, survivedRounds);
                    } catch (Exception e) {
                        plugin.getLogger().warning("发送游戏结束Title失败: " + e.getMessage());
                    }
                }
            }

            // 结束游戏
            plugin.getZombieSpawnManager().endGameAndCleanup(gameName);
        }
    }

    /**
     * 处理玩家与NPC交互事件
     *
     * @param event 玩家交互实体事件
     */
    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Entity entity = event.getRightClicked();

        // 检查是否是灵魂NPC
        if (entity.hasMetadata("soulNPC")) {
            event.setCancelled(true);

            Player player = event.getPlayer();
            player.sendMessage(ChatColor.YELLOW + "请蹲下" + getRescueTimeRequired() + "秒来救援倒地的队友！");
        }
    }

    /**
     * 创建备用灵魂NPC（使用盾牌座）
     *
     * @param player 玩家
     * @param location 位置
     */
    private void createFallbackSoulNPC(Player player, Location location) {
        try {
            // 确保位置有效
            if (location == null || location.getWorld() == null) {
                plugin.getLogger().severe("创建盔甲架失败: 位置无效");
                return;
            }

            plugin.getLogger().info("开始为玩家 " + player.getName() + " 创建盔甲架灵魂，位置: " + location);

            // 创建盾牌座作为倒地的玩家
            ArmorStand armorStand = location.getWorld().spawn(location, ArmorStand.class);

            if (armorStand == null) {
                plugin.getLogger().severe("创建盔甲架失败: spawn方法返回null");
                return;
            }

            // 设置盾牌座属性
            armorStand.setVisible(true);
            armorStand.setCustomName(ChatColor.RED + player.getName() + "的灵魂");
            armorStand.setCustomNameVisible(true);
            armorStand.setGravity(false);
            armorStand.setInvulnerable(true);
            armorStand.setBasePlate(false);
            armorStand.setArms(true);
            armorStand.setSmall(false);

            // 给盾牌座装备史蒂夫头颅和皮革套装
            try {
                // 装备史蒂夫头颅
                ItemStack head = new ItemStack(Material.PLAYER_HEAD);
                SkullMeta skullMeta = (SkullMeta) head.getItemMeta();
                if (skullMeta != null) {
                    skullMeta.setOwningPlayer(player);
                    head.setItemMeta(skullMeta);
                }
                armorStand.getEquipment().setHelmet(head);

                // 装备皮革胸甲
                ItemStack chest = new ItemStack(Material.LEATHER_CHESTPLATE);
                armorStand.getEquipment().setChestplate(chest);

                // 装备皮革护腿
                ItemStack legs = new ItemStack(Material.LEATHER_LEGGINGS);
                armorStand.getEquipment().setLeggings(legs);

                // 装备皮革靴子
                ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
                armorStand.getEquipment().setBoots(boots);

                plugin.getLogger().info("已为盔甲架设置装备");
            } catch (Exception e) {
                plugin.getLogger().warning("为盾牌座装备物品时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 设置盾牌座姿势（倒地姿势 - 更加明显的倒下姿势）
            try {
                // 头部朝下
                armorStand.setHeadPose(new EulerAngle(Math.toRadians(90), 0, 0));
                // 身体平躺
                armorStand.setBodyPose(new EulerAngle(Math.toRadians(90), 0, 0));
                // 手臂伸展
                armorStand.setLeftArmPose(new EulerAngle(Math.toRadians(190), Math.toRadians(20), Math.toRadians(10)));
                armorStand.setRightArmPose(new EulerAngle(Math.toRadians(190), Math.toRadians(-20), Math.toRadians(-10)));
                // 腿部伸展
                armorStand.setLeftLegPose(new EulerAngle(Math.toRadians(190), Math.toRadians(10), Math.toRadians(10)));
                armorStand.setRightLegPose(new EulerAngle(Math.toRadians(190), Math.toRadians(-10), Math.toRadians(-10)));

                // 调整位置，使盔甲架看起来像是倒在地上
                armorStand.teleport(location.clone().add(0, -0.7, 0));

                plugin.getLogger().info("已为盔甲架设置倒下姿势");
            } catch (Exception e) {
                plugin.getLogger().warning("设置盔甲架姿势时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 添加发光效果
            armorStand.setGlowing(true);

            // 设置元数据，标识这是一个灵魂NPC
            armorStand.setMetadata("soulNPC", new FixedMetadataValue(plugin, true));
            armorStand.setMetadata("playerUUID", new FixedMetadataValue(plugin, player.getUniqueId().toString()));

            // 存储NPC引用
            playerSoulNPCs.put(player.getUniqueId(), armorStand.getUniqueId());

            plugin.getLogger().info("已为玩家 " + player.getName() + " 创建倒地灵魂（盾牌座），UUID: " + armorStand.getUniqueId());

            // 创建救援全息图
            createReviveHologram(player, armorStand.getLocation().add(0, 1.5, 0), getMaxRescueWaitTime());

            // 注意：不在这里发送广播消息，因为在createSoulNPC方法中已经通过配置文件发送了消息
            // 避免重复显示倒下信息

            // 更新计分板
            plugin.getScoreboardManager().updatePlayerScoreboard(player);

            // 为所有玩家更新计分板，显示灵魂状态
            String currentGameName = gameSessionManager.getPlayerGame(player);
            if (currentGameName != null) {
                for (UUID playerId : gameSessionManager.getGameParticipants(currentGameName)) {
                    Player p = Bukkit.getPlayer(playerId);
                    if (p != null && p.isOnline()) {
                        plugin.getScoreboardManager().updatePlayerScoreboard(p);
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("使用盾牌座创建灵魂失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建救援全息图
     *
     * @param player 玩家
     * @param location 位置
     * @param timeLimit 时间限制
     */
    private void createReviveHologram(Player player, Location location, int timeLimit) {
        plugin.getLogger().info("开始为玩家 " + player.getName() + " 创建救援悬浮文字");

        // 先清理玩家现有的悬浮文字，避免重复创建
        if (playerSoulHolograms.containsKey(player.getUniqueId())) {
            UUID existingHoloUUID = playerSoulHolograms.get(player.getUniqueId());
            plugin.getLogger().info("发现玩家 " + player.getName() + " 已有悬浮文字，UUID: " + existingHoloUUID);

            // 如果使用DecentHolograms
            if (plugin.isDecentHologramsAvailable()) {
                try {
                    String existingHoloId = existingHoloUUID.toString();
                    plugin.getLogger().info("尝试清理DecentHolograms悬浮文字，ID: " + existingHoloId);
                    eu.decentsoftware.holograms.api.holograms.Hologram existingHologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(existingHoloId);
                    if (existingHologram != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(existingHoloId);
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的现有DecentHolograms悬浮文字");
                    } else {
                        plugin.getLogger().warning("未找到玩家 " + player.getName() + " 的DecentHolograms悬浮文字，ID: " + existingHoloId);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("清理DecentHolograms悬浮文字失败: " + e.getMessage());
                    // 如果不是DecentHolograms的悬浮文字，尝试常规方式移除
                    Entity existingHolo = Bukkit.getEntity(existingHoloUUID);
                    if (existingHolo != null) {
                        if (existingHolo instanceof Player) {
                            plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + existingHolo.getName());
                        } else {
                            existingHolo.remove();
                            plugin.getLogger().info("已清理玩家 " + player.getName() + " 的现有盔甲架悬浮文字");
                        }
                    } else {
                        plugin.getLogger().warning("未找到玩家 " + player.getName() + " 的盔甲架悬浮文字，UUID: " + existingHoloUUID);
                    }
                }
            } else {
                // 使用传统方式清理悬浮文字
                plugin.getLogger().info("尝试清理盔甲架悬浮文字，UUID: " + existingHoloUUID);
                Entity existingHolo = Bukkit.getEntity(existingHoloUUID);
                if (existingHolo != null) {
                    if (existingHolo instanceof Player) {
                        plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + existingHolo.getName());
                    } else {
                        existingHolo.remove();
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的现有盔甲架悬浮文字");
                    }
                } else {
                    plugin.getLogger().warning("未找到玩家 " + player.getName() + " 的盔甲架悬浮文字，UUID: " + existingHoloUUID);
                }
            }

            // 无论清理是否成功，都从映射中移除旧的引用
            playerSoulHolograms.remove(player.getUniqueId());
        }

        // 检查是否可以使用DecentHolograms插件
        if (plugin.isDecentHologramsAvailable()) {
            try {
                // 生成唯一ID - 使用玩家名称和随机数以避免冲突
                String holoId = "soul_" + player.getName() + "_" + System.currentTimeMillis() % 10000;
                plugin.getLogger().info("尝试使用DecentHolograms创建悬浮文字，ID: " + holoId);

                // 先检查是否已存在同ID的悬浮文字，如果有则移除
                eu.decentsoftware.holograms.api.holograms.Hologram existingHologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                if (existingHologram != null) {
                    plugin.getLogger().warning("发现已存在同ID的悬浮文字，先移除它");
                    eu.decentsoftware.holograms.api.DHAPI.removeHologram(holoId);
                }

                // 创建悬浮文字
                List<String> lines = new ArrayList<>();
                lines.add(ChatColor.YELLOW + "救援剩余时间: " + timeLimit + "秒");

                // 使用DecentHolograms API创建悬浮文字
                eu.decentsoftware.holograms.api.DHAPI.createHologram(holoId, location, lines);
                plugin.getLogger().info("成功创建DecentHolograms悬浮文字，ID: " + holoId);

                // 存储悬浮文字ID到玩家UUID的映射
                playerSoulHolograms.put(player.getUniqueId(), UUID.randomUUID()); // 使用随机UUID作为映射键

                // 创建定时更新悬浮文字的任务
                new BukkitRunnable() {
                    private int remainingTime = timeLimit;

                    @Override
                    public void run() {
                        // 检查玩家是否已经复活或者时间已到
                        Player p = Bukkit.getPlayer(player.getUniqueId());
                        if ((p != null && p.isOnline() && p.getGameMode() != GameMode.SPECTATOR) || remainingTime <= 0) {
                            try {
                                // 删除悬浮文字
                                eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                                if (hologram != null) {
                                    eu.decentsoftware.holograms.api.DHAPI.removeHologram(holoId);
                                    plugin.getLogger().info("已删除玩家 " + player.getName() + " 的DecentHolograms悬浮文字，原因：玩家已复活或时间已到");
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("删除救援悬浮文字失败: " + e.getMessage());
                            }
                            cancel();
                            return;
                        }

                        // 检查是否有玩家正在救援
                        UUID rescuerUUID = playerBeingRevived.get(player.getUniqueId());
                        if (rescuerUUID != null) {
                            // 有玩家正在救援，显示救援进度
                            int progress = reviveProgress.getOrDefault(player.getUniqueId(), 0);
                            try {
                                List<String> updatedLines = new ArrayList<>();
                                updatedLines.add(ChatColor.GREEN + "正在救援: " + progress + "/" + getRescueTimeRequired() + "秒");
                                eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                                if (hologram != null) {
                                    eu.decentsoftware.holograms.api.DHAPI.setHologramLines(hologram, updatedLines);
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("更新救援悬浮文字失败: " + e.getMessage());
                            }
                        } else {
                            // 没有玩家救援，显示剩余时间
                            try {
                                List<String> updatedLines = new ArrayList<>();
                                updatedLines.add(ChatColor.YELLOW + "救援剩余时间: " + remainingTime + "秒");
                                eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                                if (hologram != null) {
                                    eu.decentsoftware.holograms.api.DHAPI.setHologramLines(hologram, updatedLines);
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("更新救援悬浮文字失败: " + e.getMessage());
                            }
                            remainingTime--;
                        }
                    }
                }.runTaskTimer(plugin, 0L, 20L); // 每秒更新一次

                return;
            } catch (Exception e) {
                plugin.getLogger().warning("使用DecentHolograms创建救援悬浮文字失败，将使用盔甲架代替: " + e.getMessage());
                e.printStackTrace();
                // 如果DecentHolograms创建失败，回退到使用盔甲架
            }
        }

        // 如果DecentHolograms不可用或创建失败，不创建备用悬浮文字
        // 盔甲架本身已经有自己的名称显示，不需要额外的悬浮文字
        plugin.getLogger().info("不创建备用悬浮文字，使用盔甲架自带的名称显示");
    }

    /**
     * 处理玩家蹲下事件
     *
     * @param event 玩家蹲下事件
     */
    @EventHandler
    public void onPlayerToggleSneak(PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();

        // 只处理蹲下开始的事件
        if (!event.isSneaking()) {
            return;
        }

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查游戏是否正在运行
        if (gameSessionManager.getGameState(gameName) != GameState.RUNNING) {
            return;
        }

        // 寻找附近的灵魂NPC（使用配置化的救援范围）
        double rescueRange = getRescueRange();
        for (Entity entity : player.getNearbyEntities(rescueRange, rescueRange, rescueRange)) {
            if (entity.hasMetadata("soulNPC")) {
                String playerUUIDString = entity.getMetadata("playerUUID").get(0).asString();
                UUID playerUUID = UUID.fromString(playerUUIDString);

                // 检查是否是自己的灵魂
                if (player.getUniqueId().equals(playerUUID)) {
                    player.sendMessage(ChatColor.RED + "你不能救援自己，需要其他玩家的帮助！");
                    return;
                }

                // 检查是否在同一个游戏中
                Player deadPlayer = Bukkit.getPlayer(playerUUID);
                if (deadPlayer == null || !gameName.equals(gameSessionManager.getPlayerGame(deadPlayer))) {
                    player.sendMessage(ChatColor.RED + "你不能救援不在同一个游戏中的玩家！");
                    return;
                }

                // 检查是否已经有人在救援
                if (playerBeingRevived.containsKey(playerUUID) && !player.getUniqueId().equals(playerBeingRevived.get(playerUUID))) {
                    player.sendMessage(ChatColor.YELLOW + "已经有其他玩家正在救援该玩家！");
                    return;
                }

                // 开始救援过程
                startReviveProcess(player, playerUUID, entity);
                return;
            }
        }
    }

    /**
     * 开始救援过程
     *
     * @param rescuer 救援者
     * @param deadPlayerUUID 死亡玩家UUID
     * @param soulEntity 灵魂实体
     */
    private void startReviveProcess(Player rescuer, UUID deadPlayerUUID, Entity soulEntity) {
        // 记录救援关系
        playerBeingRevived.put(deadPlayerUUID, rescuer.getUniqueId());
        reviveProgress.put(deadPlayerUUID, 0);

        // 准备变量
        Map<String, String> variables = new HashMap<>();
        variables.put("rescuer_name", rescuer.getName());
        variables.put("target_name", Bukkit.getOfflinePlayer(deadPlayerUUID).getName());
        variables.put("total", String.valueOf(getRescueTimeRequired()));
        variables.put("progress", "0");

        // 发送救援者Title
        sendConfigurableTitle(rescuer, "rescue_system.rescue_process.rescuer_title", variables);

        // 通知死亡玩家
        Player deadPlayer = Bukkit.getPlayer(deadPlayerUUID);
        if (deadPlayer != null && deadPlayer.isOnline()) {
            sendConfigurableTitle(deadPlayer, "rescue_system.rescue_process.target_title", variables);
        }

        // 创建救援任务
        BukkitTask task = new BukkitRunnable() {
            private int progress = 0;

            @Override
            public void run() {
                // 检查任务是否已被取消
                if (isCancelled()) {
                    return;
                }

                // 检查救援关系是否还存在（防止重复执行）
                if (!playerBeingRevived.containsKey(deadPlayerUUID) ||
                    !rescuer.getUniqueId().equals(playerBeingRevived.get(deadPlayerUUID))) {
                    plugin.getLogger().info("救援关系已不存在，停止救援任务");
                    cancel();
                    return;
                }

                // 检查救援者是否在线且仍在蹲下
                if (!rescuer.isOnline() || !rescuer.isSneaking()) {
                    String message = plugin.getConfig().getString("rescue_system.rescue_failure.rescuer_messages.stopped_crouching", "&c救援失败，你停止了蹲下");
                    cancelRevive(deadPlayerUUID, rescuer.getUniqueId(), ChatColor.translateAlternateColorCodes('&', message));
                    cancel(); // 立即停止当前任务
                    return;
                }

                // 检查救援者是否还在灵魂附近（使用配置化的救援范围）
                double rescueRange = getRescueRange();
                boolean nearSoul = false;
                for (Entity entity : rescuer.getNearbyEntities(rescueRange, rescueRange, rescueRange)) {
                    if (entity.getUniqueId().equals(soulEntity.getUniqueId())) {
                        nearSoul = true;
                        break;
                    }
                }

                if (!nearSoul) {
                    String message = plugin.getConfig().getString("rescue_system.rescue_failure.rescuer_messages.left_area", "&c救援失败，你离开了灵魂");
                    cancelRevive(deadPlayerUUID, rescuer.getUniqueId(), ChatColor.translateAlternateColorCodes('&', message));
                    cancel(); // 立即停止当前任务
                    return;
                }

                // 更新救援进度
                progress++;
                reviveProgress.put(deadPlayerUUID, progress);

                // 更新救援进度Title
                Map<String, String> progressVariables = new HashMap<>();
                progressVariables.put("rescuer_name", rescuer.getName());
                progressVariables.put("target_name", Bukkit.getOfflinePlayer(deadPlayerUUID).getName());
                progressVariables.put("progress", String.valueOf(progress));
                progressVariables.put("total", String.valueOf(getRescueTimeRequired()));

                // 发送救援者进度Title
                sendConfigurableTitle(rescuer, "rescue_system.rescue_process.rescuer_title", progressVariables);

                // 发送被救援者进度Title
                Player deadPlayer = Bukkit.getPlayer(deadPlayerUUID);
                if (deadPlayer != null && deadPlayer.isOnline()) {
                    sendConfigurableTitle(deadPlayer, "rescue_system.rescue_process.target_title", progressVariables);
                }

                // 如果达到所需时间，复活玩家
                if (progress >= getRescueTimeRequired()) {
                    revivePlayer(deadPlayerUUID, soulEntity.getLocation());

                    // 发送救援成功消息给救援者
                    Map<String, String> successVariables = new HashMap<>();
                    successVariables.put("target_name", Bukkit.getOfflinePlayer(deadPlayerUUID).getName());
                    sendConfigurableMessages(rescuer, "rescue_system.rescue_success.rescuer_messages", successVariables);

                    cancel();
                    playerReviveTasks.remove(rescuer.getUniqueId());
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次

        // 存储任务引用
        playerReviveTasks.put(rescuer.getUniqueId(), task);
    }

    /**
     * 取消救援过程
     *
     * @param deadPlayerUUID 死亡玩家UUID
     * @param rescuerUUID 救援者UUID
     * @param message 取消原因消息
     */
    private void cancelRevive(UUID deadPlayerUUID, UUID rescuerUUID, String message) {
        // 检查是否已经在处理这个救援取消，防止重复执行
        if (!playerBeingRevived.containsKey(deadPlayerUUID)) {
            plugin.getLogger().info("救援过程已经被取消或不存在 - 死亡玩家: " + deadPlayerUUID + ", 救援者: " + rescuerUUID);
            return;
        }

        plugin.getLogger().info("取消救援过程 - 死亡玩家: " + deadPlayerUUID + ", 救援者: " + rescuerUUID);

        // 移除救援关系（先移除，防止重复调用）
        playerBeingRevived.remove(deadPlayerUUID);
        reviveProgress.remove(deadPlayerUUID);

        // 取消救援者的任务
        if (playerReviveTasks.containsKey(rescuerUUID)) {
            BukkitTask task = playerReviveTasks.get(rescuerUUID);
            if (task != null && !task.isCancelled()) {
                task.cancel();
                plugin.getLogger().info("已取消救援者 " + rescuerUUID + " 的救援任务");
            }
            playerReviveTasks.remove(rescuerUUID);
        }

        // 同时检查是否有以死亡玩家为键的任务（防止任务泄漏）
        if (playerReviveTasks.containsKey(deadPlayerUUID)) {
            BukkitTask task = playerReviveTasks.get(deadPlayerUUID);
            if (task != null && !task.isCancelled()) {
                task.cancel();
                plugin.getLogger().info("已取消死亡玩家 " + deadPlayerUUID + " 相关的救援任务");
            }
            playerReviveTasks.remove(deadPlayerUUID);
        }

        // 通知救援者（只发送一次）
        Player rescuer = Bukkit.getPlayer(rescuerUUID);
        if (rescuer != null && rescuer.isOnline()) {
            rescuer.sendMessage(message);
        }

        // 只有在玩家还是"downed"状态时才通知，避免重复消息
        Player deadPlayer = Bukkit.getPlayer(deadPlayerUUID);
        if (deadPlayer != null && deadPlayer.isOnline() && "downed".equals(playerStatus.get(deadPlayerUUID))) {
            Map<String, String> variables = new HashMap<>();
            variables.put("target_name", deadPlayer.getName());
            String generalMessage = plugin.getConfig().getString("rescue_system.rescue_failure.target_messages.general", "&c救援失败，请等待其他队友的救援");
            deadPlayer.sendMessage(ChatColor.translateAlternateColorCodes('&', generalMessage));
        }

        plugin.getLogger().info("救援取消完成");
    }

    /**
     * 清理指定玩家的灵魂NPC和相关数据
     *
     * @param player 玩家
     */
    public void cleanupPlayerSoulNPC(Player player) {
        if (player == null) {
            return;
        }

        UUID playerUUID = player.getUniqueId();
        plugin.getLogger().info("开始清理玩家 " + player.getName() + " 的灵魂NPC和相关数据");

        // 取消死亡计时器
        if (playerDeathTasks.containsKey(playerUUID)) {
            playerDeathTasks.get(playerUUID).cancel();
            playerDeathTasks.remove(playerUUID);
            plugin.getLogger().info("已取消玩家 " + player.getName() + " 的死亡计时器");
        }

        // 取消救援任务
        if (playerReviveTasks.containsKey(playerUUID)) {
            playerReviveTasks.get(playerUUID).cancel();
            playerReviveTasks.remove(playerUUID);
            plugin.getLogger().info("已取消玩家 " + player.getName() + " 的救援任务");
        }

        // 清理救援悬浮文字
        if (playerSoulHolograms.containsKey(playerUUID)) {
            UUID holoUUID = playerSoulHolograms.get(playerUUID);

            // 如果使用DecentHolograms
            if (plugin.isDecentHologramsAvailable()) {
                try {
                    // 尝试将UUID转换为字符串作为悬浮文字ID
                    String holoId = holoUUID.toString();
                    eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                    if (hologram != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(holoId);
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的救援悬浮文字");
                    }
                } catch (Exception e) {
                    // 如果不是DecentHolograms的悬浮文字，尝试常规方式移除
                    Entity holo = Bukkit.getEntity(holoUUID);
                    if (holo != null) {
                        // 检查是否是玩家实体，避免使用remove()方法
                        if (holo instanceof Player) {
                            plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                        } else {
                            holo.remove();
                            plugin.getLogger().info("已清理玩家 " + player.getName() + " 的救援悬浮文字");
                        }
                    }
                }
            } else {
                // 使用传统方式清理悬浮文字
                Entity holo = Bukkit.getEntity(holoUUID);
                if (holo != null) {
                    // 检查是否是玩家实体，避免使用remove()方法
                    if (holo instanceof Player) {
                        plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                    } else {
                        holo.remove();
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的救援悬浮文字");
                    }
                }
            }

            // 从映射中移除
            playerSoulHolograms.remove(playerUUID);
        }

        // 移除灵魂NPC
        removeSoulNPC(playerUUID);

        // 清理救援相关数据
        playerBeingRevived.remove(playerUUID);
        reviveProgress.remove(playerUUID);
        playerStatus.remove(playerUUID);

        // 同时清理PlayerInteractionManager中的状态数据
        playerManager.removePlayerData(player, "status");

        plugin.getLogger().info("已完成清理玩家 " + player.getName() + " 的灵魂NPC和相关数据");
    }

    /**
     * 清理所有灵魂NPC和全息图
     */
    public void cleanupAllSoulNPCs() {
        // 清理灵魂NPC
        for (UUID npcUUID : playerSoulNPCs.values()) {
            Entity npc = Bukkit.getEntity(npcUUID);
            if (npc != null) {
                // 检查是否是玩家实体，避免使用remove()方法
                if (npc instanceof Player) {
                    plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + npc.getName());
                } else {
                    npc.remove();
                }
            }
        }
        playerSoulNPCs.clear();

        // 清理全息图
        if (plugin.isDecentHologramsAvailable()) {
            // 使用DecentHolograms API清理悬浮文字
            for (UUID holoUUID : playerSoulHolograms.values()) {
                try {
                    // 尝试将UUID转换为字符串作为悬浮文字ID
                    String holoId = holoUUID.toString();
                    eu.decentsoftware.holograms.api.holograms.Hologram hologram = eu.decentsoftware.holograms.api.DHAPI.getHologram(holoId);
                    if (hologram != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(holoId);
                    }
                } catch (Exception e) {
                    // 如果不是DecentHolograms的悬浮文字，尝试常规方式移除
                    Entity holo = Bukkit.getEntity(holoUUID);
                    if (holo != null) {
                        // 检查是否是玩家实体，避免使用remove()方法
                        if (holo instanceof Player) {
                            plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                        } else {
                            holo.remove();
                        }
                    }
                }
            }
        } else {
            // 使用传统方式清理悬浮文字
            for (UUID holoUUID : playerSoulHolograms.values()) {
                Entity holo = Bukkit.getEntity(holoUUID);
                if (holo != null) {
                    // 检查是否是玩家实体，避免使用remove()方法
                    if (holo instanceof Player) {
                        plugin.getLogger().warning("尝试移除玩家实体，这是不允许的操作。实体名称: " + holo.getName());
                    } else {
                        holo.remove();
                    }
                }
            }
        }
        playerSoulHolograms.clear();

        // 清理救援相关数据
        playerBeingRevived.clear();
        reviveProgress.clear();
        playerStatus.clear();

        // 取消所有死亡任务
        for (BukkitTask task : playerDeathTasks.values()) {
            task.cancel();
        }
        playerDeathTasks.clear();

        // 取消所有救援任务
        for (BukkitTask task : playerReviveTasks.values()) {
            task.cancel();
        }
        playerReviveTasks.clear();

        plugin.getLogger().info("所有灵魂NPC和救援悬浮文字已清理");
    }

    /**
     * 检查实体是否是游戏实体（僵尸/变异生物）
     *
     * @param entity 要检查的实体
     * @return 如果是游戏实体返回true，否则返回false
     */
    private boolean isGameEntity(Entity entity) {
        if (entity == null) {
            return false;
        }

        // 检查是否是普通僵尸类型
        if (entity instanceof Zombie || entity.getType() == EntityType.ZOMBIE) {
            return true;
        }

        // 检查是否有游戏实体相关的元数据标记
        if (entity.hasMetadata("gameEntity") ||
            entity.hasMetadata("customZombieType") ||
            entity.hasMetadata("NPC") ||
            entity.hasMetadata("idcZombieEntity")) {
            return true;
        }

        // 检查变异生物特有的元数据标记
        if (entity.hasMetadata("isMutantCreeper") ||
            entity.hasMetadata("isMutantBlaze") ||
            entity.hasMetadata("mutantEntity") ||
            entity.hasMetadata("mutantZombie01") ||
            entity.hasMetadata("mutantZombie04") ||
            entity.hasMetadata("mutantIronGolem") ||
            entity.hasMetadata("mutationKing") ||
            entity.hasMetadata("soulGuardian") ||
            entity.hasMetadata("swampBoggedMonster") ||
            // 补充缺失的变异生物元数据标记
            entity.hasMetadata("mutantHusk") ||
            entity.hasMetadata("mutantZombieHorse") ||
            entity.hasMetadata("mutantZombieMax") ||
            // 检查所有可能的变异生物特有标记
            entity.hasMetadata("mutantEndermite") ||
            entity.hasMetadata("mutantSpider") ||
            entity.hasMetadata("mutantSkeleton") ||
            entity.hasMetadata("mutantWitherSkeleton") ||
            entity.hasMetadata("mutantSnowGolem") ||
            entity.hasMetadata("mutantMagmaCube") ||
            entity.hasMetadata("mutantPiglin") ||
            entity.hasMetadata("mutantShulker") ||
            entity.hasMetadata("mutantDragon")) {
            return true;
        }

        // 检查实体名称是否包含游戏实体标识
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            // 检查是否包含变异生物名称标识
            if (customName.contains("变异") || customName.contains("异变") ||
                customName.contains("灾厄") || customName.contains("感染") ||
                customName.contains("僵尸") || customName.contains("zombie") ||
                customName.contains("idc") || customName.contains("idn")) {
                return true;
            }
        }

        return false;
    }
}
