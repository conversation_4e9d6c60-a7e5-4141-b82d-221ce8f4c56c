package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.projectiles.ProjectileSource;

/**
 * 处理玩家PVP相关事件的监听器
 */
public class PlayerPvPListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;
    private final GameSessionManager gameSessionManager;
    private final ShootPluginHelper shootPluginHelper;
    private PlayerDeathReviveListener deathReviveListener;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public PlayerPvPListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameSessionManager = plugin.getGameSessionManager();
        this.shootPluginHelper = plugin.getShootPluginHelper();
    }

    /**
     * 设置死亡复活监听器
     *
     * @param deathReviveListener 死亡复活监听器
     */
    public void setDeathReviveListener(PlayerDeathReviveListener deathReviveListener) {
        this.deathReviveListener = deathReviveListener;
    }

    /**
     * 处理玩家对玩家的伤害事件，实现友伤控制
     *
     * @param event 实体伤害事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDamagePlayer(EntityDamageByEntityEvent event) {
        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        Player victim = (Player) event.getEntity();
        Player attacker = null;

        // 确定攻击者
        if (event.getDamager() instanceof Player) {
            attacker = (Player) event.getDamager();
        } else if (event.getDamager() instanceof Projectile) {
            // 处理弓箭、雪球等投射物
            Projectile projectile = (Projectile) event.getDamager();
            ProjectileSource shooter = projectile.getShooter();
            if (shooter instanceof Player) {
                attacker = (Player) shooter;
            }
        }

        // 如果攻击者不是玩家，不处理
        if (attacker == null) {
            return;
        }

        // 检查两个玩家是否都在游戏中
        if (!playerManager.isPlayerInStatus(victim, PlayerStatus.IN_GAME)
                || !playerManager.isPlayerInStatus(attacker, PlayerStatus.IN_GAME)) {
            return;
        }

        // 检查两个玩家是否在同一个游戏中
        String victimGame = gameSessionManager.getPlayerGame(victim);
        String attackerGame = gameSessionManager.getPlayerGame(attacker);
        if (victimGame == null || attackerGame == null || !victimGame.equals(attackerGame)) {
            return;
        }

        // 检查游戏是否启用友伤
        boolean friendlyFire = plugin.getConfig().getBoolean("game.friendly_fire", false);

        plugin.getLogger().info("PlayerPvPListener: 检测到玩家 " + attacker.getName() + " 攻击玩家 " + victim.getName() +
                ", 友军伤害: " + friendlyFire + ", 伤害: " + event.getFinalDamage() + ", 受害者当前血量: " + victim.getHealth());

        if (!friendlyFire) {
            // 如果禁用友伤，取消伤害事件
            event.setCancelled(true);

            // 检查是否启用友伤提示消息
            boolean friendlyFireMessageEnabled = plugin.getConfig().getBoolean("game.friendly_fire_message_enabled", true);
            if (friendlyFireMessageEnabled) {
                // 使用MessageManager发送友伤提示消息
                plugin.getMessageManager().sendFriendlyFireDisabledMessage(attacker);
            }

            plugin.getLogger().info("PlayerPvPListener: 已取消友军伤害");
        } else {
            // 如果启用友军伤害，检查这次伤害是否会导致玩家死亡
            if (victim.getHealth() <= event.getFinalDamage()) {
                plugin.getLogger().warning("PlayerPvPListener: 玩家 " + victim.getName() + " 将被 " + attacker.getName() + " 的攻击击杀！");

                // 取消伤害事件，防止玩家真正死亡
                event.setCancelled(true);

                // 恢复玩家生命值
                victim.setHealth(1.0);

                // 直接调用倒地机制
                if (deathReviveListener != null) {
                    deathReviveListener.handlePlayerKilledByZombie(victim, attacker);
                    plugin.getLogger().info("PlayerPvPListener: 已在EntityDamageByEntityEvent中处理玩家 " + victim.getName() + " 的倒地");
                } else {
                    plugin.getLogger().warning("PlayerPvPListener: 死亡复活监听器未设置，无法处理倒地");
                }

                // 计算奖励金额
                double reward = calculatePvPReward(attacker, victim);

                // 给予击杀者金钱奖励
                if (reward > 0) {
                    rewardPlayer(attacker, victim, reward);
                }

                // 发送Title通知
                sendKillTitle(attacker, victim);
            }
        }
    }

    /**
     * 处理玩家死亡事件
     * 使用HIGH优先级确保在ZombieAttackListener之前处理PvP死亡
     *
     * @param event 玩家死亡事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player victim = event.getEntity();
        Player killer = victim.getKiller();

        plugin.getLogger().info("PlayerPvPListener: PlayerDeathEvent触发，受害者: " + victim.getName() +
                ", 击杀者: " + (killer != null ? killer.getName() : "null"));

        // 如果没有击杀者，则不处理
        if (killer == null) {
            plugin.getLogger().info("PlayerPvPListener: 没有击杀者，跳过处理");
            return;
        }

        // 检查两个玩家是否都在游戏中
        if (!playerManager.isPlayerInStatus(victim, PlayerStatus.IN_GAME)
                || !playerManager.isPlayerInStatus(killer, PlayerStatus.IN_GAME)) {
            return;
        }

        // 检查两个玩家是否在同一个游戏中
        String victimGame = gameSessionManager.getPlayerGame(victim);
        String killerGame = gameSessionManager.getPlayerGame(killer);
        if (victimGame == null || killerGame == null || !victimGame.equals(killerGame)) {
            return;
        }

        // 检查是否启用友军伤害
        boolean friendlyFire = plugin.getConfig().getBoolean("game.friendly_fire", false);
        if (!friendlyFire) {
            // 如果禁用友军伤害，这种情况不应该发生，直接返回
            plugin.getLogger().warning("玩家 " + victim.getName() + " 被 " + killer.getName() + " 击杀，但友军伤害已禁用");
            return;
        }

        // 取消死亡事件，防止玩家真正死亡和复活到世界出生点
        event.setCancelled(true);

        // 取消死亡消息广播
        event.setDeathMessage(null);

        // 重要：恢复玩家生命值，防止触发重生机制
        victim.setHealth(1.0);

        // 如果死亡复活监听器已设置，则处理玩家被击杀（使用倒地机制）
        if (deathReviveListener != null) {
            // 将玩家击杀者作为damager传递，这样可以在倒地时显示正确的击杀信息
            deathReviveListener.handlePlayerKilledByZombie(victim, killer);

            plugin.getLogger().info("玩家 " + victim.getName() + " 被玩家 " + killer.getName() + " 击杀，已进入倒地状态");
        } else {
            plugin.getLogger().warning("死亡复活监听器未设置，无法处理玩家 " + victim.getName() + " 的PvP死亡");
        }

        // 计算奖励金额
        double reward = calculatePvPReward(killer, victim);

        // 给予击杀者金钱奖励
        if (reward > 0) {
            rewardPlayer(killer, victim, reward);
        }

        // 发送Title通知
        sendKillTitle(killer, victim);
    }

    /**
     * 处理玩家重生事件，防止PvP死亡后的意外重生
     *
     * @param event 玩家重生事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 检查玩家是否在游戏会话中
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查玩家是否处于倒地状态
        if (deathReviveListener != null) {
            String playerStatus = plugin.getPlayerInteractionManager().getPlayerData(player, "status");
            if ("downed".equals(playerStatus) || "dead".equals(playerStatus)) {
                // 玩家正在倒地状态，不应该重生
                plugin.getLogger().warning("玩家 " + player.getName() + " 在倒地状态下尝试重生，将阻止重生并保持倒地状态");

                // 延迟设置玩家为旁观者模式，确保重生事件完成后再处理
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    if (player.isOnline()) {
                        player.setGameMode(org.bukkit.GameMode.SPECTATOR);
                        plugin.getLogger().info("已将玩家 " + player.getName() + " 重新设置为旁观者模式");
                    }
                }, 1L);

                return;
            }
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 正常重生");
    }

    /**
     * 计算PVP击杀奖励金额
     *
     * @param killer 击杀者
     * @param victim 被击杀者
     * @return 奖励金额
     */
    private double calculatePvPReward(Player killer, Player victim) {
        // 基础奖励金额
        double baseReward = plugin.getConfig().getDouble("game.money.player_kill", 50);

        // 根据武器类型计算额外奖励
        double bonusMultiplier = 1.0;
        ItemStack weapon = killer.getInventory().getItemInMainHand();

        if (weapon != null && !weapon.getType().isAir()) {
            String weaponType = weapon.getType().toString();

            // 近战武器额外奖励
            if (weaponType.contains("SWORD") || weaponType.contains("AXE")) {
                bonusMultiplier += plugin.getConfig().getDouble("game.money.melee_weapon_bonus", 0.3);
            } // 远程武器额外奖励
            else if (weaponType.contains("BOW") || weaponType.contains("CROSSBOW")) {
                bonusMultiplier += plugin.getConfig().getDouble("game.money.ranged_weapon_bonus", 0.1);
            }
        }

        // 计算最终奖励
        double finalReward = baseReward * bonusMultiplier;

        // 应用最小/最大限制
        double minReward = plugin.getConfig().getDouble("game.money.min_reward", 5);
        double maxReward = plugin.getConfig().getDouble("game.money.max_reward", 100);

        finalReward = Math.max(minReward, Math.min(finalReward, maxReward));

        // 随机波动 ±10%
        double randomFactor = 0.9 + Math.random() * 0.2; // 0.9 到 1.1
        finalReward *= randomFactor;

        // 四舍五入到整数
        return Math.round(finalReward);
    }

    /**
     * 处理玩家击杀奖惩
     *
     * @param killer 击杀者
     * @param victim 被击杀者
     * @param amount 金额
     */
    private void rewardPlayer(Player killer, Player victim, double amount) {
        // 使用ShootPluginHelper减少玩家金钱
        shootPluginHelper.subtractPlayerMoney(killer, amount);

        // 消息提示
        String message = ChatColor.GOLD + "击杀玩家 " + ChatColor.RED + victim.getName()
                + ChatColor.GOLD + " -" + ChatColor.RED + (int) amount + ChatColor.GOLD + " 金钱";
        killer.sendMessage(message);
    }

    /**
     * 发送击杀Title通知
     *
     * @param killer 击杀者
     * @param victim 被击杀者
     */
    private void sendKillTitle(Player killer, Player victim) {
        // 击杀者标题和副标题
        String killerTitle = ChatColor.RED + "击杀!";
        String killerSubtitle = ChatColor.GOLD + "你击杀了 " + ChatColor.RED + victim.getName();

        // 被击杀者标题和副标题
        String victimTitle = ChatColor.RED + "死亡!";
        String victimSubtitle = ChatColor.GOLD + "你被 " + ChatColor.RED + killer.getName() + ChatColor.GOLD + " 击杀了";

        try {
            // 使用原生方法发送标题
            killer.sendTitle(killerTitle, killerSubtitle, 10, 40, 20);
            victim.sendTitle(victimTitle, victimSubtitle, 10, 40, 20);
            plugin.getLogger().info("发送了击杀通知");

            // 使用原生方法发送标题
            killer.sendTitle(killerTitle, killerSubtitle, 10, 40, 20);
            victim.sendTitle(victimTitle, victimSubtitle, 10, 40, 20);
            plugin.getLogger().info("使用原生方法发送了击杀通知");
        } catch (Exception ex) {
            plugin.getLogger().warning("发送击杀Title失败: " + ex.getMessage());
            // 如果标题发送失败，尝试发送普通消息
            killer.sendMessage(killerTitle);
            killer.sendMessage(killerSubtitle);
            victim.sendMessage(victimTitle);
            victim.sendMessage(victimSubtitle);
        }
    }
}
