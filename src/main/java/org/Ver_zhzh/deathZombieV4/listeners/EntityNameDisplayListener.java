package org.Ver_zhzh.deathZombieV4.listeners;

import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 实体名称动态显示监听器
 * 处理玩家靠近实体时的名称显示和隐藏
 */
public class EntityNameDisplayListener implements Listener {

    private final DeathZombieV4 plugin;

    // 存储玩家当前附近的实体
    private final Map<UUID, Set<Entity>> playerNearbyEntities = new HashMap<>();

    // 存储实体的原始名称可见性状态
    private final Map<Entity, Boolean> originalNameVisibility = new HashMap<>();

    // 检查任务
    private BukkitRunnable checkTask;

    public EntityNameDisplayListener(DeathZombieV4 plugin) {
        this.plugin = plugin;

        // 检查功能是否启用
        if (isFeatureEnabled()) {
            startTargetCheckTask();
            // 初始化时隐藏所有游戏实体的名称
            initializeEntityNameVisibility();
        }
    }

    /**
     * 启动目标检查任务
     */
    private void startTargetCheckTask() {
        checkTask = new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : plugin.getServer().getOnlinePlayers()) {
                    // 检查玩家是否在游戏中
                    String gameName = plugin.getGameSessionManager().getPlayerGame(player);
                    if (gameName == null) {
                        continue;
                    }

                    // 检查玩家附近的实体
                    checkPlayerNearbyEntities(player, gameName);
                }
            }
        };

        // 使用配置的检查间隔
        long checkInterval = getCheckInterval();
        checkTask.runTaskTimer(plugin, 0L, checkInterval);
    }

    /**
     * 检查玩家附近的实体
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void checkPlayerNearbyEntities(Player player, String gameName) {
        UUID playerId = player.getUniqueId();
        Set<Entity> currentNearbyEntities = getPlayerNearbyEntities(player, gameName);
        Set<Entity> previousNearbyEntities = playerNearbyEntities.getOrDefault(playerId, new HashSet<>());

        // 找出新增的附近实体（需要显示名称）
        Set<Entity> newEntities = new HashSet<>(currentNearbyEntities);
        newEntities.removeAll(previousNearbyEntities);

        // 找出不再附近的实体（需要隐藏名称）
        Set<Entity> removedEntities = new HashSet<>(previousNearbyEntities);
        removedEntities.removeAll(currentNearbyEntities);

        // 显示新附近实体的名称
        for (Entity entity : newEntities) {
            if (entity.isValid()) {
                showEntityName(entity);
            }
        }

        // 隐藏不再附近实体的名称
        for (Entity entity : removedEntities) {
            if (entity.isValid()) {
                hideEntityName(entity);
            }
        }

        // 更新玩家附近实体记录
        if (currentNearbyEntities.isEmpty()) {
            playerNearbyEntities.remove(playerId);
        } else {
            playerNearbyEntities.put(playerId, currentNearbyEntities);
        }
    }

    /**
     * 获取玩家附近的游戏实体
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 附近的实体集合
     */
    private Set<Entity> getPlayerNearbyEntities(Player player, String gameName) {
        Set<Entity> nearbyEntities = new HashSet<>();
        double displayDistance = getDisplayDistance();

        // 获取玩家附近指定范围内的所有实体
        for (Entity entity : player.getNearbyEntities(displayDistance, displayDistance, displayDistance)) {
            // 检查是否是游戏实体
            if (isGameEntity(entity, gameName)) {
                // 计算精确距离
                double distance = player.getLocation().distance(entity.getLocation());

                // 确保在显示距离范围内
                if (distance <= displayDistance) {
                    nearbyEntities.add(entity);
                }
            }
        }

        return nearbyEntities;
    }

    /**
     * 检查实体是否属于指定游戏
     *
     * @param entity 实体
     * @param gameName 游戏名称
     * @return 是否属于游戏
     */
    private boolean isGameEntity(Entity entity, String gameName) {
        // 检查实体是否有游戏会话标记
        if (entity.hasMetadata("gameSession")) {
            String entityGameName = entity.getMetadata("gameSession").get(0).asString();
            return gameName.equals(entityGameName);
        }

        // 检查实体名称是否包含游戏标识
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            if (customName.startsWith("游戏_" + gameName + "_")) {
                return true;
            }
        }

        // 检查是否是游戏相关的实体类型
        if (entity instanceof LivingEntity && !(entity instanceof Player)) {
            // 排除功能性实体（门卫NPC、全息图等）
            if (entity.hasMetadata("doorNPC") || entity.hasMetadata("hologram")) {
                return false; // 不处理这些功能性实体
            }

            // 检查各种游戏实体标记
            if (entity.hasMetadata("gameEntity") ||
                entity.hasMetadata("customZombieType") ||
                entity.hasMetadata("NPC") ||
                entity.hasMetadata("idcZombieEntity") ||
                entity.hasMetadata("mutationKing") ||
                entity.hasMetadata("mutantIronGolem") ||
                entity.hasMetadata("soulGuardian") ||
                entity.hasMetadata("twinZombie") ||
                entity.hasMetadata("mutantEndermite") ||
                entity.hasMetadata("mutantSpider") ||
                entity.hasMetadata("disasterGuardian") ||
                entity.hasMetadata("disasterSummoner") ||
                entity.hasMetadata("disasterRavagerBeast") ||
                entity.hasMetadata("mutantZombieHorse") ||
                entity.hasMetadata("mutantMagmaCube") ||
                entity.hasMetadata("mutantHusk") ||
                entity.hasMetadata("swampStrayMonster") ||
                entity.hasMetadata("mutantZombie04") ||
                entity.hasMetadata("screamingZombie")) {
                return true;
            }

            // 检查实体名称中的关键词
            if (entity.getCustomName() != null) {
                String customName = entity.getCustomName();
                if (customName.contains("感染") || customName.contains("僵尸") ||
                    customName.contains("变异") || customName.contains("id") ||
                    customName.contains("idc") || customName.contains("idn") ||
                    customName.contains("zombie") || customName.contains("灵魂") ||
                    customName.contains("异变") || customName.contains("灾厄") ||
                    customName.contains("凋零") || customName.contains("暗影") ||
                    customName.contains("鲜血") || customName.contains("烈焰") ||
                    customName.contains("爬行者") || customName.contains("末影") ||
                    customName.contains("蜘蛛") || customName.contains("岩浆") ||
                    customName.contains("尸壳") || customName.contains("尖叫") ||
                    customName.contains("博士") || customName.contains("法师") ||
                    customName.contains("科学家") || customName.contains("毁灭") ||
                    customName.contains("雷霆") || customName.contains("电击") ||
                    customName.contains("冰冻") || customName.contains("气球") ||
                    customName.contains("迷雾") || customName.contains("终极") ||
                    customName.contains("肥胖")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 显示实体名称
     *
     * @param entity 实体
     */
    private void showEntityName(Entity entity) {
        if (entity instanceof LivingEntity) {
            LivingEntity livingEntity = (LivingEntity) entity;
            
            // 保存原始可见性状态
            if (!originalNameVisibility.containsKey(entity)) {
                originalNameVisibility.put(entity, livingEntity.isCustomNameVisible());
            }
            
            // 显示名称
            if (livingEntity.getCustomName() != null) {
                livingEntity.setCustomNameVisible(true);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("玩家靠近实体，显示名称: " + livingEntity.getCustomName());
                }
            }
        }
    }

    /**
     * 隐藏实体名称
     *
     * @param entity 实体
     */
    private void hideEntityName(Entity entity) {
        if (entity instanceof LivingEntity) {
            LivingEntity livingEntity = (LivingEntity) entity;
            
            // 恢复原始可见性状态
            Boolean originalVisibility = originalNameVisibility.get(entity);
            if (originalVisibility != null) {
                livingEntity.setCustomNameVisible(originalVisibility);
                originalNameVisibility.remove(entity);
                
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("玩家远离实体，隐藏名称: " + (livingEntity.getCustomName() != null ? livingEntity.getCustomName() : "无名称"));
                }
            } else {
                // 如果没有保存的状态，默认隐藏
                livingEntity.setCustomNameVisible(false);
            }
        }
    }

    /**
     * 玩家移动事件处理（用于清理离线玩家的数据）
     */
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        // 这个事件主要用于确保玩家在线状态的检查
        // 实际的目标检查在定时任务中进行
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        if (checkTask != null) {
            checkTask.cancel();
        }
        
        // 恢复所有实体的名称可见性
        for (Map.Entry<Entity, Boolean> entry : originalNameVisibility.entrySet()) {
            Entity entity = entry.getKey();
            Boolean originalVisibility = entry.getValue();
            
            if (entity.isValid() && entity instanceof LivingEntity) {
                ((LivingEntity) entity).setCustomNameVisible(originalVisibility);
            }
        }
        
        playerNearbyEntities.clear();
        originalNameVisibility.clear();
    }

    /**
     * 清理特定游戏的数据
     *
     * @param gameName 游戏名称
     */
    public void cleanupGame(String gameName) {
        // 清理该游戏相关的玩家附近实体数据
        playerNearbyEntities.entrySet().removeIf(entry -> {
            Set<Entity> entities = entry.getValue();
            entities.removeIf(entity -> isGameEntity(entity, gameName));
            return entities.isEmpty();
        });

        // 恢复该游戏实体的名称可见性
        originalNameVisibility.entrySet().removeIf(entry -> {
            Entity entity = entry.getKey();
            Boolean originalVisibility = entry.getValue();

            if (isGameEntity(entity, gameName)) {
                if (entity.isValid() && entity instanceof LivingEntity) {
                    ((LivingEntity) entity).setCustomNameVisible(originalVisibility);
                }
                return true;
            }
            return false;
        });
    }

    /**
     * 检查功能是否启用
     *
     * @return 是否启用
     */
    private boolean isFeatureEnabled() {
        return plugin.getConfig().getBoolean("entity_name_display.enabled", true);
    }

    /**
     * 获取显示距离
     *
     * @return 显示距离
     */
    private double getDisplayDistance() {
        return plugin.getConfig().getDouble("entity_name_display.display_distance", 5.0);
    }

    /**
     * 获取检查间隔
     *
     * @return 检查间隔（tick）
     */
    private long getCheckInterval() {
        return plugin.getConfig().getLong("entity_name_display.check_interval", 10L);
    }

    /**
     * 初始化实体名称可见性，隐藏所有游戏实体的名称
     */
    private void initializeEntityNameVisibility() {
        // 遍历所有世界，隐藏游戏实体的名称
        for (org.bukkit.World world : plugin.getServer().getWorlds()) {
            for (Entity entity : world.getEntities()) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity livingEntity = (LivingEntity) entity;

                    // 检查是否是游戏实体
                    if (isGameEntityForAnyGame(entity)) {
                        // 保存原始可见性状态
                        if (!originalNameVisibility.containsKey(entity)) {
                            originalNameVisibility.put(entity, livingEntity.isCustomNameVisible());
                        }

                        // 隐藏名称
                        livingEntity.setCustomNameVisible(false);

                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("初始化时隐藏实体名称: " + (livingEntity.getCustomName() != null ? livingEntity.getCustomName() : "无名称"));
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查实体是否属于任何游戏（不限定特定游戏名称）
     *
     * @param entity 实体
     * @return 是否属于游戏
     */
    private boolean isGameEntityForAnyGame(Entity entity) {
        // 检查实体是否有游戏会话标记
        if (entity.hasMetadata("gameSession")) {
            return true;
        }

        // 检查实体名称是否包含游戏标识
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            if (customName.startsWith("游戏_")) {
                return true;
            }
        }

        // 检查是否是游戏相关的实体类型
        if (entity instanceof LivingEntity && !(entity instanceof Player)) {
            // 排除功能性实体（门卫NPC、全息图等）
            if (entity.hasMetadata("doorNPC") || entity.hasMetadata("hologram")) {
                return false; // 不处理这些功能性实体
            }

            // 检查各种游戏实体标记
            if (entity.hasMetadata("gameEntity") ||
                entity.hasMetadata("customZombieType") ||
                entity.hasMetadata("NPC") ||
                entity.hasMetadata("idcZombieEntity") ||
                entity.hasMetadata("mutationKing") ||
                entity.hasMetadata("mutantIronGolem") ||
                entity.hasMetadata("soulGuardian") ||
                entity.hasMetadata("twinZombie") ||
                entity.hasMetadata("mutantEndermite") ||
                entity.hasMetadata("mutantSpider") ||
                entity.hasMetadata("disasterGuardian") ||
                entity.hasMetadata("disasterSummoner") ||
                entity.hasMetadata("disasterRavagerBeast") ||
                entity.hasMetadata("mutantZombieHorse") ||
                entity.hasMetadata("mutantMagmaCube") ||
                entity.hasMetadata("mutantHusk") ||
                entity.hasMetadata("swampStrayMonster") ||
                entity.hasMetadata("mutantZombie04") ||
                entity.hasMetadata("screamingZombie")) {
                return true;
            }

            // 检查实体名称中的关键词
            if (entity.getCustomName() != null) {
                String customName = entity.getCustomName();
                if (customName.contains("感染") || customName.contains("僵尸") ||
                    customName.contains("变异") || customName.contains("id") ||
                    customName.contains("idc") || customName.contains("idn") ||
                    customName.contains("zombie") || customName.contains("灵魂") ||
                    customName.contains("异变") || customName.contains("灾厄") ||
                    customName.contains("凋零") || customName.contains("暗影") ||
                    customName.contains("鲜血") || customName.contains("烈焰") ||
                    customName.contains("爬行者") || customName.contains("末影") ||
                    customName.contains("蜘蛛") || customName.contains("岩浆") ||
                    customName.contains("尸壳") || customName.contains("尖叫") ||
                    customName.contains("博士") || customName.contains("法师") ||
                    customName.contains("科学家") || customName.contains("毁灭") ||
                    customName.contains("雷霆") || customName.contains("电击") ||
                    customName.contains("冰冻") || customName.contains("气球") ||
                    customName.contains("迷雾") || customName.contains("终极") ||
                    customName.contains("肥胖")) {
                    return true;
                }
            }
        }

        return false;
    }
}
