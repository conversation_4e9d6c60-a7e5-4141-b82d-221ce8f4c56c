package org.Ver_zhzh.deathZombieV4.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

/**
 * 管理游戏中的门交互逻辑
 */
public class DoorManager {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;

    // 存储每个游戏的门区域信息: <游戏名, <门名, 门区域>>
    private final Map<String, Map<String, Region>> doorRegions;

    // 存储已解锁的门: <游戏名, <门名, 是否已解锁>>
    private final Map<String, Map<String, Boolean>> unlockedDoors;

    // 存储游戏内开启的门: <游戏名, <门名, 是否在游戏内开启>>
    private final Map<String, Map<String, Boolean>> inGameOpeningDoors;

    // 随机数生成器
    // private final Random random = new Random(); // 暂时未使用
    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public DoorManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.doorRegions = new HashMap<>();
        this.unlockedDoors = new HashMap<>();
        this.inGameOpeningDoors = new HashMap<>();
    }

    /**
     * 注册门区域
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param region 门区域
     */
    public void registerDoorRegion(String gameName, String doorName, Region region) {
        if (!doorRegions.containsKey(gameName)) {
            doorRegions.put(gameName, new HashMap<>());
        }
        doorRegions.get(gameName).put(doorName, region);

        // 初始化该门的解锁状态
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors != null && doors.containsKey(doorName)) {
            boolean isLocked = (boolean) doors.get(doorName).get("locked");
            if (!isLocked) {
                // 如果门配置为自动解锁状态
                if (!unlockedDoors.containsKey(gameName)) {
                    unlockedDoors.put(gameName, new HashMap<>());
                }
                unlockedDoors.get(gameName).put(doorName, true);

                // 对于自动解锁的门，也设置InGameOpening为true
                setDoorInGameOpening(gameName, doorName, true);
                plugin.getLogger().info("门 " + doorName + " 配置为自动解锁，同时设置游戏内开启状态为true");
            }
        }
    }

    /**
     * 检查玩家是否在门区域内
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 玩家所在的门名称，如果不在任何门区域内则返回null
     */
    public String getPlayerDoorRegion(Player player, String gameName) {
        if (!doorRegions.containsKey(gameName)) {
            return null;
        }

        Map<String, Region> regions = doorRegions.get(gameName);
        for (Map.Entry<String, Region> entry : regions.entrySet()) {
            if (entry.getValue().contains(player.getLocation())) {
                return entry.getKey();
            }
        }

        return null;
    }

    /**
     * 尝试解锁门
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 是否成功解锁
     */
    public boolean unlockDoor(Player player, String gameName, String doorName) {
        plugin.getLogger().info("尝试为玩家 " + player.getName() + " 解锁游戏 " + gameName + " 中的门 " + doorName);

        // 检查门是否已经解锁
        if (isDoorUnlocked(gameName, doorName)) {
            player.sendMessage(ChatColor.YELLOW + "这个门已经解锁了！");
            plugin.getLogger().info("门 " + doorName + " 已经解锁，无需再次解锁");
            return false;
        }

        // 获取门的解锁价格
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors == null || !doors.containsKey(doorName)) {
            player.sendMessage(ChatColor.RED + "找不到门 '" + doorName + "' 的配置信息！");
            plugin.getLogger().warning("找不到游戏 " + gameName + " 中门 " + doorName + " 的配置信息");
            return false;
        }

        Map<String, Object> doorInfo = doors.get(doorName);

        plugin.getLogger().info("门 " + doorName + " 的配置信息: " + doorInfo);

        if (!(boolean) doorInfo.get("locked")) {
            // 门配置为自动解锁状态
            plugin.getLogger().info("门 " + doorName + " 配置为自动解锁状态");
            setDoorUnlocked(gameName, doorName, true);
            return true;
        }

        int unlockPrice = (int) doorInfo.get("unlockPrice");

        // 检查玩家是否有足够的金钱
        double playerMoney = plugin.getShootPluginHelper().getPlayerMoney(player);
        plugin.getLogger().info("玩家 " + player.getName() + " 当前金钱: " + playerMoney + ", 门解锁价格: " + unlockPrice);

        if (playerMoney < unlockPrice) {
            player.sendMessage(ChatColor.RED + "你没有足够的金钱解锁这个门！需要 " + unlockPrice + " 金币，你只有 " + (int) playerMoney + " 金币。");
            plugin.getLogger().info("玩家 " + player.getName() + " 金钱不足，无法解锁门 " + doorName);
            return false;
        }

        // 扣除玩家金钱
        boolean moneyDeducted = plugin.getShootPluginHelper().subtractPlayerMoney(player, unlockPrice);
        if (!moneyDeducted) {
            player.sendMessage(ChatColor.RED + "扣除金钱失败，无法解锁门！");
            plugin.getLogger().warning("无法从玩家 " + player.getName() + " 扣除 " + unlockPrice + " 金币");
            return false;
        }

        plugin.getLogger().info("已从玩家 " + player.getName() + " 扣除 " + unlockPrice + " 金币");

        // 记录统计数据
        plugin.getPlayerInteractionManager().addPlayerDoorsOpened(player, gameName, 1);
        plugin.getPlayerInteractionManager().addPlayerMoneySpent(player, gameName, unlockPrice);

        // 设置门为已解锁状态
        setDoorUnlocked(gameName, doorName, true);
        plugin.getLogger().info("门 " + doorName + " 已设置为解锁状态");

        // 设置门的游戏内开启状态为true
        setDoorInGameOpening(gameName, doorName, true);
        plugin.getLogger().info("门 " + doorName + " 已设置为游戏内开启状态");

        // 检查是否有关联的僵尸生成点
        activateLinkedZombieSpawn(gameName, doorName);

        player.sendMessage(ChatColor.GREEN + "你已成功解锁门 '" + doorName + "'，花费了 " + unlockPrice + " 金币！");

        // 广播消息
        for (Player p : Bukkit.getOnlinePlayers()) {
            if (p != player) {
                p.sendMessage(ChatColor.YELLOW + player.getName() + " 解锁了门 '" + doorName + "'！");
            }
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 成功解锁了门 " + doorName);

        return true;
    }

    /**
     * 激活与门关联的僵尸生成点
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     */
    private void activateLinkedZombieSpawn(String gameName, String doorName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return;
        }

        // 检查门是否存在
        String doorPath = "doors." + doorName.replace(".", "_");
        if (!config.isSet(doorPath)) {
            return;
        }

        // 同步更新关联的出生点状态
        updateLinkedSpawnPointsForDoor(gameName, doorName, true);

        // 首先检查新的多生成点关联
        if (config.isList(doorPath + ".linkedSpawns")) {
            List<String> spawnNames = config.getStringList(doorPath + ".linkedSpawns");
            if (spawnNames.isEmpty()) {
                return;
            }

            plugin.getLogger().info("门 " + doorName + " 解锁，关联的生成点: " + String.join(", ", spawnNames) + " 已启用");
        } // 兼容旧的单一生成点关联
        else if (config.isSet(doorPath + ".linkedSpawn")) {
            String spawnName = config.getString(doorPath + ".linkedSpawn");
            if (spawnName == null || spawnName.isEmpty()) {
                return;
            }

            plugin.getLogger().info("门 " + doorName + " 解锁，关联的生成点: " + spawnName + " 已启用");
        }
    }

    /**
     * 设置门的解锁状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param unlocked 是否解锁
     */
    public void setDoorUnlocked(String gameName, String doorName, boolean unlocked) {
        if (!unlockedDoors.containsKey(gameName)) {
            unlockedDoors.put(gameName, new HashMap<>());
        }

        // 更新门的状态
        unlockedDoors.get(gameName).put(doorName, unlocked);

        // 只有当门被解锁时，才移除门区域的方块
        // 锁定的门保持方块不变，直到玩家解锁
        if (unlocked) {
            removeDoorBlocks(gameName, doorName);

            // 当门被解锁时，更新相关生成点的状态
            updateSpawnPointsForUnlockedDoor(gameName, doorName);
        }
    }

    /**
     * 设置门的游戏内开启状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param inGameOpening 是否在游戏内开启
     */
    public void setDoorInGameOpening(String gameName, String doorName, boolean inGameOpening) {
        if (!inGameOpeningDoors.containsKey(gameName)) {
            inGameOpeningDoors.put(gameName, new HashMap<>());
        }

        // 更新内存中的状态
        inGameOpeningDoors.get(gameName).put(doorName, inGameOpening);

        // 同时更新配置文件
        gameManager.setDoorInGameOpening(gameName, doorName, inGameOpening);

        plugin.getLogger().info("门 " + doorName + " 的游戏内开启状态已设置为: " + inGameOpening);
    }

    /**
     * 检查门是否在游戏内开启
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 门是否在游戏内开启
     */
    public boolean isDoorInGameOpening(String gameName, String doorName) {
        // 首先检查运行时的状态
        if (inGameOpeningDoors.containsKey(gameName)
                && inGameOpeningDoors.get(gameName).containsKey(doorName)) {
            return inGameOpeningDoors.get(gameName).get(doorName);
        }

        // 如果运行时没有记录，从配置文件中读取
        boolean inGameOpening = gameManager.getDoorInGameOpening(gameName, doorName);

        // 缓存到内存中
        if (!inGameOpeningDoors.containsKey(gameName)) {
            inGameOpeningDoors.put(gameName, new HashMap<>());
        }
        inGameOpeningDoors.get(gameName).put(doorName, inGameOpening);

        return inGameOpening;
    }

    /**
     * 移除门区域内的方块
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     */
    public void removeDoorBlocks(String gameName, String doorName) {
        if (!doorRegions.containsKey(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有门区域映射");
            return;
        }

        if (!doorRegions.get(gameName).containsKey(doorName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 中找不到门 " + doorName + " 的区域");
            return;
        }

        Region region = doorRegions.get(gameName).get(doorName);

        // 创建一个延迟任务，在主线程中移除方块
        Bukkit.getScheduler().runTask(plugin, () -> {
            for (int x = (int) region.getMinX(); x <= (int) region.getMaxX(); x++) {
                for (int y = (int) region.getMinY(); y <= (int) region.getMaxY(); y++) {
                    for (int z = (int) region.getMinZ(); z <= (int) region.getMaxZ(); z++) {
                        Block block = region.getWorld().getBlockAt(x, y, z);
                        // 只移除实心方块，不移除空气、水等
                        if (block.getType() != Material.AIR
                                && block.getType() != Material.WATER
                                && block.getType() != Material.LAVA
                                && !block.getType().name().contains("SIGN")) {
                            block.setType(Material.AIR);
                        }
                    }
                }
            }
        });
    }

    /**
     * 填充门区域内的方块
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     */
    public void fillDoorBlocks(String gameName, String doorName) {
        if (!doorRegions.containsKey(gameName) || !doorRegions.get(gameName).containsKey(doorName)) {
            return;
        }

        Region region = doorRegions.get(gameName).get(doorName);

        // 获取门的材质信息
        Material doorMaterial = Material.BARRIER; // 默认使用障碍方块

        // 尝试从游戏配置中获取门的材质
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors != null && doors.containsKey(doorName)) {
            Map<String, Object> doorInfo = doors.get(doorName);
            if (doorInfo.containsKey("material")) {
                String materialName = (String) doorInfo.get("material");
                try {
                    doorMaterial = Material.valueOf(materialName.toUpperCase());
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的门材质: " + materialName + "，使用默认材质 BARRIER");
                }
            }
        }

        // 存储最终使用的材质
        final Material finalDoorMaterial = doorMaterial;

        // 创建一个延迟任务，在主线程中填充方块
        Bukkit.getScheduler().runTask(plugin, () -> {
            for (int x = (int) region.getMinX(); x <= (int) region.getMaxX(); x++) {
                for (int y = (int) region.getMinY(); y <= (int) region.getMaxY(); y++) {
                    for (int z = (int) region.getMinZ(); z <= (int) region.getMaxZ(); z++) {
                        Block block = region.getWorld().getBlockAt(x, y, z);
                        // 只在空气方块位置放置门方块
                        if (block.getType() == Material.AIR) {
                            block.setType(finalDoorMaterial);
                        }
                    }
                }
            }
        });
    }

    /**
     * 检查门是否已解锁（现在检查InGameOpening状态）
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 门是否在游戏内开启
     */
    public boolean isDoorUnlocked(String gameName, String doorName) {
        // 现在检查门的游戏内开启状态，而不是locked状态
        return isDoorInGameOpening(gameName, doorName);
    }

    /**
     * 重置游戏中所有门的状态
     *
     * @param gameName 游戏名称
     */
    public void resetDoors(String gameName) {
        if (unlockedDoors.containsKey(gameName)) {
            unlockedDoors.get(gameName).clear();
        }

        // 重置所有门的游戏内开启状态为false
        if (inGameOpeningDoors.containsKey(gameName)) {
            inGameOpeningDoors.get(gameName).clear();
        }

        // 获取所有门并重置它们的InGameOpening状态
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors != null) {
            for (String doorName : doors.keySet()) {
                // 重置游戏内开启状态为false
                setDoorInGameOpening(gameName, doorName, false);
                plugin.getLogger().info("重置门 " + doorName + " 的游戏内开启状态为false");
            }
        }

        // 重新加载自动解锁的门，并设置相应的InGameOpening状态
        if (doors != null) {
            for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
                String doorName = entry.getKey();
                Map<String, Object> doorInfo = entry.getValue();

                if (!(boolean) doorInfo.get("locked")) {
                    // 如果门配置为自动解锁状态，设置为解锁并设置InGameOpening为true
                    if (!unlockedDoors.containsKey(gameName)) {
                        unlockedDoors.put(gameName, new HashMap<>());
                    }
                    unlockedDoors.get(gameName).put(doorName, true);
                    // 对于自动解锁的门，设置InGameOpening状态为true
                    setDoorInGameOpening(gameName, doorName, true);
                    plugin.getLogger().info("门 " + doorName + " 配置为自动解锁，设置游戏内开启状态为true");

                    // 同步更新关联的出生点状态
                    updateLinkedSpawnPointsForDoor(gameName, doorName, true);
                }
            }
        }
    }

    /**
     * 获取门的解锁价格
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 门的解锁价格，如果门不存在则返回-1
     */
    public int getDoorUnlockPrice(String gameName, String doorName) {
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors == null || !doors.containsKey(doorName)) {
            return -1;
        }

        Map<String, Object> doorInfo = doors.get(doorName);
        if (!(boolean) doorInfo.get("locked")) {
            return 0; // 自动解锁的门，价格为0
        }

        return (int) doorInfo.get("unlockPrice");
    }

    /**
     * 根据门解锁状态获取调整后的怪物生成分配
     *
     * @param gameName 游戏名称
     * @param round 当前回合
     * @return 调整后的怪物生成分配 (Map<生成点名称, 配置>)
     */
    public Map<String, Map<String, Object>> getAdjustedMonsterSpawnAllocation(String gameName, int round) {
        // 记录调整开始时间，用于调试
        long startTime = System.currentTimeMillis();
        plugin.getLogger().info("开始调整游戏 " + gameName + " 第 " + round + " 回合的怪物生成分配");

        // 从游戏配置中获取当前回合的生成模式
        Map<String, Map<String, Object>> originalModes = plugin.getGameManager().getRoundModes(gameName, round);
        if (originalModes == null || originalModes.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 第 " + round + " 回合没有设置生成模式");
            return new HashMap<>(); // 返回空映射
        }

        // 创建结果映射
        Map<String, Map<String, Object>> adjustedModes = new HashMap<>(originalModes);

        // 打印原始配置信息以便调试
        plugin.getLogger().info("游戏 " + gameName + " 第 " + round + " 回合原始生成配置：");
        for (String spawnName : adjustedModes.keySet()) {
            Map<String, Object> config = adjustedModes.get(spawnName);
            boolean isEnabled = config.containsKey("enabled") ? Boolean.TRUE.equals(config.get("enabled")) : true;
            plugin.getLogger().info("- 生成点[" + spawnName + "] 启用状态: " + isEnabled);
        }

        // 检查每个生成点是否受到门解锁的影响
        for (String spawnName : new ArrayList<>(adjustedModes.keySet())) {
            // 提取实际生成点名称（如果是组合键）
            String actualSpawnName = spawnName;
            if (spawnName.contains("_")) {
                // 修复：正确提取生成点名称
                // 格式应该是 "生成点名称_刷怪逻辑X"，其中生成点名称可能包含下划线
                // 例如：HOME_AN_刷怪逻辑1 -> HOME_AN，HOME_KM_刷怪逻辑1 -> HOME_KM
                int lastUnderscoreIndex = spawnName.lastIndexOf("_刷怪逻辑");
                if (lastUnderscoreIndex > 0) {
                    actualSpawnName = spawnName.substring(0, lastUnderscoreIndex);
                    plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                } else {
                    // 如果没有找到"_刷怪逻辑"模式，使用原来的逻辑作为备用
                    String[] parts = spawnName.split("_");
                    if (parts.length > 1) {
                        // 检查最后一部分是否是刷怪逻辑
                        String lastPart = parts[parts.length - 1];
                        if (lastPart.startsWith("刷怪逻辑")) {
                            // 重新组合除最后一部分外的所有部分
                            actualSpawnName = String.join("_", Arrays.copyOf(parts, parts.length - 1));
                            plugin.getLogger().info("从组合键 " + spawnName + " 提取生成点名称: " + actualSpawnName);
                        } else {
                            plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                        }
                    } else {
                        plugin.getLogger().warning("无法从组合键 " + spawnName + " 提取生成点名称，使用原始名称");
                    }
                }
            }

            Map<String, Object> spawnConfig = adjustedModes.get(spawnName);

            // 首先从GameManager获取生成点的实际enabled状态
            Map<String, Map<String, Object>> gameSpawns = plugin.getGameManager().getZombieSpawns(gameName);
            boolean actualSpawnEnabled = true; // 默认启用

            if (gameSpawns != null && gameSpawns.containsKey(actualSpawnName)) {
                Map<String, Object> spawnInfo = gameSpawns.get(actualSpawnName);
                actualSpawnEnabled = (Boolean) spawnInfo.getOrDefault("enabled", true);
                plugin.getLogger().info("生成点 " + actualSpawnName + " 在GameManager中的enabled状态: " + actualSpawnEnabled);
            }

            // 如果生成点在GameManager中被禁用，则在这里也禁用
            if (!actualSpawnEnabled) {
                spawnConfig.put("enabled", false);
                plugin.getLogger().info("生成点 " + spawnName + " 被禁用，因为在GameManager中被设置为禁用");
            } else {
                // 检查生成点是否需要门解锁
                if (spawnConfig.containsKey("requiredDoor")) {
                    String requiredDoor = (String) spawnConfig.get("requiredDoor");

                    // 检查门是否解锁
                    if (!isDoorUnlocked(gameName, requiredDoor)) {
                        // 门未解锁，禁用该生成点
                        spawnConfig.put("enabled", false);
                        plugin.getLogger().info("生成点 " + spawnName + " 被禁用，因为门 " + requiredDoor + " 未解锁");
                    }
                } // 检查生成点是否需要多个门解锁
                else if (spawnConfig.containsKey("requiredDoors") && spawnConfig.get("requiredDoors") instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> requiredDoors = (List<String>) spawnConfig.get("requiredDoors");

                    // 检查是否有任何未解锁的门
                    boolean allDoorsUnlocked = true;
                    for (String doorName : requiredDoors) {
                        if (!isDoorUnlocked(gameName, doorName)) {
                            allDoorsUnlocked = false;
                            plugin.getLogger().info("生成点 " + spawnName + " 需要的门 " + doorName + " 未解锁");
                            break;
                        }
                    }

                    if (!allDoorsUnlocked) {
                        // 如果有任何门未解锁，禁用该生成点
                        spawnConfig.put("enabled", false);
                        plugin.getLogger().info("生成点 " + spawnName + " 被禁用，因为有门未解锁");
                    }
                } // 检查生成点是否有关联的门
                else {
                    // 获取与该生成点关联的所有门
                    List<String> linkedDoors = getLinkedDoors(gameName, actualSpawnName);
                    if (!linkedDoors.isEmpty()) {
                        // 检查是否有任何关联的门已解锁
                        boolean anyDoorUnlocked = false;
                        for (String doorName : linkedDoors) {
                            boolean isUnlocked = isDoorUnlocked(gameName, doorName);
                            plugin.getLogger().info("生成点 " + actualSpawnName + " 关联的门 " + doorName + " 解锁状态: " + isUnlocked);
                            if (isUnlocked) {
                                anyDoorUnlocked = true;
                                break;
                            }
                        }

                        if (!anyDoorUnlocked) {
                            // 如果没有关联的门已解锁，禁用该生成点
                            spawnConfig.put("enabled", false);
                            plugin.getLogger().info("生成点 " + spawnName + " 被禁用，因为没有关联的门已解锁");
                        }
                    }
                }
            }
        }

        // 收集被禁用的生成点的刷怪逻辑
        Map<String, List<Map<String, Object>>> disabledSpawnLogics = new HashMap<>();

        // 首先检查哪些生成点被禁用，并收集它们的刷怪逻辑
        for (String spawnName : new ArrayList<>(adjustedModes.keySet())) {
            Map<String, Object> spawnConfig = adjustedModes.get(spawnName);
            boolean isEnabled = spawnConfig.containsKey("enabled") ? Boolean.TRUE.equals(spawnConfig.get("enabled")) : true;

            if (!isEnabled) {
                // 如果生成点被禁用，收集它的刷怪逻辑
                List<Map<String, Object>> logics = new ArrayList<>();

                // 检查是否有嵌套的刷怪逻辑
                if (spawnConfig instanceof Map) {
                    Map<String, Object> spawnConfigMap = (Map<String, Object>) spawnConfig;

                    // 检查嵌套的刷怪逻辑
                    for (String key : spawnConfigMap.keySet()) {
                        if (key.startsWith("刷怪逻辑") && spawnConfigMap.get(key) instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> logicData = (Map<String, Object>) spawnConfigMap.get(key);

                            // 检查是否包含必要的刷怪信息
                            if (logicData.containsKey("monsterType") && logicData.containsKey("monsterId")) {
                                logics.add(new HashMap<>(logicData));
                                plugin.getLogger().info("收集到禁用生成点 " + spawnName + " 的刷怪逻辑: " + key);
                            }
                        }
                    }

                    // 检查直接配置
                    if (spawnConfigMap.containsKey("monsterType") && spawnConfigMap.containsKey("monsterId")) {
                        Map<String, Object> directLogic = new HashMap<>(spawnConfigMap);
                        logics.add(directLogic);
                        plugin.getLogger().info("收集到禁用生成点 " + spawnName + " 的直接刷怪逻辑");
                    }
                }

                if (!logics.isEmpty()) {
                    disabledSpawnLogics.put(spawnName, logics);
                }
            }
        }

        // 检查是否有任何可用的生成点
        boolean anyEnabled = false;
        List<String> enabledSpawns = new ArrayList<>();
        for (String spawnName : adjustedModes.keySet()) {
            Map<String, Object> config = adjustedModes.get(spawnName);
            if (Boolean.TRUE.equals(config.get("enabled"))) {
                anyEnabled = true;
                enabledSpawns.add(spawnName);
            }
        }

        // 如果没有可用的生成点，检查是否需要特殊处理
        if (!anyEnabled) {
            plugin.getLogger().warning("游戏 " + gameName + " 第 " + round + " 回合没有可用的生成点");

            // 查找没有关联门的生成点，这些可以作为备用选择
            List<String> unrestrictedSpawns = new ArrayList<>();
            for (String spawnName : adjustedModes.keySet()) {
                // 提取实际生成点名称
                String actualSpawnName = spawnName;
                if (spawnName.contains("_")) {
                    actualSpawnName = spawnName.substring(0, spawnName.indexOf("_"));
                }

                // 检查是否有关联的门
                List<String> linkedDoors = getLinkedDoors(gameName, actualSpawnName);
                if (linkedDoors.isEmpty()) {
                    unrestrictedSpawns.add(spawnName);
                    plugin.getLogger().info("找到无门限制的生成点: " + spawnName);
                }
            }

            // 只有在有被禁用的刷怪逻辑需要重新分配时，才考虑启用无门限制的生成点
            // 但是要检查这些无门限制的生成点在配置文件中是否真的可用
            if (!disabledSpawnLogics.isEmpty() && !unrestrictedSpawns.isEmpty()) {
                plugin.getLogger().info("检测到被禁用的刷怪逻辑，检查无门限制的生成点是否可用");

                List<String> actuallyAvailableSpawns = new ArrayList<>();
                for (String spawnName : unrestrictedSpawns) {
                    // 提取实际生成点名称
                    String actualSpawnName = spawnName;
                    if (spawnName.contains("_")) {
                        actualSpawnName = spawnName.substring(0, spawnName.indexOf("_"));
                    }

                    // 检查该生成点在GameManager中的实际enabled状态
                    Map<String, Map<String, Object>> gameSpawns = plugin.getGameManager().getZombieSpawns(gameName);
                    if (gameSpawns != null && gameSpawns.containsKey(actualSpawnName)) {
                        Map<String, Object> spawnInfo = gameSpawns.get(actualSpawnName);
                        boolean actualSpawnEnabled = (Boolean) spawnInfo.getOrDefault("enabled", true);

                        if (actualSpawnEnabled) {
                            Map<String, Object> config = adjustedModes.get(spawnName);
                            config.put("enabled", true);
                            enabledSpawns.add(spawnName);
                            actuallyAvailableSpawns.add(spawnName);
                            plugin.getLogger().info("启用无门限制的生成点: " + spawnName + " (配置文件中enabled=true)");
                        } else {
                            plugin.getLogger().info("跳过无门限制的生成点: " + spawnName + " (配置文件中enabled=false)");
                        }
                    }
                }

                if (!actuallyAvailableSpawns.isEmpty()) {
                    anyEnabled = true;
                    plugin.getLogger().info("成功启用 " + actuallyAvailableSpawns.size() + " 个无门限制且配置可用的生成点");
                } else {
                    plugin.getLogger().warning("所有无门限制的生成点在配置文件中都被禁用");
                }
            } else if (unrestrictedSpawns.isEmpty()) {
                plugin.getLogger().warning("没有找到无门限制的生成点，玩家需要解锁门才能继续游戏");
            } else {
                plugin.getLogger().info("没有被禁用的刷怪逻辑需要重新分配，保持当前状态");
            }
        }

        // 修复：恢复被禁用的生成点的刷怪逻辑分配给可用的生成点
        // 这样可以确保游戏的连续性，玩家需要解锁门才能获得更多生成点
        if (!disabledSpawnLogics.isEmpty() && !enabledSpawns.isEmpty()) {
            plugin.getLogger().info("检测到被禁用的生成点的刷怪逻辑，将其分配给可用的生成点");

            // 计算每个可用生成点应该分配到的额外怪物数量
            redistributeDisabledSpawnLogics(adjustedModes, disabledSpawnLogics, enabledSpawns);

            // 记录被禁用的生成点信息，但不进行重新分配
            for (String disabledSpawn : disabledSpawnLogics.keySet()) {
                List<Map<String, Object>> logics = disabledSpawnLogics.get(disabledSpawn);
                plugin.getLogger().info("生成点 " + disabledSpawn + " 被禁用，包含 " + logics.size() + " 个刷怪逻辑");

                // 检查是否有关联的门
                String actualSpawnName = disabledSpawn;
                if (disabledSpawn.contains("_")) {
                    actualSpawnName = disabledSpawn.substring(0, disabledSpawn.indexOf("_"));
                }

                List<String> linkedDoors = getLinkedDoors(gameName, actualSpawnName);
                if (!linkedDoors.isEmpty()) {
                    StringBuilder doorNames = new StringBuilder();
                    for (String doorName : linkedDoors) {
                        doorNames.append(doorName).append("(").append(isDoorUnlocked(gameName, doorName) ? "已解锁" : "未解锁").append("), ");
                    }
                    plugin.getLogger().info("生成点 " + actualSpawnName + " 关联的门: " + doorNames.toString());
                } else {
                    plugin.getLogger().info("生成点 " + actualSpawnName + " 没有关联的门");
                }
            }
        }

        // 打印调整后的配置信息
        plugin.getLogger().info("游戏 " + gameName + " 第 " + round + " 回合调整后生成配置：");
        for (String spawnName : adjustedModes.keySet()) {
            Map<String, Object> config = adjustedModes.get(spawnName);
            boolean isEnabled = config.containsKey("enabled") ? Boolean.TRUE.equals(config.get("enabled")) : true;
            plugin.getLogger().info("- 生成点[" + spawnName + "] 调整后启用状态: " + isEnabled);
        }

        // 记录调整结束时间，用于调试
        long endTime = System.currentTimeMillis();
        plugin.getLogger().info("完成调整游戏 " + gameName + " 第 " + round + " 回合的怪物生成分配，耗时: " + (endTime - startTime) + "ms");

        return adjustedModes;
    }

    /**
     * 获取与生成点关联的门名称
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @return 关联的门名称列表，如果没有关联则返回空列表
     */
    public List<String> getLinkedDoors(String gameName, String spawnName) {
        List<String> result = new ArrayList<>();

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("doors")) {
            return result;
        }

        // 遍历所有门，查找关联了该生成点的门
        ConfigurationSection doorsSection = config.getConfigurationSection("doors");
        for (String doorName : doorsSection.getKeys(false)) {
            // 检查新的多生成点关联
            if (doorsSection.isList(doorName + ".linkedSpawns")) {
                List<String> linkedSpawns = doorsSection.getStringList(doorName + ".linkedSpawns");
                if (linkedSpawns.contains(spawnName)) {
                    result.add(doorName);
                }
            } // 兼容旧的单一生成点关联
            else if (doorsSection.isSet(doorName + ".linkedSpawn")) {
                String linkedSpawn = doorsSection.getString(doorName + ".linkedSpawn");
                if (spawnName.equals(linkedSpawn)) {
                    result.add(doorName);
                }
            }
        }

        return result;
    }

    // 下面的方法暂时未使用，注释掉以避免警告
    /*
    private String getLinkedDoor(String gameName, String spawnName) {
        List<String> doors = getLinkedDoors(gameName, spawnName);
        return doors.isEmpty() ? null : doors.get(0);
    }
     */
    /**
     * 检查生成点是否被锁定的门限制
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @return 如果生成点被锁定的门限制则返回true，否则返回false
     */
    public boolean isSpawnRestrictedByLockedDoor(String gameName, String spawnName) {
        // 获取与该生成点关联的所有门
        List<String> linkedDoors = getLinkedDoors(gameName, spawnName);

        // 如果没有关联的门，则不受限制
        if (linkedDoors.isEmpty()) {
            plugin.getLogger().info("生成点 " + spawnName + " 没有关联的门，不受限制");
            return false;
        }

        // 检查是否有任何关联的门已解锁
        boolean anyDoorUnlocked = false;
        for (String doorName : linkedDoors) {
            boolean isUnlocked = isDoorUnlocked(gameName, doorName);
            plugin.getLogger().info("生成点 " + spawnName + " 关联的门 " + doorName + " 解锁状态: " + isUnlocked);
            if (isUnlocked) {
                anyDoorUnlocked = true;
                break;
            }
        }

        // 如果所有关联的门都未解锁，则生成点受限制
        if (!anyDoorUnlocked) {
            plugin.getLogger().info("生成点 " + spawnName + " 的所有关联门都未解锁，生成点受限制");
            return true;
        } else {
            plugin.getLogger().info("生成点 " + spawnName + " 至少有一个关联门已解锁，生成点不受限制");
            return false;
        }
    }

    /**
     * 当门被解锁时，更新相关生成点的状态
     *
     * @param gameName 游戏名称
     * @param doorName 被解锁的门名称
     */
    private void updateSpawnPointsForUnlockedDoor(String gameName, String doorName) {
        plugin.getLogger().info("门 " + doorName + " 被解锁，正在更新相关生成点状态");

        // 获取所有生成点
        Map<String, Map<String, Object>> spawns = gameManager.getZombieSpawns(gameName);
        if (spawns == null || spawns.isEmpty()) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何生成点");
            return;
        }

        // 检查每个生成点，看是否与该门相关
        for (String spawnName : spawns.keySet()) {
            List<String> linkedDoors = getLinkedDoors(gameName, spawnName);

            // 如果该生成点与被解锁的门相关
            if (linkedDoors.contains(doorName)) {
                // 重新检查该生成点是否仍被限制
                boolean isRestricted = isSpawnRestrictedByLockedDoor(gameName, spawnName);

                if (!isRestricted) {
                    // 不再自动启用生成点，只记录日志
                    // 生成点的状态将在下次刷怪时通过getAdjustedMonsterSpawnAllocation方法动态判断
                    plugin.getLogger().info("生成点 " + spawnName + " 不再被限制，将在下次刷怪时生效");
                } else {
                    plugin.getLogger().info("生成点 " + spawnName + " 仍被其他锁定的门限制");
                }
            }
        }

        plugin.getLogger().info("已完成门 " + doorName + " 解锁后的生成点状态更新");
    }

    /**
     * 重新分配被禁用生成点的刷怪逻辑到可用生成点
     *
     * @param adjustedModes 调整后的生成模式
     * @param disabledSpawnLogics 被禁用的生成点逻辑
     * @param enabledSpawns 可用的生成点列表
     */
    private void redistributeDisabledSpawnLogics(Map<String, Map<String, Object>> adjustedModes,
                                                  Map<String, List<Map<String, Object>>> disabledSpawnLogics,
                                                  List<String> enabledSpawns) {

        plugin.getLogger().info("开始重新分配被禁用生成点的刷怪逻辑");

        // 统计所有被禁用生成点的怪物数量
        Map<String, Integer> totalDisabledCounts = new HashMap<>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : disabledSpawnLogics.entrySet()) {
            String disabledSpawn = entry.getKey();
            List<Map<String, Object>> logics = entry.getValue();

            for (Map<String, Object> logic : logics) {
                String monsterType = (String) logic.get("monsterType");
                String monsterId = (String) logic.get("monsterId");
                String countStr = (String) logic.get("count");

                try {
                    int count = Integer.parseInt(countStr);
                    String key = monsterType + "_" + monsterId;
                    totalDisabledCounts.put(key, totalDisabledCounts.getOrDefault(key, 0) + count);

                    plugin.getLogger().info("被禁用生成点 " + disabledSpawn + " 的怪物 " + key + " 数量: " + count);
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无法解析怪物数量: " + countStr);
                }
            }
        }

        // 将被禁用的怪物平均分配给可用的生成点
        int enabledSpawnCount = enabledSpawns.size();

        for (Map.Entry<String, Integer> entry : totalDisabledCounts.entrySet()) {
            String monsterKey = entry.getKey();
            int totalCount = entry.getValue();

            String[] parts = monsterKey.split("_");
            if (parts.length != 2) {
                continue;
            }

            String monsterType = parts[0];
            String monsterId = parts[1];

            // 计算每个可用生成点应该分配的数量
            int baseCount = totalCount / enabledSpawnCount;
            int remainder = totalCount % enabledSpawnCount;

            plugin.getLogger().info("重新分配怪物 " + monsterKey + ": 总数 " + totalCount + ", 每个生成点基础数量 " + baseCount + ", 余数 " + remainder);

            // 为每个可用生成点添加怪物
            for (int i = 0; i < enabledSpawns.size(); i++) {
                String enabledSpawn = enabledSpawns.get(i);
                int assignedCount = baseCount;

                // 将余数分配给前几个生成点
                if (i < remainder) {
                    assignedCount++;
                }

                if (assignedCount > 0) {
                    // 查找或创建该生成点的怪物配置
                    String spawnKey = enabledSpawn + "_" + monsterType + "_" + monsterId;

                    if (adjustedModes.containsKey(spawnKey)) {
                        // 如果已存在，增加数量
                        Map<String, Object> existingConfig = adjustedModes.get(spawnKey);
                        String existingCountStr = (String) existingConfig.get("count");
                        try {
                            int existingCount = Integer.parseInt(existingCountStr);
                            int newCount = existingCount + assignedCount;
                            existingConfig.put("count", String.valueOf(newCount));

                            plugin.getLogger().info("增加生成点 " + enabledSpawn + " 的怪物 " + monsterKey + " 数量: " + existingCount + " -> " + newCount);
                        } catch (NumberFormatException e) {
                            plugin.getLogger().warning("无法解析现有怪物数量: " + existingCountStr);
                        }
                    } else {
                        // 如果不存在，创建新的配置
                        Map<String, Object> newConfig = new HashMap<>();
                        newConfig.put("monsterType", monsterType);
                        newConfig.put("monsterId", monsterId);
                        newConfig.put("count", String.valueOf(assignedCount));

                        // 重要修复：不要强制设置enabled=true，而是继承原始生成点的enabled状态
                        // 检查原始生成点的enabled状态
                        boolean originalEnabled = true; // 默认值
                        if (adjustedModes.containsKey(enabledSpawn)) {
                            Map<String, Object> originalConfig = adjustedModes.get(enabledSpawn);
                            originalEnabled = originalConfig.containsKey("enabled") ?
                                Boolean.TRUE.equals(originalConfig.get("enabled")) : true;
                        }
                        newConfig.put("enabled", originalEnabled);

                        adjustedModes.put(spawnKey, newConfig);

                        plugin.getLogger().info("为生成点 " + enabledSpawn + " 创建新的怪物配置 " + monsterKey + " 数量: " + assignedCount + ", enabled状态: " + originalEnabled);
                    }
                }
            }
        }

        plugin.getLogger().info("已完成被禁用生成点的刷怪逻辑重新分配");
    }

    /**
     * 更新门关联的出生点状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param enabled 是否启用关联的出生点
     */
    private void updateLinkedSpawnPointsForDoor(String gameName, String doorName, boolean enabled) {
        plugin.getLogger().info("更新门 " + doorName + " 关联的出生点状态为: " + enabled);

        // 获取与该门关联的所有出生点
        List<String> linkedSpawns = getLinkedSpawns(gameName, doorName);

        if (linkedSpawns.isEmpty()) {
            plugin.getLogger().info("门 " + doorName + " 没有关联的出生点");
            return;
        }

        // 更新每个关联出生点的状态
        for (String spawnName : linkedSpawns) {
            boolean setResult = gameManager.setZombieSpawnEnabled(gameName, spawnName, enabled);
            plugin.getLogger().info("将出生点 " + spawnName + " 状态设置为 " + enabled + ": " + (setResult ? "成功" : "失败"));
        }
    }

    /**
     * 获取门关联的出生点列表
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 关联的出生点列表
     */
    private List<String> getLinkedSpawns(String gameName, String doorName) {
        List<String> linkedSpawns = new ArrayList<>();

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return linkedSpawns;
        }

        String doorPath = "doors." + doorName;

        // 检查新的多生成点关联格式
        if (config.isList(doorPath + ".linkedSpawns")) {
            linkedSpawns.addAll(config.getStringList(doorPath + ".linkedSpawns"));
        }
        // 检查旧的单一生成点关联格式
        else if (config.isSet(doorPath + ".linkedSpawn")) {
            String linkedSpawn = config.getString(doorPath + ".linkedSpawn");
            if (linkedSpawn != null && !linkedSpawn.isEmpty()) {
                linkedSpawns.add(linkedSpawn);
            }
        }

        return linkedSpawns;
    }
}
