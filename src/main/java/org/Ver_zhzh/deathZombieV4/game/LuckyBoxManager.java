package org.Ver_zhzh.deathZombieV4.game;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

/**
 * 幸运箱管理器 负责管理游戏中的幸运箱系统，包括设置、激活、抽奖等功能
 */
public class LuckyBoxManager implements Listener {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final ShootPluginHelper shootHelper;
    private final Random random;

    // 存储正在进行抽奖的玩家: <玩家UUID, 抽奖任务>
    private final Map<UUID, BukkitTask> activeDrawings;

    // 存储幸运箱的悬浮文字: <游戏名_箱子ID, ArmorStand>
    private final Map<String, ArmorStand> luckyBoxHolograms;

    // 存储玩家的奖励武器: <全息图ID, 武器ID>
    private final Map<String, String> pendingRewards = new HashMap<>();

    // 存储全息图位置信息: <全息图ID, 位置>
    private final Map<String, Location> hologramLocations = new HashMap<>();

    // 存储倒计时任务: <游戏名_箱子ID, BukkitTask>
    private final Map<String, BukkitTask> rewardTimers = new HashMap<>();

    public LuckyBoxManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.shootHelper = plugin.getShootPluginHelper();
        this.random = new Random();
        this.activeDrawings = new HashMap<>();
        this.luckyBoxHolograms = new HashMap<>();
    }

    /**
     * 添加幸运箱到游戏配置
     *
     * @param gameName 游戏名称
     * @param location 箱子位置
     * @param type 箱子类型 (local/random)
     * @param openRound 开启回合
     * @param openCost 开启金钱
     * @return 是否添加成功
     */
    public boolean addLuckyBox(String gameName, Location location, String type, int openRound, int openCost) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

            // 获取下一个可用的ID
            int nextId = getNextLuckyBoxId(gameConfig);
            String boxId = "id" + nextId;

            // 设置幸运箱配置
            String boxPath = "luckyBoxes." + boxId;
            gameConfig.set(boxPath + ".world", location.getWorld().getName());
            gameConfig.set(boxPath + ".x", location.getX());
            gameConfig.set(boxPath + ".y", location.getY());
            gameConfig.set(boxPath + ".z", location.getZ());
            gameConfig.set(boxPath + ".type", type);
            gameConfig.set(boxPath + ".openRound", openRound);
            gameConfig.set(boxPath + ".openCost", openCost);
            gameConfig.set(boxPath + ".enabled", true);

            // 保存配置
            gameConfig.save(gameFile);

            plugin.getLogger().info("成功添加幸运箱 " + boxId + " 到游戏 " + gameName
                    + " (类型: " + type + ", 开启回合: " + openRound + ", 消耗金钱: " + openCost + ")");

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存幸运箱配置时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置现有幸运箱的位置
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @param location 新位置
     * @return 是否设置成功
     */
    public boolean setLuckyBoxLocation(String gameName, String boxId, Location location) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);
            String boxPath = "luckyBoxes." + boxId;

            if (!gameConfig.isSet(boxPath)) {
                plugin.getLogger().warning("幸运箱 " + boxId + " 不存在于游戏 " + gameName + " 中");
                return false;
            }

            // 更新位置
            gameConfig.set(boxPath + ".world", location.getWorld().getName());
            gameConfig.set(boxPath + ".x", location.getX());
            gameConfig.set(boxPath + ".y", location.getY());
            gameConfig.set(boxPath + ".z", location.getZ());

            // 保存配置
            gameConfig.save(gameFile);

            plugin.getLogger().info("成功设置幸运箱 " + boxId + " 的位置在游戏 " + gameName + " 中");

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存幸运箱位置时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取下一个可用的幸运箱ID
     *
     * @param gameConfig 游戏配置
     * @return 下一个可用的ID
     */
    private int getNextLuckyBoxId(FileConfiguration gameConfig) {
        ConfigurationSection luckyBoxesSection = gameConfig.getConfigurationSection("luckyBoxes");
        if (luckyBoxesSection == null) {
            return 1;
        }

        int maxId = 0;
        for (String key : luckyBoxesSection.getKeys(false)) {
            if (key.startsWith("id")) {
                try {
                    int id = Integer.parseInt(key.substring(2));
                    maxId = Math.max(maxId, id);
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }

        return maxId + 1;
    }

    /**
     * 激活游戏中的幸运箱
     *
     * @param gameName 游戏名称
     * @param currentRound 当前回合
     */
    public void activateLuckyBoxes(String gameName, int currentRound) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return;
        }

        ConfigurationSection luckyBoxesSection = gameConfig.getConfigurationSection("luckyBoxes");
        if (luckyBoxesSection == null) {
            return;
        }

        // 收集所有可激活的箱子
        List<String> localBoxes = new ArrayList<>();
        List<String> randomBoxes = new ArrayList<>();

        for (String boxId : luckyBoxesSection.getKeys(false)) {
            ConfigurationSection boxSection = luckyBoxesSection.getConfigurationSection(boxId);
            if (boxSection == null) {
                continue;
            }

            int openRound = boxSection.getInt("openRound", 1);
            boolean enabled = boxSection.getBoolean("enabled", true);
            String type = boxSection.getString("type", "local");

            if (enabled && currentRound >= openRound) {
                if ("local".equals(type)) {
                    localBoxes.add(boxId);
                } else if ("random".equals(type)) {
                    randomBoxes.add(boxId);
                }
            }
        }

        // 激活所有local类型的箱子
        for (String boxId : localBoxes) {
            activateLuckyBox(gameName, boxId);
        }

        // 从random类型的箱子中随机选择一个激活
        if (!randomBoxes.isEmpty()) {
            String selectedBox = randomBoxes.get(random.nextInt(randomBoxes.size()));
            activateLuckyBox(gameName, selectedBox);
        }
    }

    /**
     * 激活单个幸运箱
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     */
    private void activateLuckyBox(String gameName, String boxId) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return;
        }

        String boxPath = "luckyBoxes." + boxId;
        ConfigurationSection boxSection = gameConfig.getConfigurationSection(boxPath);
        if (boxSection == null) {
            return;
        }

        // 获取箱子位置
        String worldName = boxSection.getString("world");
        double x = boxSection.getDouble("x");
        double y = boxSection.getDouble("y");
        double z = boxSection.getDouble("z");
        int openCost = boxSection.getInt("openCost", 1000);

        if (worldName == null || Bukkit.getWorld(worldName) == null) {
            plugin.getLogger().warning("幸运箱 " + boxId + " 的世界 " + worldName + " 不存在");
            return;
        }

        Location location = new Location(Bukkit.getWorld(worldName), x, y, z);

        // 在箱子位置放置箱子方块
        Block block = location.getBlock();
        block.setType(Material.CHEST);

        // 创建悬浮文字
        createLuckyBoxHologram(gameName, boxId, location, openCost);

        plugin.getLogger().info("激活幸运箱 " + boxId + " 在游戏 " + gameName + " 中");
    }

    /**
     * 创建幸运箱悬浮文字（统一管理）
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @param location 位置
     * @param openCost 开启费用
     */
    private void createLuckyBoxHologram(String gameName, String boxId, Location location, int openCost) {
        // 在箱子上方创建悬浮文字，调整位置到方块中心上方
        Location hologramLocation = location.clone().add(0.5, 1.5, 0.5);

        // 检查是否是大箱子的一部分
        Block block = location.getBlock();
        if (block.getType() == Material.CHEST || block.getType() == Material.TRAPPED_CHEST) {
            org.bukkit.block.data.type.Chest chestData = (org.bukkit.block.data.type.Chest) block.getBlockData();

            if (chestData.getType() != org.bukkit.block.data.type.Chest.Type.SINGLE) {
                // 对于大箱子，我们需要确定确切的中心位置
                org.bukkit.block.data.type.Chest.Type chestType = chestData.getType();
                org.bukkit.block.data.BlockData adjacentBlockData = null;
                Location adjacentLocation = null;

                // 根据箱子朝向获取另一半箱子位置
                switch (chestData.getFacing()) {
                    case NORTH:
                        if (chestType == org.bukkit.block.data.type.Chest.Type.LEFT) {
                            adjacentLocation = location.clone().add(1, 0, 0);
                        } else { // RIGHT
                            adjacentLocation = location.clone().add(-1, 0, 0);
                        }
                        break;
                    case SOUTH:
                        if (chestType == org.bukkit.block.data.type.Chest.Type.LEFT) {
                            adjacentLocation = location.clone().add(-1, 0, 0);
                        } else { // RIGHT
                            adjacentLocation = location.clone().add(1, 0, 0);
                        }
                        break;
                    case EAST:
                        if (chestType == org.bukkit.block.data.type.Chest.Type.LEFT) {
                            adjacentLocation = location.clone().add(0, 0, 1);
                        } else { // RIGHT
                            adjacentLocation = location.clone().add(0, 0, -1);
                        }
                        break;
                    case WEST:
                        if (chestType == org.bukkit.block.data.type.Chest.Type.LEFT) {
                            adjacentLocation = location.clone().add(0, 0, -1);
                        } else { // RIGHT
                            adjacentLocation = location.clone().add(0, 0, 1);
                        }
                        break;
                    default:
                        // 未预期的情况，使用默认位置
                        break;
                }

                if (adjacentLocation != null) {
                    // 计算两个箱子中间的位置
                    hologramLocation = new Location(
                            location.getWorld(),
                            (location.getX() + adjacentLocation.getX()) / 2 + 0.5,
                            location.getY() + 1.5,
                            (location.getZ() + adjacentLocation.getZ()) / 2 + 0.5
                    );

                    plugin.getLogger().info("大箱子检测 - 创建全息图位置: "
                            + hologramLocation.getX() + ", "
                            + hologramLocation.getY() + ", "
                            + hologramLocation.getZ());
                }
            }
        }

        // 创建多行悬浮文字（使用多个ArmorStand实现每个元素单独一行）

        // 第一行：幸运箱标题（位置较高）
        ArmorStand titleHologram = (ArmorStand) location.getWorld().spawnEntity(hologramLocation.clone().add(0, 0.4, 0), EntityType.ARMOR_STAND);
        titleHologram.setVisible(false);
        titleHologram.setGravity(false);
        titleHologram.setCustomName(ChatColor.LIGHT_PURPLE + "幸运箱");
        titleHologram.setCustomNameVisible(true);
        titleHologram.setMarker(true);
        titleHologram.setSmall(true);

        // 第二行：金钱信息（位置较低，与标题间距0.4格）
        ArmorStand costHologram = (ArmorStand) location.getWorld().spawnEntity(hologramLocation.clone().add(0, -0.4, 0), EntityType.ARMOR_STAND);
        costHologram.setVisible(false);
        costHologram.setGravity(false);
        costHologram.setCustomName(ChatColor.YELLOW + "" + openCost + " 金钱");
        costHologram.setCustomNameVisible(true);
        costHologram.setMarker(true);
        costHologram.setSmall(true);

        // 存储悬浮文字引用（存储主要的标题悬浮文字，用于状态更新）
        String key = gameName + "_" + boxId;
        luckyBoxHolograms.put(key, titleHologram);

        // 存储金钱悬浮文字引用
        String costKey = gameName + "_" + boxId + "_cost";
        luckyBoxHolograms.put(costKey, costHologram);

        plugin.getLogger().info("创建统一幸运箱悬浮文字: " + key + ", 费用: " + openCost);
    }

    /**
     * 处理玩家与幸运箱的交互
     *
     * @param player 玩家
     * @param location 箱子位置
     * @param gameName 游戏名称
     * @return 是否成功处理交互
     */
    public boolean handleLuckyBoxInteraction(Player player, Location location, String gameName) {
        // 检查是否是幸运箱
        String boxId = findLuckyBoxAt(gameName, location);
        if (boxId == null) {
            return false;
        }

        // 首先检查这个箱子是否有待领取的奖励
        String rewardKey = gameName + "_" + boxId + "_reward";
        if (pendingRewards.containsKey(rewardKey)) {
            // 有待领取的奖励，处理奖励领取
            String weaponId = pendingRewards.get(rewardKey);
            if (handleRewardClaim(player, weaponId, gameName, boxId)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 通过箱子交互获取了奖励: " + weaponId);
                return true;
            }
        }

        // 检查玩家是否正在抽奖
        if (activeDrawings.containsKey(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "你正在进行抽奖，请等待完成！");
            return true;
        }

        // 检查玩家是否有未获取的奖励
        if (hasPlayerPendingReward(player)) {
            player.sendMessage(ChatColor.RED + "你还有未获取的奖励！请先获取之前的奖励再开启新的幸运箱。");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return true;
        }

        // 获取箱子配置
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return false;
        }

        String boxPath = "luckyBoxes." + boxId;
        ConfigurationSection boxSection = gameConfig.getConfigurationSection(boxPath);
        if (boxSection == null) {
            return false;
        }

        // 检查开启回合要求
        int requiredRound = boxSection.getInt("openRound", 1);
        int currentRound = plugin.getGameSessionManager().getGameRound(gameName);

        if (currentRound < requiredRound) {
            player.sendMessage(ChatColor.RED + "这个幸运箱要到第 " + requiredRound + " 回合才能开启！当前回合: " + currentRound);
            return true;
        }

        int openCost = boxSection.getInt("openCost", 1000);

        // 检查玩家金钱
        double playerMoney = shootHelper.getPlayerMoney(player);
        if (playerMoney < openCost) {
            player.sendMessage(ChatColor.RED + "你的金钱不足！需要 " + openCost + " 金钱，你只有 " + (int) playerMoney + " 金钱。");
            return true;
        }

        // 扣除金钱
        if (!shootHelper.subtractPlayerMoney(player, openCost)) {
            player.sendMessage(ChatColor.RED + "扣除金钱失败！");
            return true;
        }

        // 开始抽奖
        startLuckyBoxDrawing(player, gameName, boxId);

        return true;
    }

    /**
     * 处理奖励领取
     *
     * @param player 玩家
     * @param weaponId 武器ID
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @return 是否成功领取
     */
    private boolean handleRewardClaim(Player player, String weaponId, String gameName, String boxId) {
        // 检查玩家是否有空的武器槽位或手持武器可替换
        int slotStatus = getAvailableWeaponSlot(player, weaponId);

        if (slotStatus == -1) {
            // 没有空槽位也没有手持武器，无法获取新武器
            player.sendMessage(ChatColor.RED + "你的武器槽位已满! 请先清理背包或手持一把武器来替换.");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return false;
        }

        if (slotStatus == -3) {
            // 玩家身上有相同武器但没有手持，需要先切换
            String weaponName = getWeaponName(weaponId);
            player.sendMessage(ChatColor.RED + "你的背包中已有 " + ChatColor.GOLD + weaponName + ChatColor.RED + "！");
            player.sendMessage(ChatColor.YELLOW + "请先手持该武器，然后再获取奖励以补充弹药。");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return false;
        }

        boolean success = false;
        String weaponName = getWeaponName(weaponId);

        if (slotStatus == -2) {
            // 手持相同武器，补充弹药
            int heldSlot = player.getInventory().getHeldItemSlot();
            if (heldSlot >= 1 && heldSlot <= 4) { // 只允许在槽位2-5（索引1-4）
                // 调用Shoot插件的补充弹药方法（会验证武器匹配）
                success = shootHelper.replenishPlayerAmmo(player, weaponId);

                if (success) {
                    player.sendMessage(ChatColor.GREEN + "已为你的 " + ChatColor.GOLD + weaponName + ChatColor.GREEN + " 补充弹药！");
                } else {
                    // 如果补充弹药失败（可能是武器不匹配），尝试替换武器
                    plugin.getLogger().warning("弹药补充失败，尝试替换武器");
                    player.getInventory().setItem(heldSlot, null);
                    success = shootHelper.giveGunToPlayer(player, weaponId);
                    if (success) {
                        player.sendMessage(ChatColor.GREEN + "已替换手持武器为: " + ChatColor.GOLD + weaponName);
                    } else {
                        player.sendMessage(ChatColor.RED + "给予武器失败，请稍后再试！");
                    }
                }
            } else {
                player.sendMessage(ChatColor.RED + "请手持一把可替换的武器（槽位2-5）.");
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                return false;
            }
        } else if (slotStatus == -4) {
            // 手持其他武器，替换为新武器
            int heldSlot = player.getInventory().getHeldItemSlot();
            if (heldSlot >= 1 && heldSlot <= 4) { // 只允许在槽位2-5（索引1-4）
                // 清除手持的武器，替换为新武器
                player.getInventory().setItem(heldSlot, null);
                success = shootHelper.giveGunToPlayer(player, weaponId);
                if (success) {
                    player.sendMessage(ChatColor.GREEN + "已替换手持武器为: " + ChatColor.GOLD + weaponName);
                }
            } else {
                player.sendMessage(ChatColor.RED + "请手持一把可替换的武器（槽位2-5）.");
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                return false;
            }
        } else {
            // 有空槽位，直接给予武器
            success = shootHelper.giveGunToPlayer(player, weaponId);
            if (success) {
                player.sendMessage(ChatColor.GREEN + "成功获得武器: " + ChatColor.GOLD + weaponName);
            }
        }

        if (success) {
            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 武器 " + weaponId + "，开始清理和恢复...");

            // 播放获取音效
            player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 0.8f, 1.2f);

            // 移除所有相关的奖励记录
            String rewardKey = gameName + "_" + boxId + "_reward";
            if (pendingRewards.containsKey(rewardKey)) {
                pendingRewards.remove(rewardKey);
                plugin.getLogger().info("已移除奖励记录: " + rewardKey);
            }

            // 取消倒计时任务（防止继续更新悬浮文字）
            String timerKey = gameName + "_" + boxId;
            BukkitTask timerTask = rewardTimers.remove(timerKey);
            if (timerTask != null) {
                timerTask.cancel();
                plugin.getLogger().info("已取消倒计时任务: " + timerKey);
            }

            // 立即清理玩家的所有抽奖全息图（防止残留）
            cleanupPlayerDrawingHolograms(player);

            // 查找并移除对应的全息图ID记录
            int removedCount = 0;
            for (Map.Entry<String, String> entry : new HashMap<>(pendingRewards).entrySet()) {
                if (entry.getValue().equals(weaponId) && entry.getKey().startsWith("lucky_box_drawing_")) {
                    String hologramId = entry.getKey();
                    pendingRewards.remove(hologramId);

                    // 尝试移除全息图
                    try {
                        if (eu.decentsoftware.holograms.api.DHAPI.getHologram(hologramId) != null) {
                            eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
                            plugin.getLogger().info("玩家获取奖励后已移除抽奖全息图: " + hologramId);
                            removedCount++;
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("移除全息图失败: " + e.getMessage());
                    }

                    // 清理全息图位置缓存
                    hologramLocations.remove(hologramId);
                }
            }
            plugin.getLogger().info("共移除了 " + removedCount + " 个抽奖全息图");

            // 恢复幸运箱原始状态
            resetLuckyBox(gameName, boxId);

            plugin.getLogger().info("玩家 " + player.getName() + " 获取武器 " + weaponId + " 的处理已完成");
            return true;
        } else {
            plugin.getLogger().warning("给予玩家 " + player.getName() + " 武器 " + weaponId + " 失败");
            player.sendMessage(ChatColor.RED + "给予武器失败，请稍后再试!");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return false;
        }
    }

    /**
     * 查找指定位置的幸运箱ID
     *
     * @param gameName 游戏名称
     * @param location 位置
     * @return 幸运箱ID，如果不存在则返回null
     */
    private String findLuckyBoxAt(String gameName, Location location) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return null;
        }

        ConfigurationSection luckyBoxesSection = gameConfig.getConfigurationSection("luckyBoxes");
        if (luckyBoxesSection == null) {
            return null;
        }

        // 检查点击的方块是否是大箱子的一部分
        Block clickedBlock = location.getBlock();
        if (clickedBlock.getType() == Material.CHEST || clickedBlock.getType() == Material.TRAPPED_CHEST) {
            org.bukkit.block.Chest chest = (org.bukkit.block.Chest) clickedBlock.getState();
            org.bukkit.block.data.type.Chest chestData = (org.bukkit.block.data.type.Chest) clickedBlock.getBlockData();

            // 如果是大箱子，获取另一半的位置
            if (chestData.getType() != org.bukkit.block.data.type.Chest.Type.SINGLE) {
                // 这是大箱子的一部分，需要检查两个位置
                org.bukkit.inventory.InventoryHolder holder = chest.getInventory().getHolder();
                if (holder instanceof org.bukkit.block.DoubleChest) {
                    org.bukkit.block.DoubleChest doubleChest = (org.bukkit.block.DoubleChest) holder;
                    org.bukkit.block.Chest leftChest = (org.bukkit.block.Chest) doubleChest.getLeftSide();
                    org.bukkit.block.Chest rightChest = (org.bukkit.block.Chest) doubleChest.getRightSide();

                    if (leftChest != null) {
                        String boxId = checkBoxLocation(luckyBoxesSection, leftChest.getLocation());
                        if (boxId != null) {
                            return boxId;
                        }
                    }

                    if (rightChest != null) {
                        String boxId = checkBoxLocation(luckyBoxesSection, rightChest.getLocation());
                        if (boxId != null) {
                            return boxId;
                        }
                    }
                }
            }
        }

        // 原始的位置检查作为备用
        return checkBoxLocation(luckyBoxesSection, location);
    }

    /**
     * 检查指定位置是否与配置中的幸运箱位置匹配
     *
     * @param luckyBoxesSection 幸运箱配置部分
     * @param location 要检查的位置
     * @return 匹配的幸运箱ID，如果不匹配则返回null
     */
    private String checkBoxLocation(ConfigurationSection luckyBoxesSection, Location location) {
        for (String boxId : luckyBoxesSection.getKeys(false)) {
            ConfigurationSection boxSection = luckyBoxesSection.getConfigurationSection(boxId);
            if (boxSection == null) {
                continue;
            }

            String worldName = boxSection.getString("world");
            double x = boxSection.getDouble("x");
            double y = boxSection.getDouble("y");
            double z = boxSection.getDouble("z");

            if (worldName != null && worldName.equals(location.getWorld().getName())
                    && Math.abs(x - location.getX()) < 1
                    && Math.abs(y - location.getY()) < 1
                    && Math.abs(z - location.getZ()) < 1) {
                return boxId;
            }
        }

        return null;
    }

    /**
     * 开始幸运箱抽奖
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     */
    private void startLuckyBoxDrawing(Player player, String gameName, String boxId) {
        player.sendMessage(ChatColor.GREEN + "开始抽奖！");

        // 获取箱子位置
        Location boxLocation = getBoxLocation(gameName, boxId);
        if (boxLocation == null) {
            player.sendMessage(ChatColor.RED + "无法获取箱子位置！");
            return;
        }

        // 获取可抽取的武器列表
        List<String> availableWeapons = getAvailableWeapons();
        if (availableWeapons.isEmpty()) {
            player.sendMessage(ChatColor.RED + "没有可抽取的武器！");
            return;
        }

        // 创建抽奖全息图的位置（箱子上方3.0格，避免与ArmorStand重叠）
        Location hologramLocation = boxLocation.clone().add(0.5, 3.0, 0.5);

        // 检查是否支持DecentHolograms
        if (plugin.isDecentHologramsAvailable()) {
            // 使用DecentHolograms创建抽奖效果
            createDrawingHologramWithDecentHolograms(player, hologramLocation, availableWeapons, gameName, boxId);
        } else {
            // 使用Title显示方式（备用方案）
            createDrawingWithTitle(player, availableWeapons);
        }
    }

    /**
     * 使用DecentHolograms创建抽奖全息图效果
     *
     * @param player 玩家
     * @param location 全息图位置
     * @param availableWeapons 可用武器列表
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     */
    private void createDrawingHologramWithDecentHolograms(Player player, Location location, List<String> availableWeapons, String gameName, String boxId) {
        // 获取持续时间和切换速度
        int duration = plugin.getConfig().getInt("luckyBox.display.duration", 5);
        int switchSpeed = plugin.getConfig().getInt("luckyBox.display.switch_speed", 3);

        // 创建全息图ID
        String hologramId = "lucky_box_drawing_" + player.getUniqueId().toString();

        // 强制清理所有可能的残留全息图（防止重复显示）
        cleanupPlayerDrawingHolograms(player);

        // 删除可能存在的同ID全息图
        try {
            if (eu.decentsoftware.holograms.api.DHAPI.getHologram(hologramId) != null) {
                eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
                plugin.getLogger().info("已删除旧的抽奖全息图: " + hologramId);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("尝试删除旧全息图时出错: " + e.getMessage());
        }

        try {
            // 直接使用传入的location，已经调整为箱子上方3.0格的位置
            Location hologramLocation = location.clone();

            // 创建基本的抽奖全息图结构（抽奖过程中不显示金钱和名称）
            List<String> lines = new ArrayList<>();

            // 设置标题 - 使用金色"幸运箱"
            lines.add(ChatColor.GOLD + "※ 幸运箱 ※");

            // 使用DHAPI格式显示物品 - 第一个武器
            String firstWeapon = availableWeapons.get(0);
            String itemMaterial = getWeaponMaterialName(firstWeapon);
            lines.add("#ICON: " + itemMaterial);

            // 添加武器名称（与物品图标相连）
            String weaponName = getWeaponName(firstWeapon);
            lines.add(ChatColor.GREEN + weaponName);

            // 抽奖过程中不显示金钱信息，添加空行保持结构
            lines.add("");

            // 创建全息图
            eu.decentsoftware.holograms.api.DHAPI.createHologram(hologramId, hologramLocation, lines);
            plugin.getLogger().info("成功创建抽奖全息图，物品材质: " + itemMaterial);

            // 存储全息图位置信息
            hologramLocations.put(hologramId, hologramLocation);

            // 播放开始音效
            player.playSound(location, Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.2f);

            // 开始执行抽奖动画
            startDrawingAnimation(player, hologramLocation, availableWeapons, gameName, boxId, hologramId);

        } catch (Exception e) {
            plugin.getLogger().severe("创建抽奖全息图失败: " + e.getMessage());
            e.printStackTrace();

            // 回退到使用标题显示
            player.sendMessage(ChatColor.RED + "全息图显示失败，将使用标题显示抽奖效果");
            createDrawingWithTitle(player, availableWeapons);
        }
    }

    /**
     * 获取箱子的开启费用
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @return 费用文本
     */
    private String getOpenCost(String gameName, String boxId) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig != null) {
            String boxPath = "luckyBoxes." + boxId;
            ConfigurationSection boxSection = gameConfig.getConfigurationSection(boxPath);
            if (boxSection != null) {
                return String.valueOf(boxSection.getInt("openCost", 1000));
            }
        }
        return "1000";
    }

    /**
     * 开始执行抽奖动画效果
     */
    private void startDrawingAnimation(Player player, Location location, List<String> availableWeapons,
            String gameName, String boxId, String hologramId) {
        // 获取持续时间和切换速度
        int duration = plugin.getConfig().getInt("luckyBox.display.duration", 5);
        int switchSpeed = Math.max(1, plugin.getConfig().getInt("luckyBox.display.switch_speed", 3));

        // 保存原始幸运箱全息图引用，用于后续恢复
        String originalHologramKey = gameName + "_" + boxId;
        ArmorStand originalHologram = luckyBoxHolograms.get(originalHologramKey);

        // 修改原始全息图文本为抽奖中状态（不显示金钱和名称）
        if (originalHologram != null) {
            // 更改为抽奖中状态显示，只显示抽奖状态
            originalHologram.setCustomName(ChatColor.LIGHT_PURPLE + "※ 抽奖中 ※");
            plugin.getLogger().info("已更新幸运箱悬浮文字为抽奖中状态: " + originalHologramKey);
        } else {
            plugin.getLogger().warning("未找到原始幸运箱悬浮文字: " + originalHologramKey);
        }

        // 隐藏金钱悬浮文字（抽奖过程中不显示金钱）
        String costKey = gameName + "_" + boxId + "_cost";
        ArmorStand costHologram = luckyBoxHolograms.get(costKey);
        if (costHologram != null) {
            costHologram.setCustomNameVisible(false);
            plugin.getLogger().info("已隐藏金钱悬浮文字: " + costKey);
        }

        // 创建抽奖任务
        BukkitTask drawingTask = new BukkitRunnable() {
            private int tickCount = 0;
            private final int totalTicks = duration * 20; // 转换为tick
            private int currentWeaponIndex = 0;
            private final int switchTicks = Math.max(1, switchSpeed); // 确保至少为1
            private String finalWeapon = null;
            private String finalWeaponName = null;

            // 旋转和显示相关变量
            private double angle = 0;
            private double heightOffset = 0;
            private final double rotationSpeed = 0.2;
            private final double wobbleSpeed = 0.1;
            private final double wobbleAmount = 0.15;
            private double circleRadius = 0.2;

            // 物品翻转动画相关变量
            private double flipAngle = 0;
            private double scaleY = 1.0;
            private boolean isFlipping = false;
            private int flipDuration = 0;
            private String nextWeapon = null; // 下一个要显示的武器

            // 从配置文件获取翻转动画设置
            private final boolean flipEnabled = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enabled", true);
            private final int maxFlipDuration = plugin.getConfig().getInt("luckyBox.display.flip_animation.duration", 10);
            private final double flipSpeed = plugin.getConfig().getDouble("luckyBox.display.flip_animation.speed", 0.3);
            private final double flipHeightEffect = plugin.getConfig().getDouble("luckyBox.display.flip_animation.height_effect", 0.3);
            private final boolean flipParticles = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.particles", true);

            // 增强效果设置
            private final boolean enhancedRotation = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enhanced_effects.enhanced_rotation", true);
            private final boolean dynamicRadius = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enhanced_effects.dynamic_radius", true);
            private final boolean spiralParticles = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enhanced_effects.spiral_particles", true);
            private final boolean colorTransition = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enhanced_effects.color_transition", true);
            private final boolean extraSounds = plugin.getConfig().getBoolean("luckyBox.display.flip_animation.enhanced_effects.extra_sounds", true);

            // 音效相关
            private final Sound[] sounds = {
                Sound.BLOCK_NOTE_BLOCK_BELL,
                Sound.BLOCK_NOTE_BLOCK_CHIME,
                Sound.BLOCK_NOTE_BLOCK_XYLOPHONE,
                Sound.BLOCK_NOTE_BLOCK_PLING,
                Sound.BLOCK_NOTE_BLOCK_HARP
            };

            @Override
            public void run() {
                tickCount++;

                try {
                    // 获取全息图实例
                    eu.decentsoftware.holograms.api.holograms.Hologram hologram
                            = eu.decentsoftware.holograms.api.DHAPI.getHologram(hologramId);

                    if (hologram == null) {
                        this.cancel();
                        resetLuckyBox(gameName, boxId);
                        return;
                    }

                    // 更新基本动画效果
                    angle += rotationSpeed;
                    heightOffset = Math.sin(tickCount * wobbleSpeed) * wobbleAmount;

                    // 如果到了后期，逐渐减小旋转半径，让动画看起来像是要停止
                    if (tickCount > totalTicks * 0.7) {
                        circleRadius = Math.max(0, circleRadius - 0.01);
                    }

                    // 更新翻转动画效果
                    if (isFlipping) {
                        flipDuration++;
                        flipAngle += flipSpeed;

                        // 计算缩放效果（模拟翻转时的压缩效果）
                        scaleY = Math.abs(Math.cos(flipAngle));

                        // 增强翻转视觉效果：在翻转过程中添加额外的旋转和缩放
                        double flipProgress = (double) flipDuration / maxFlipDuration;
                        double enhancedRotationAngle = enhancedRotation ? angle + (flipAngle * 2.0) : angle; // 翻转时增加额外旋转
                        double enhancedRadius = dynamicRadius ? circleRadius * (0.8 + 0.4 * scaleY) : circleRadius; // 翻转时改变旋转半径

                        // 在翻转中点时更换武器
                        if (flipDuration == maxFlipDuration / 2 && nextWeapon != null) {
                            String weaponMaterial = getWeaponMaterialName(nextWeapon);
                            String weaponName = getWeaponName(nextWeapon);

                            // 更新物品行 - 在翻转中点时添加特殊效果
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 1,
                                    "#ICON: " + weaponMaterial);

                            // 更新物品名称行 - 添加翻转时的颜色变化效果
                            String coloredName;
                            if (colorTransition) {
                                coloredName = flipProgress < 0.5
                                    ? ChatColor.YELLOW + weaponName
                                    : ChatColor.GREEN + weaponName;
                            } else {
                                coloredName = ChatColor.GREEN + weaponName;
                            }
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 2, coloredName);

                            // 播放翻转音效 - 增强音效
                            Sound randomSound = sounds[random.nextInt(sounds.length)];
                            float volume = 0.9f + (random.nextFloat() * 0.3f);
                            float pitch = 1.4f + (tickCount / (float) totalTicks) * 0.8f;
                            player.playSound(player.getLocation(), randomSound, volume, pitch);

                            // 添加额外的翻转音效（如果启用）
                            if (extraSounds) {
                                player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 0.6f, 1.8f);
                            }

                            // 在翻转中点添加增强的粒子效果（如果启用）
                            if (flipParticles) {
                                // 计算当前位置用于粒子效果
                                double x = enhancedRadius * Math.cos(enhancedRotationAngle);
                                double z = enhancedRadius * Math.sin(enhancedRotationAngle);
                                double flipHeightOffset = (1.0 - scaleY) * flipHeightEffect;
                                Location particleLocation = location.clone().add(x, heightOffset + flipHeightOffset, z);

                                // 增强的粒子效果
                                particleLocation.getWorld().spawnParticle(
                                    org.bukkit.Particle.ENCHANT,
                                    particleLocation,
                                    12,
                                    0.4, 0.4, 0.4,
                                    0.15
                                );
                                particleLocation.getWorld().spawnParticle(
                                    org.bukkit.Particle.CRIT,
                                    particleLocation,
                                    8,
                                    0.3, 0.3, 0.3,
                                    0.08
                                );
                                // 添加额外的闪光效果
                                particleLocation.getWorld().spawnParticle(
                                    org.bukkit.Particle.FIREWORK,
                                    particleLocation,
                                    3,
                                    0.1, 0.1, 0.1,
                                    0.02
                                );
                                // 添加螺旋粒子效果（如果启用）
                                if (spiralParticles) {
                                    for (int i = 0; i < 6; i++) {
                                        double spiralAngle = (System.currentTimeMillis() / 100.0) + (i * Math.PI / 3);
                                        double spiralX = 0.3 * Math.cos(spiralAngle);
                                        double spiralZ = 0.3 * Math.sin(spiralAngle);
                                        Location spiralLoc = particleLocation.clone().add(spiralX, 0, spiralZ);
                                        spiralLoc.getWorld().spawnParticle(
                                            org.bukkit.Particle.ENCHANT,
                                            spiralLoc,
                                            1,
                                            0, 0, 0,
                                            0
                                        );
                                    }
                                }
                            }
                        }

                        // 翻转过程中的动态位置更新（使用增强的旋转效果）
                        if (flipDuration > 0 && flipDuration < maxFlipDuration) {
                            double x = enhancedRadius * Math.cos(enhancedRotationAngle);
                            double z = enhancedRadius * Math.sin(enhancedRotationAngle);
                            double flipHeightOffset = (1.0 - scaleY) * flipHeightEffect;
                            Location enhancedLocation = location.clone().add(x, heightOffset + flipHeightOffset, z);
                            hologram.setLocation(enhancedLocation);
                        }

                        // 翻转结束
                        if (flipDuration >= maxFlipDuration) {
                            isFlipping = false;
                            flipDuration = 0;
                            flipAngle = 0;
                            scaleY = 1.0;
                            nextWeapon = null;

                            // 翻转结束时播放完成音效（如果启用）
                            if (extraSounds) {
                                player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 0.8f, 1.5f);
                            }
                        }
                    }

                    // 计算新位置（仅在非翻转状态下更新）
                    if (!isFlipping) {
                        double x = circleRadius * Math.cos(angle);
                        double z = circleRadius * Math.sin(angle);
                        Location newLocation = location.clone().add(x, heightOffset, z);
                        hologram.setLocation(newLocation);
                    }

                    // 根据切换速度来触发动画或直接切换
                    if ((tickCount % switchTicks == 0 || tickCount == totalTicks - 1) && !isFlipping) {
                        // 循环选择武器
                        if (tickCount < totalTicks - 10) { // 最后10ticks不再随机切换
                            // 随机跳跃的索引，使动画更随机
                            currentWeaponIndex = (currentWeaponIndex + 1 + random.nextInt(3)) % availableWeapons.size();

                            String currentWeapon = availableWeapons.get(currentWeaponIndex);
                            String weaponMaterial = getWeaponMaterialName(currentWeapon);
                            String weaponName = getWeaponName(currentWeapon);

                            if (flipEnabled) {
                                // 设置下一个要显示的武器并开始翻转动画
                                nextWeapon = currentWeapon;
                                isFlipping = true;
                                flipDuration = 0;
                                flipAngle = 0;
                            } else {
                                // 直接更新显示（不翻转）
                                eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 1,
                                        "#ICON: " + weaponMaterial);
                                eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 2,
                                        ChatColor.GREEN + weaponName);
                            }
                        } else {
                            // 最后阶段，直接更新显示（不翻转）
                            String currentWeapon = availableWeapons.get(currentWeaponIndex);
                            String weaponMaterial = getWeaponMaterialName(currentWeapon);
                            String weaponName = getWeaponName(currentWeapon);

                            // 更新物品行
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 1,
                                    "#ICON: " + weaponMaterial);

                            // 更新物品名称行
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 2,
                                    ChatColor.GREEN + weaponName);
                        }

                        // 在抽奖过程中，不显示金钱和名称，只在最终结果中显示
                        if (tickCount >= totalTicks - 10) {
                            // 切换到"恭喜获得"标题
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 0,
                                    ChatColor.GOLD + "◈ 恭喜获得 ◈");
                        } else {
                            // 抽奖过程中使用动态文本
                            String animatedTitle = tickCount % 6 < 3
                                    ? ChatColor.LIGHT_PURPLE + "※ 幸运箱 ※"
                                    : ChatColor.GOLD + "※ 幸运箱 ※";
                            eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 0, animatedTitle);
                        }

                        // 确保第4行（索引3）为空，不显示金钱信息
                        eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 3, "");

                        // 播放音效（如果禁用翻转动画或在非翻转状态下）
                        if (!flipEnabled || !isFlipping) {
                            Sound randomSound = sounds[random.nextInt(sounds.length)];
                            float volume = 0.6f + (random.nextFloat() * 0.3f);
                            float pitch = 0.8f + (tickCount / (float) totalTicks) * 0.8f;
                            player.playSound(player.getLocation(), randomSound, volume, pitch);
                        }
                    }

                    // 抽奖结束逻辑
                    if (tickCount >= totalTicks) {
                        // 根据概率选择最终武器
                        finalWeapon = selectWeaponByProbability();
                        finalWeaponName = getWeaponName(finalWeapon);
                        String finalWeaponMaterial = getWeaponMaterialName(finalWeapon);

                        // 设置最终结果显示，返回到中心位置
                        hologram.setLocation(location.clone());

                        // 更新全息图内容，武器出现后只显示武器信息，不显示其他内容
                        eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 0,
                                ChatColor.GOLD + "◈ 恭喜获得 ◈");
                        eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 1,
                                "#ICON: " + finalWeaponMaterial);
                        // 物品名称行
                        eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 2,
                                ChatColor.GREEN + finalWeaponName);
                        // 清除第4行，不在DecentHolograms中显示倒计时（倒计时只在ArmorStand中显示）
                        eu.decentsoftware.holograms.api.DHAPI.setHologramLine(hologram, 3, "");

                        // 显示结果消息
                        player.sendMessage(ChatColor.GREEN + "恭喜你获得了 "
                                + ChatColor.GOLD + finalWeaponName + ChatColor.GREEN + "！"
                                + ChatColor.YELLOW + " 请点击箱子或文字获取!");

                        // 播放成功音效
                        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                        player.playSound(player.getLocation(), Sound.ENTITY_FIREWORK_ROCKET_TWINKLE, 0.7f, 1.2f);

                        // 存储奖励信息，等待玩家点击
                        String rewardKey = gameName + "_" + boxId + "_reward";
                        pendingRewards.put(hologramId, finalWeapon);
                        pendingRewards.put(rewardKey, finalWeapon); // 用于箱子点击检测

                        // 更新全息图位置缓存
                        hologramLocations.put(hologramId, location.clone());

                        // 移除抽奖任务
                        activeDrawings.remove(player.getUniqueId());

                        // 开始30秒倒计时，之后自动恢复原始状态
                        startRewardTimer(gameName, boxId, hologramId, player);

                        this.cancel();
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("抽奖动画运行时出错: " + e.getMessage());
                    this.cancel();

                    // 尝试移除全息图，避免残留
                    try {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
                        plugin.getLogger().info("异常处理时已移除抽奖全息图: " + hologramId);
                    } catch (Exception ex) {
                        plugin.getLogger().warning("移除全息图失败: " + ex.getMessage());
                    }

                    // 清理全息图位置缓存
                    hologramLocations.remove(hologramId);

                    // 恢复原始幸运箱全息图
                    resetLuckyBox(gameName, boxId);

                    // 回退到使用标题显示抽奖结果
                    String finalWeapon = selectWeaponByProbability();
                    giveWeaponToPlayer(player, finalWeapon);
                    player.sendMessage(ChatColor.GREEN + "抽奖动画出错，但已获得 "
                            + getWeaponName(finalWeapon) + ChatColor.GREEN + "！");
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);

        // 存储抽奖任务
        activeDrawings.put(player.getUniqueId(), drawingTask);
    }

    /**
     * 开始30秒奖励倒计时
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @param hologramId 全息图ID
     * @param player 玩家
     */
    private void startRewardTimer(String gameName, String boxId, String hologramId, Player player) {
        // 获取原始幸运箱全息图引用
        String originalHologramKey = gameName + "_" + boxId;
        ArmorStand originalHologram = luckyBoxHolograms.get(originalHologramKey);

        // 获取奖励武器名称用于显示
        String weaponId = pendingRewards.get(gameName + "_" + boxId + "_reward");
        String weaponName = weaponId != null ? getWeaponName(weaponId) : "未知武器";

        BukkitTask timerTask = new BukkitRunnable() {
            private int timeLeftMs = 30000; // 30秒 = 30000毫秒
            private String previousTimeDisplay = "";

            @Override
            public void run() {
                if (timeLeftMs <= 0) {
                    // 时间到，恢复原始状态
                    resetLuckyBox(gameName, boxId);

                    // 尝试移除全息图
                    try {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
                        plugin.getLogger().info("已移除抽奖全息图: " + hologramId);
                    } catch (Exception e) {
                        plugin.getLogger().warning("移除全息图失败: " + e.getMessage());
                    }

                    // 清理全息图位置缓存
                    hologramLocations.remove(hologramId);

                    // 移除奖励记录
                    String rewardKey = gameName + "_" + boxId + "_reward";
                    pendingRewards.remove(hologramId);
                    pendingRewards.remove(rewardKey);

                    // 从倒计时任务Map中移除引用
                    String timerKey = gameName + "_" + boxId;
                    rewardTimers.remove(timerKey);
                    plugin.getLogger().info("倒计时结束，已移除任务引用: " + timerKey);

                    this.cancel();
                    return;
                }

                // 格式化时间显示：秒.毫秒
                String timeDisplay = String.format("%.2f秒", timeLeftMs / 1000.0);

                // 更新统一悬浮文字显示倒计时和奖励信息（使用多个ArmorStand实现多行显示）
                if (originalHologram != null) {
                    // 更新上方标题为获得信息
                    String titleText = ChatColor.GOLD + "★ 获得 " + ChatColor.GREEN + weaponName;
                    if (!titleText.equals(previousTimeDisplay)) {
                        originalHologram.setCustomName(titleText);
                        previousTimeDisplay = titleText;
                    }

                    // 更新下方金钱悬浮文字为倒计时信息
                    String costKey = gameName + "_" + boxId + "_cost";
                    ArmorStand costHologram = luckyBoxHolograms.get(costKey);
                    if (costHologram != null) {
                        costHologram.setCustomName(ChatColor.RED + "点击获取 " + ChatColor.YELLOW + timeDisplay);
                        costHologram.setCustomNameVisible(true); // 确保显示
                    }
                }

                // 移除重复的全息图倒计时显示，只保留统一悬浮文字的倒计时
                // 注释掉重复的DecentHolograms倒计时更新，避免显示重复信息

                // 每tick减少50毫秒（1 tick = 50ms）
                timeLeftMs -= 50;
            }
        }.runTaskTimer(plugin, 0L, 1L); // 每tick运行一次，实现毫秒级更新

        // 存储倒计时任务引用，以便在玩家获取奖励时可以取消
        String timerKey = gameName + "_" + boxId;
        rewardTimers.put(timerKey, timerTask);
        plugin.getLogger().info("已存储倒计时任务: " + timerKey);
    }

    /**
     * 重置幸运箱到原始状态
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     */
    private void resetLuckyBox(String gameName, String boxId) {
        plugin.getLogger().info("开始重置幸运箱 " + boxId + " 到原始状态");

        // 先清理可能的DecentHolograms抽奖全息图
        try {
            if (plugin.isDecentHologramsAvailable()) {
                plugin.getLogger().info("清理DecentHolograms抽奖全息图...");

                // 清理与该幸运箱相关的抽奖全息图
                String prefix = "lucky_box_" + gameName + "_" + boxId;
                for (int i = 0; i < 10; i++) {
                    String possibleId = prefix + "_drawing_" + i;
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                        plugin.getLogger().info("重置时清理抽奖全息图: " + possibleId);
                    }
                }

                // 清理通用格式的抽奖全息图
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    String playerDrawingId = "lucky_box_drawing_" + onlinePlayer.getUniqueId().toString();
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(playerDrawingId) != null) {
                        // 检查该全息图是否在该幸运箱附近
                        Location boxLocation = getBoxLocation(gameName, boxId);
                        if (boxLocation != null) {
                            eu.decentsoftware.holograms.api.holograms.Hologram hologram =
                                eu.decentsoftware.holograms.api.DHAPI.getHologram(playerDrawingId);
                            if (hologram != null && hologram.getLocation().distance(boxLocation) < 5) {
                                eu.decentsoftware.holograms.api.DHAPI.removeHologram(playerDrawingId);
                                plugin.getLogger().info("重置时清理玩家抽奖全息图: " + playerDrawingId);
                            }
                        }
                    }
                }

                // 额外清理：清理所有可能的抽奖结果全息图
                Location boxLocation = getBoxLocation(gameName, boxId);
                if (boxLocation != null) {
                    // 清理可能残留的奖励全息图
                    for (Map.Entry<String, String> entry : new HashMap<>(pendingRewards).entrySet()) {
                        String hologramId = entry.getKey();
                        if (hologramId.contains("lucky_box_drawing_")) {
                            // 检查全息图位置是否在该幸运箱附近
                            Location holoLocation = hologramLocations.get(hologramId);
                            if (holoLocation != null && holoLocation.distance(boxLocation) < 5) {
                                try {
                                    eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
                                    plugin.getLogger().info("重置时清理奖励全息图: " + hologramId);
                                    pendingRewards.remove(hologramId);
                                    hologramLocations.remove(hologramId);
                                } catch (Exception ex) {
                                    plugin.getLogger().warning("清理奖励全息图失败: " + ex.getMessage());
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("重置幸运箱时清理全息图出错: " + e.getMessage());
            e.printStackTrace();
        }

        // 恢复原始幸运箱全息图显示
        String originalHologramKey = gameName + "_" + boxId;
        ArmorStand originalHologram = luckyBoxHolograms.get(originalHologramKey);

        plugin.getLogger().info("恢复原始ArmorStand悬浮文字...");

        // 恢复原始名称和显示（统一显示名称和费用）
        if (originalHologram != null) {
            // 从游戏配置中获取原始金钱数值
            int openCost = 1000; // 默认值
            FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
            if (gameConfig != null) {
                String boxPath = "luckyBoxes." + boxId;
                ConfigurationSection boxSection = gameConfig.getConfigurationSection(boxPath);
                if (boxSection != null) {
                    openCost = boxSection.getInt("openCost", 1000);
                }
            }

            // 恢复为初始状态：显示幸运箱名称
            originalHologram.setCustomName(ChatColor.LIGHT_PURPLE + "幸运箱");

            // 恢复金钱悬浮文字显示
            String costKey = gameName + "_" + boxId + "_cost";
            ArmorStand costHologram = luckyBoxHolograms.get(costKey);
            if (costHologram != null) {
                costHologram.setCustomName(ChatColor.YELLOW + "" + openCost + " 金钱");
                costHologram.setCustomNameVisible(true);
                plugin.getLogger().info("已恢复金钱悬浮文字: " + costKey);
            }
            originalHologram.setCustomNameVisible(true);
            plugin.getLogger().info("已恢复统一悬浮文字: " + originalHologramKey + ", 费用: " + openCost);
        } else {
            plugin.getLogger().warning("未找到原始悬浮文字: " + originalHologramKey);
        }

        // 取消倒计时任务（如果存在）
        String timerKey = gameName + "_" + boxId;
        BukkitTask timerTask = rewardTimers.remove(timerKey);
        if (timerTask != null) {
            timerTask.cancel();
            plugin.getLogger().info("重置时已取消倒计时任务: " + timerKey);
        }

        // 清理相关的奖励记录
        String rewardKey = gameName + "_" + boxId + "_reward";
        if (pendingRewards.containsKey(rewardKey)) {
            pendingRewards.remove(rewardKey);
            plugin.getLogger().info("已清理奖励记录: " + rewardKey);
        }

        plugin.getLogger().info("已完成重置幸运箱 " + boxId + " 到原始状态");
    }



    /**
     * 获取带有动画效果的文本
     *
     * @param baseText 基本文本
     * @param tick 当前tick
     * @return 带有动画效果的文本
     */
    private String getAnimatedText(String baseText, int tick) {
        String[] animations = {
            "...",
            ".. .",
            ". ..",
            " ...",
            "... ",
            ".. .",
            ". .."
        };

        return baseText + animations[tick % animations.length];
    }

    /**
     * 使用Title显示方式创建抽奖效果（备用方案）
     *
     * @param player 玩家
     * @param availableWeapons 可用武器列表
     */
    private void createDrawingWithTitle(Player player, List<String> availableWeapons) {
        // 获取持续时间
        int duration = plugin.getConfig().getInt("luckyBox.display.duration", 3);

        // 创建抽奖任务
        BukkitTask drawingTask = new BukkitRunnable() {
            private int tickCount = 0;
            private final int totalTicks = duration * 20; // 转换为tick
            private String currentWeapon = availableWeapons.get(0);

            @Override
            public void run() {
                tickCount++;

                // 每5tick切换一次武器显示
                if (tickCount % 5 == 0) {
                    currentWeapon = availableWeapons.get(random.nextInt(availableWeapons.size()));

                    // 获取武器信息
                    String weaponName = getWeaponName(currentWeapon);
                    player.sendTitle("", ChatColor.YELLOW + weaponName, 0, 10, 0);

                    // 播放音效
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
                }

                // 抽奖结束
                if (tickCount >= totalTicks) {
                    // 根据概率选择最终武器
                    String finalWeapon = selectWeaponByProbability();
                    String finalWeaponName = getWeaponName(finalWeapon);

                    // 显示结果
                    player.sendTitle(ChatColor.GOLD + "恭喜获得", ChatColor.GREEN + finalWeaponName, 10, 40, 10);
                    player.sendMessage(ChatColor.GREEN + "恭喜你获得了 " + finalWeaponName + "！");

                    // 播放成功音效
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);

                    // 给予武器
                    giveWeaponToPlayer(player, finalWeapon);

                    // 移除抽奖任务
                    activeDrawings.remove(player.getUniqueId());

                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);

        // 存储抽奖任务
        activeDrawings.put(player.getUniqueId(), drawingTask);
    }

    /**
     * 获取武器名称
     *
     * @param weaponId 武器ID
     * @return 武器名称
     */
    private String getWeaponName(String weaponId) {
        // 优先从配置文件获取武器名称
        ConfigurationSection weaponsSection = plugin.getConfig().getConfigurationSection("luckyBox.weapons");
        if (weaponsSection != null && weaponsSection.isSet(weaponId + ".name")) {
            String configName = weaponsSection.getString(weaponId + ".name");
            if (configName != null && !configName.isEmpty()) {
                return configName;
            }
        }

        // 使用GameKitManager获取武器名称
        String name = plugin.getGameKitManager().getItemName(weaponId);
        if (name != null && !name.equals(weaponId)) {
            return name;
        }

        // 如果没有找到，返回默认名称
        return "武器 " + weaponId;
    }

    /**
     * 获取武器的材质名称，用于全息图显示
     *
     * @param weaponId 武器ID
     * @return 材质名称
     */
    private String getWeaponMaterialName(String weaponId) {
        // 优先从配置获取
        ConfigurationSection weaponsSection = plugin.getConfig().getConfigurationSection("luckyBox.weapons");
        if (weaponsSection != null && weaponsSection.isSet(weaponId + ".material")) {
            String materialName = weaponsSection.getString(weaponId + ".material", "WOODEN_HOE");
            plugin.getLogger().info("从配置获取物品材质: " + weaponId + " -> " + materialName);
            return materialName.toUpperCase();
        }

        // 尝试从GameKitManager获取材质
        String materialFromKit = plugin.getGameKitManager().getItemMaterial(weaponId);
        if (materialFromKit != null && !materialFromKit.isEmpty()) {
            plugin.getLogger().info("从GameKit获取物品材质: " + weaponId + " -> " + materialFromKit);
            return materialFromKit.toUpperCase();
        }

        // 默认材质为木锄（与gameKit.yml保持一致）
        String materialName = "WOODEN_HOE";
        plugin.getLogger().info("使用默认物品材质: " + weaponId + " -> " + materialName);

        // 确保材质名称是大写的，以便与DecentHolograms兼容
        return materialName.toUpperCase();
    }

    /**
     * 检查字符串是否为数字
     *
     * @param str 要检查的字符串
     * @return 是否为数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取箱子位置
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @return 箱子位置
     */
    private Location getBoxLocation(String gameName, String boxId) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return null;
        }

        String boxPath = "luckyBoxes." + boxId;
        if (!gameConfig.isSet(boxPath)) {
            return null;
        }

        String worldName = gameConfig.getString(boxPath + ".world");
        double x = gameConfig.getDouble(boxPath + ".x");
        double y = gameConfig.getDouble(boxPath + ".y");
        double z = gameConfig.getDouble(boxPath + ".z");

        if (worldName == null || plugin.getServer().getWorld(worldName) == null) {
            return null;
        }

        return new Location(plugin.getServer().getWorld(worldName), x, y, z);
    }

    /**
     * 获取可抽取的武器列表
     *
     * @return 武器ID列表
     */
    private List<String> getAvailableWeapons() {
        List<String> weapons = new ArrayList<>();

        // 从config.yml获取幸运箱武器配置
        ConfigurationSection luckyBoxConfig = plugin.getConfig().getConfigurationSection("luckyBox.weapons");
        if (luckyBoxConfig != null) {
            for (String weaponId : luckyBoxConfig.getKeys(false)) {
                weapons.add(weaponId);
            }
        }

        // 如果配置为空，使用默认武器列表
        if (weapons.isEmpty()) {
            weapons.add("id1");
            weapons.add("id2");
            weapons.add("id3");
            weapons.add("id4");
            weapons.add("id5");
        }

        return weapons;
    }

    /**
     * 根据概率选择武器
     *
     * @return 选中的武器ID
     */
    private String selectWeaponByProbability() {
        ConfigurationSection luckyBoxConfig = plugin.getConfig().getConfigurationSection("luckyBox.weapons");
        if (luckyBoxConfig == null) {
            // 如果没有配置，随机返回一个默认武器
            List<String> defaultWeapons = getAvailableWeapons();
            return defaultWeapons.get(random.nextInt(defaultWeapons.size()));
        }

        // 计算总概率
        double totalProbability = 0.0;
        Map<String, Double> weaponProbabilities = new HashMap<>();

        for (String weaponId : luckyBoxConfig.getKeys(false)) {
            double probability = luckyBoxConfig.getDouble(weaponId + ".probability", 10.0);
            weaponProbabilities.put(weaponId, probability);
            totalProbability += probability;
        }

        // 随机选择
        double randomValue = random.nextDouble() * totalProbability;
        double currentSum = 0.0;

        for (Map.Entry<String, Double> entry : weaponProbabilities.entrySet()) {
            currentSum += entry.getValue();
            if (randomValue <= currentSum) {
                return entry.getKey();
            }
        }

        // 如果没有选中任何武器，返回第一个
        return weaponProbabilities.keySet().iterator().next();
    }

    /**
     * 给予玩家武器
     *
     * @param player 玩家
     * @param weaponId 武器ID
     */
    private void giveWeaponToPlayer(Player player, String weaponId) {
        // 检查玩家是否有空的武器槽位
        if (!hasEmptyWeaponSlot(player)) {
            player.sendMessage(ChatColor.RED + "你的武器槽位已满，无法获得武器！");
            return;
        }

        // 使用Shoot插件给予武器
        if (shootHelper.giveGunToPlayer(player, weaponId)) {
            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 武器: " + weaponId);
        } else {
            player.sendMessage(ChatColor.RED + "给予武器失败！");
        }
    }

    /**
     * 检查玩家是否有空的武器槽位
     *
     * @param player 玩家
     * @return 是否有空槽位
     */
    private boolean hasEmptyWeaponSlot(Player player) {
        // 检查槽位1-5是否有空位（武器槽位）
        // 注意：槽位从0开始计数，所以槽位1实际上是索引0
        for (int slot = 1; slot <= 4; slot++) {  // 检查槽位2-5 (索引1-4)
            ItemStack item = player.getInventory().getItem(slot);
            if (item == null || item.getType() == Material.AIR) {
                return true;
            }
        }

        // 如果没有找到空槽位，则检查是否至少有一个武器，用于替换
        boolean hasWeapon = false;
        for (int slot = 1; slot <= 4; slot++) {  // 检查槽位2-5 (索引1-4)
            ItemStack item = player.getInventory().getItem(slot);
            if (item != null && isWeapon(item)) {
                hasWeapon = true;
                break;
            }
        }

        // 只有当玩家有一个武器时，才能获取新的武器（替换）
        return hasWeapon;
    }

    /**
     * 检查物品是否为武器
     *
     * @param item 物品
     * @return 是否为武器
     */
    private boolean isWeapon(ItemStack item) {
        // 检查是否为武器类型的物品
        if (item == null) {
            return false;
        }

        // 检查是否为锄头类型的物品（在游戏中代表枪）
        Material type = item.getType();
        if (type == Material.WOODEN_HOE || type == Material.STONE_HOE
                || type == Material.IRON_HOE || type == Material.GOLDEN_HOE
                || type == Material.DIAMOND_HOE || type == Material.NETHERITE_HOE) {
            return true;
        }

        // 检查物品名称或Lore是否包含武器特征
        if (item.hasItemMeta()) {
            if (item.getItemMeta().hasDisplayName()) {
                String name = item.getItemMeta().getDisplayName();
                if (name.contains("枪") || name.contains("Gun")
                        || name.contains("步枪") || name.contains("Rifle")
                        || name.contains("手枪") || name.contains("Pistol")
                        || name.contains("霰弹枪") || name.contains("Shotgun")
                        || name.contains("狙击") || name.contains("Sniper")) {
                    return true;
                }
            }

            if (item.getItemMeta().hasLore()) {
                List<String> lore = item.getItemMeta().getLore();
                for (String line : lore) {
                    if (line.contains("武器") || line.contains("Weapon")
                            || line.contains("枪") || line.contains("Gun")) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 检查物品是否为相同的武器
     *
     * @param item 物品
     * @param weaponId 武器ID
     * @param weaponName 武器名称
     * @return 是否为相同武器
     */
    private boolean isSameWeapon(ItemStack item, String weaponId, String weaponName) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            plugin.getLogger().info("【武器匹配】物品为空或没有显示名称");
            return false;
        }

        String itemDisplayName = ChatColor.stripColor(item.getItemMeta().getDisplayName());
        String targetWeaponName = ChatColor.stripColor(weaponName);

        plugin.getLogger().info("【武器匹配】比较武器: 物品名称='" + itemDisplayName + "', 目标武器='" + targetWeaponName + "', 武器ID='" + weaponId + "'");

        // 直接比较武器名称
        if (itemDisplayName.equals(targetWeaponName)) {
            plugin.getLogger().info("【武器匹配】完全匹配: " + itemDisplayName + " == " + targetWeaponName);
            return true;
        }

        // 使用模糊匹配，检查武器名称是否包含对方
        // 这样可以处理各种情况：完全匹配、部分匹配等
        if (itemDisplayName.contains(targetWeaponName) || targetWeaponName.contains(itemDisplayName)) {
            plugin.getLogger().info("【武器匹配】模糊匹配成功: '" + itemDisplayName + "' 包含 '" + targetWeaponName + "'");
            return true;
        }

        // 特殊情况处理：根据实际配置文件内容进行匹配
        switch (weaponId) {
            case "id1":
                return itemDisplayName.contains("手枪") || itemDisplayName.contains("Pistol");
            case "id2":
                return itemDisplayName.contains("步枪") || itemDisplayName.contains("Rifle");
            case "id3":
                return itemDisplayName.contains("霰弹枪") || itemDisplayName.contains("Shotgun");
            case "id4":
                return itemDisplayName.contains("机枪") || itemDisplayName.contains("Machine Gun");
            case "id5":
                return itemDisplayName.contains("火箭筒") || itemDisplayName.contains("Rocket");
            case "id6":
                return itemDisplayName.contains("电击枪") || itemDisplayName.contains("Electric");
            case "id7":
                boolean id7Match = itemDisplayName.contains("狙击步枪") || itemDisplayName.contains("狙击") || itemDisplayName.contains("Sniper");
                plugin.getLogger().info("【武器匹配】id7(狙击步枪)匹配结果: " + id7Match + ", 物品名称: '" + itemDisplayName + "'");
                return id7Match;
            case "id8":
                return itemDisplayName.contains("冷冻枪") || itemDisplayName.contains("Freeze");
            case "id9":
                return itemDisplayName.contains("雷击枪") || itemDisplayName.contains("Thunder");
            case "id10":
                return itemDisplayName.contains("压强枪") || itemDisplayName.contains("Pressure");
            case "id11":
                return itemDisplayName.contains("突击步枪") || itemDisplayName.contains("Assault");
            case "id12":
                return itemDisplayName.contains("冲锋枪") || itemDisplayName.contains("SMG");
            default:
                // 对于其他武器，使用模糊匹配
                plugin.getLogger().info("【武器匹配】未找到匹配的武器: '" + itemDisplayName + "' vs '" + targetWeaponName + "' (" + weaponId + ")");
                return false; // 已经在上面做过模糊匹配了
        }
    }

    /**
     * 清理游戏中的所有幸运箱
     *
     * @param gameName 游戏名称
     */
    public void clearLuckyBoxes(String gameName) {
        plugin.getLogger().info("开始清理游戏 " + gameName + " 的所有幸运箱和悬浮文字");

        // 清理箱子方块
        int removedBoxCount = 0;
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig != null) {
            ConfigurationSection luckyBoxesSection = gameConfig.getConfigurationSection("luckyBoxes");
            if (luckyBoxesSection != null) {
                for (String boxId : luckyBoxesSection.getKeys(false)) {
                    ConfigurationSection boxSection = luckyBoxesSection.getConfigurationSection(boxId);
                    if (boxSection == null) {
                        continue;
                    }

                    // 获取箱子位置
                    String worldName = boxSection.getString("world");
                    double x = boxSection.getDouble("x");
                    double y = boxSection.getDouble("y");
                    double z = boxSection.getDouble("z");

                    if (worldName != null && Bukkit.getWorld(worldName) != null) {
                        Location location = new Location(Bukkit.getWorld(worldName), x, y, z);

                        // 移除箱子方块
                        Block block = location.getBlock();
                        if (block.getType() == Material.CHEST) {
                            block.setType(Material.AIR);
                            removedBoxCount++;
                            plugin.getLogger().info("移除幸运箱方块: " + boxId + " 在位置 " + location);
                        }
                    }
                }
            }
        }

        if (removedBoxCount > 0) {
            plugin.getLogger().info("共移除了 " + removedBoxCount + " 个幸运箱方块");
        }

        // 移除悬浮文字（ArmorStand）
        List<String> keysToRemove = new ArrayList<>();
        int removedHologramCount = 0;

        for (Map.Entry<String, ArmorStand> entry : luckyBoxHolograms.entrySet()) {
            if (entry.getKey().startsWith(gameName + "_")) {
                ArmorStand hologram = entry.getValue();
                if (hologram != null && !hologram.isDead()) {
                    hologram.remove();
                    removedHologramCount++;
                    plugin.getLogger().info("移除了游戏 " + gameName + " 的幸运箱悬浮文字: " + entry.getKey());
                }
                keysToRemove.add(entry.getKey());
            }
        }

        // 从映射中移除已处理的条目
        for (String key : keysToRemove) {
            luckyBoxHolograms.remove(key);
        }

        if (removedHologramCount > 0) {
            plugin.getLogger().info("共清理了 " + removedHologramCount + " 个幸运箱ArmorStand悬浮文字");
        }

        // 清理所有相关的倒计时任务
        List<String> timerKeysToRemove = new ArrayList<>();
        int removedTimerCount = 0;
        for (Map.Entry<String, BukkitTask> entry : rewardTimers.entrySet()) {
            if (entry.getKey().startsWith(gameName + "_")) {
                BukkitTask task = entry.getValue();
                if (task != null) {
                    task.cancel();
                    removedTimerCount++;
                    plugin.getLogger().info("清理游戏 " + gameName + " 的倒计时任务: " + entry.getKey());
                }
                timerKeysToRemove.add(entry.getKey());
            }
        }

        for (String key : timerKeysToRemove) {
            rewardTimers.remove(key);
        }

        if (removedTimerCount > 0) {
            plugin.getLogger().info("共清理了 " + removedTimerCount + " 个倒计时任务");
        }

        // 清理DecentHolograms抽奖全息图
        int removedDecentHologramCount = 0;
        try {
            if (plugin.isDecentHologramsAvailable()) {
                // 使用固定前缀模式查找全息图
                String prefix = "lucky_box_" + gameName;

                // 尝试查找并删除以指定前缀开头的全息图
                for (int i = 0; i < 100; i++) { // 限制循环次数，避免无限循环
                    String possibleId = prefix + "_drawing_" + i;
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                        removedDecentHologramCount++;
                        plugin.getLogger().info("已清理游戏 " + gameName + " 的抽奖全息图: " + possibleId);
                    }
                }

                // 使用UUID模式查找可能的全息图
                for (Player player : Bukkit.getOnlinePlayers()) {
                    String playerId = player.getUniqueId().toString();
                    String possibleId = "lucky_box_drawing_" + playerId;
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                        removedDecentHologramCount++;
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的抽奖全息图: " + possibleId);
                    }
                }

                if (removedDecentHologramCount > 0) {
                    plugin.getLogger().info("共清理了 " + removedDecentHologramCount + " 个DecentHolograms抽奖全息图");
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("清理游戏 " + gameName + " 的DecentHolograms全息图时出错: " + e.getMessage());
        }

        // 清理待领取奖励记录
        int removedRewardCount = 0;
        Iterator<Map.Entry<String, String>> rewardIterator = pendingRewards.entrySet().iterator();
        while (rewardIterator.hasNext()) {
            Map.Entry<String, String> entry = rewardIterator.next();
            String key = entry.getKey();

            // 清理与该游戏相关的奖励记录
            if (key.contains(gameName + "_")) {
                rewardIterator.remove();
                removedRewardCount++;
                plugin.getLogger().info("清理待领取奖励记录: " + key + " -> " + entry.getValue());
            }
        }

        if (removedRewardCount > 0) {
            plugin.getLogger().info("共清理了 " + removedRewardCount + " 个待领取奖励记录");
        }

        // 清理全息图位置缓存
        int removedLocationCount = 0;
        Iterator<Map.Entry<String, Location>> locationIterator = hologramLocations.entrySet().iterator();
        while (locationIterator.hasNext()) {
            Map.Entry<String, Location> entry = locationIterator.next();
            String key = entry.getKey();

            // 清理与该游戏相关的位置缓存
            if (key.contains(gameName + "_") || key.startsWith("lucky_box_drawing_")) {
                locationIterator.remove();
                removedLocationCount++;
                plugin.getLogger().info("清理全息图位置缓存: " + key);
            }
        }

        if (removedLocationCount > 0) {
            plugin.getLogger().info("共清理了 " + removedLocationCount + " 个全息图位置缓存");
        }

        plugin.getLogger().info("游戏 " + gameName + " 的所有幸运箱和相关悬浮文字清理完成");
    }

    /**
     * 取消玩家的抽奖
     *
     * @param player 玩家
     */
    public void cancelPlayerDrawing(Player player) {
        BukkitTask task = activeDrawings.remove(player.getUniqueId());
        if (task != null) {
            task.cancel();
        }
    }

    /**
     * 玩家退出游戏时的完整清理方法
     * 这个方法会清理玩家相关的所有幸运箱资源
     *
     * @param player 玩家
     */
    public void onPlayerLeaveGame(Player player) {
        plugin.getLogger().info("开始清理玩家 " + player.getName() + " 的所有幸运箱资源...");

        // 取消玩家的抽奖任务
        cancelPlayerDrawing(player);

        // 清理玩家的抽奖全息图
        cleanupPlayerDrawingHolograms(player);

        plugin.getLogger().info("已完成清理玩家 " + player.getName() + " 的所有幸运箱资源");
    }

    /**
     * 清理玩家的所有抽奖全息图（防止重复显示）
     *
     * @param player 玩家
     */
    public void cleanupPlayerDrawingHolograms(Player player) {
        String playerUUID = player.getUniqueId().toString();
        plugin.getLogger().info("开始清理玩家 " + player.getName() + " 的所有抽奖全息图...");

        int cleanedCount = 0;

        // 清理所有以该玩家UUID结尾的抽奖全息图
        try {
            // 清理标准ID格式的全息图
            String standardId = "lucky_box_drawing_" + playerUUID;
            if (eu.decentsoftware.holograms.api.DHAPI.getHologram(standardId) != null) {
                eu.decentsoftware.holograms.api.DHAPI.removeHologram(standardId);
                plugin.getLogger().info("清理玩家 " + player.getName() + " 的残留抽奖全息图: " + standardId);
                cleanedCount++;
            }

            // 清理可能的其他格式的全息图
            for (int i = 0; i < 10; i++) {
                String possibleId = "lucky_box_drawing_" + playerUUID + "_" + i;
                if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                    eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                    plugin.getLogger().info("清理玩家 " + player.getName() + " 的残留抽奖全息图: " + possibleId);
                    cleanedCount++;
                }
            }

            // 清理可能的其他格式的全息图（不带索引）
            for (int i = 0; i < 10; i++) {
                String possibleId = "lucky_box_" + playerUUID + "_drawing_" + i;
                if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                    eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                    plugin.getLogger().info("清理玩家 " + player.getName() + " 的残留抽奖全息图: " + possibleId);
                    cleanedCount++;
                }
            }

            // 清理全息图位置缓存
            int locationsCleaned = 0;
            Iterator<Map.Entry<String, Location>> locationIterator = hologramLocations.entrySet().iterator();
            while (locationIterator.hasNext()) {
                Map.Entry<String, Location> entry = locationIterator.next();
                if (entry.getKey().contains(playerUUID)) {
                    locationIterator.remove();
                    locationsCleaned++;
                }
            }
            if (locationsCleaned > 0) {
                plugin.getLogger().info("清理了 " + locationsCleaned + " 个全息图位置缓存");
            }

            // 清理奖励记录
            int rewardsCleaned = 0;
            Iterator<Map.Entry<String, String>> rewardIterator = pendingRewards.entrySet().iterator();
            while (rewardIterator.hasNext()) {
                Map.Entry<String, String> entry = rewardIterator.next();
                if (entry.getKey().contains(playerUUID)) {
                    rewardIterator.remove();
                    rewardsCleaned++;
                }
            }
            if (rewardsCleaned > 0) {
                plugin.getLogger().info("清理了 " + rewardsCleaned + " 个奖励记录");
            }

            // 取消玩家的抽奖任务
            BukkitTask drawingTask = activeDrawings.remove(player.getUniqueId());
            if (drawingTask != null) {
                drawingTask.cancel();
                plugin.getLogger().info("取消了玩家 " + player.getName() + " 的抽奖任务");
            }

            plugin.getLogger().info("玩家 " + player.getName() + " 的清理完成，共清理 " + cleanedCount + " 个全息图");

        } catch (Exception e) {
            plugin.getLogger().warning("清理玩家 " + player.getName() + " 的抽奖全息图时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清理所有抽奖任务
     */
    public void clearAllDrawings() {
        for (BukkitTask task : activeDrawings.values()) {
            if (task != null) {
                task.cancel();
            }
        }
        activeDrawings.clear();
        pendingRewards.clear();
    }

    /**
     * 设置幸运箱通过玩家交互
     *
     * @param gameName 游戏名称
     * @param boxName 箱子名称
     * @param location 箱子位置
     * @param type 箱子类型 (local/random)
     * @param openRound 开启回合
     * @param openCost 开启金钱
     * @return 是否设置成功
     */
    public boolean setLuckyBoxByInteraction(String gameName, String boxName, Location location, String type, int openRound, int openCost) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

            // 设置幸运箱配置
            String boxPath = "luckyBoxes." + boxName;
            gameConfig.set(boxPath + ".world", location.getWorld().getName());
            gameConfig.set(boxPath + ".x", location.getX());
            gameConfig.set(boxPath + ".y", location.getY());
            gameConfig.set(boxPath + ".z", location.getZ());
            gameConfig.set(boxPath + ".type", type);
            gameConfig.set(boxPath + ".openRound", openRound);
            gameConfig.set(boxPath + ".openCost", openCost);
            gameConfig.set(boxPath + ".enabled", true);

            // 保存配置
            gameConfig.save(gameFile);

            plugin.getLogger().info("成功添加/设置幸运箱 " + boxName + " 到游戏 " + gameName
                    + " (类型: " + type + ", 开启回合: " + openRound + ", 消耗金钱: " + openCost + ")");

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存幸运箱配置时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查幸运箱是否存在
     *
     * @param gameName 游戏名称
     * @param boxName 箱子名称
     * @return 是否存在
     */
    public boolean luckyBoxExists(String gameName, String boxName) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return false;
        }

        return gameConfig.isSet("luckyBoxes." + boxName);
    }

    /**
     * 检查玩家是否有未获取的奖励
     *
     * @param player 玩家
     * @return 是否有未获取的奖励
     */
    public boolean hasPlayerPendingReward(Player player) {
        String playerUUID = player.getUniqueId().toString();

        // 检查是否有与该玩家相关的奖励记录
        for (Map.Entry<String, String> entry : pendingRewards.entrySet()) {
            String key = entry.getKey();

            // 检查是否是抽奖全息图ID（包含玩家UUID）
            if (key.startsWith("lucky_box_drawing_") && key.contains(playerUUID)) {
                plugin.getLogger().info("检测到玩家 " + player.getName() + " 有未获取的奖励: " + key + " -> " + entry.getValue());
                return true;
            }

            // 检查是否是箱子奖励记录（格式为 gameName_boxId_reward）
            if (key.endsWith("_reward")) {
                // 需要检查该奖励是否属于该玩家
                // 通过检查是否有对应的全息图ID来判断
                String weaponId = entry.getValue();
                for (String hologramKey : pendingRewards.keySet()) {
                    if (hologramKey.startsWith("lucky_box_drawing_") &&
                        hologramKey.contains(playerUUID) &&
                        pendingRewards.get(hologramKey).equals(weaponId)) {
                        plugin.getLogger().info("检测到玩家 " + player.getName() + " 有未获取的箱子奖励: " + key + " -> " + weaponId);
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 获取玩家的未获取奖励信息
     *
     * @param player 玩家
     * @return 未获取奖励的详细信息，如果没有则返回null
     */
    public String getPlayerPendingRewardInfo(Player player) {
        String playerUUID = player.getUniqueId().toString();

        // 查找玩家的未获取奖励
        for (Map.Entry<String, String> entry : pendingRewards.entrySet()) {
            String key = entry.getKey();

            // 检查是否是抽奖全息图ID（包含玩家UUID）
            if (key.startsWith("lucky_box_drawing_") && key.contains(playerUUID)) {
                String weaponId = entry.getValue();
                String weaponName = getWeaponName(weaponId);

                // 查找对应的箱子信息
                for (Map.Entry<String, String> rewardEntry : pendingRewards.entrySet()) {
                    if (rewardEntry.getKey().endsWith("_reward") && rewardEntry.getValue().equals(weaponId)) {
                        String[] parts = rewardEntry.getKey().split("_");
                        if (parts.length >= 3) {
                            String gameName = parts[0];
                            String boxId = parts[1];
                            return "游戏: " + gameName + ", 幸运箱: " + boxId + ", 奖励: " + weaponName + " (" + weaponId + ")";
                        }
                    }
                }

                return "奖励: " + weaponName + " (" + weaponId + ")"; // 如果找不到箱子信息
            }
        }

        return null; // 没有未获取的奖励
    }

    /**
     * 清理玩家的所有未获取奖励（管理员命令用）
     *
     * @param player 玩家
     * @return 清理的奖励数量
     */
    public int clearPlayerPendingRewards(Player player) {
        String playerUUID = player.getUniqueId().toString();
        int clearedCount = 0;

        // 清理所有与该玩家相关的奖励记录
        Iterator<Map.Entry<String, String>> iterator = pendingRewards.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = entry.getKey();

            // 检查是否是抽奖全息图ID（包含玩家UUID）
            if (key.startsWith("lucky_box_drawing_") && key.contains(playerUUID)) {
                // 尝试移除对应的全息图
                try {
                    if (plugin.isDecentHologramsAvailable()) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(key);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("清理全息图时出错: " + e.getMessage());
                }

                iterator.remove();
                hologramLocations.remove(key);
                clearedCount++;
                plugin.getLogger().info("清理了玩家 " + player.getName() + " 的未获取奖励: " + key + " -> " + entry.getValue());
            }
        }

        // 清理对应的箱子奖励记录
        iterator = pendingRewards.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = entry.getKey();

            if (key.endsWith("_reward")) {
                String weaponId = entry.getValue();
                // 检查是否还有对应的全息图ID
                boolean hasCorrespondingHologram = false;
                for (String hologramKey : pendingRewards.keySet()) {
                    if (hologramKey.startsWith("lucky_box_drawing_") &&
                        hologramKey.contains(playerUUID) &&
                        pendingRewards.get(hologramKey).equals(weaponId)) {
                        hasCorrespondingHologram = true;
                        break;
                    }
                }

                // 如果没有对应的全息图，说明这个奖励记录已经孤立，需要清理
                if (!hasCorrespondingHologram) {
                    iterator.remove();
                    plugin.getLogger().info("清理了孤立的奖励记录: " + key + " -> " + weaponId);
                }
            }
        }

        // 取消玩家的抽奖任务
        BukkitTask drawingTask = activeDrawings.remove(player.getUniqueId());
        if (drawingTask != null) {
            drawingTask.cancel();
            plugin.getLogger().info("取消了玩家 " + player.getName() + " 的抽奖任务");
        }

        return clearedCount;
    }

    /**
     * 设置幸运箱开启条件
     *
     * @param gameName 游戏名称
     * @param boxName 箱子名称
     * @param openParam 开启参数类型 (openRound/openCost)
     * @param openValue 开启参数值
     * @return 是否设置成功
     */
    public boolean setLuckyBoxOpenCondition(String gameName, String boxName, String openParam, int openValue) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);
            String boxPath = "luckyBoxes." + boxName;

            if (!gameConfig.isSet(boxPath)) {
                plugin.getLogger().warning("幸运箱 " + boxName + " 不存在于游戏 " + gameName + " 中");
                return false;
            }

            // 设置开启条件
            if (openParam.equalsIgnoreCase("openRound")) {
                gameConfig.set(boxPath + ".openRound", openValue);
            } else if (openParam.equalsIgnoreCase("openCost")) {
                gameConfig.set(boxPath + ".openCost", openValue);
            } else {
                plugin.getLogger().warning("无效的开启参数类型: " + openParam);
                return false;
            }

            // 保存配置
            gameConfig.save(gameFile);

            String paramName = openParam.equalsIgnoreCase("openRound") ? "开启回合" : "消耗金钱";
            plugin.getLogger().info("成功设置幸运箱 " + boxName + " 的" + paramName + "为: " + openValue);

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存幸运箱开启条件时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 添加武器到全局幸运箱奖池
     *
     * @param weaponId 武器ID
     * @param probability 概率 (0-100)
     * @return 是否添加成功
     */
    public boolean addWeaponToGlobalLuckyBoxPool(String weaponId, double probability) {
        try {
            File configFile = new File(plugin.getDataFolder(), "config.yml");
            if (!configFile.exists()) {
                plugin.getLogger().warning("主配置文件不存在!");
                return false;
            }

            FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);

            // 检查武器ID是否有效
            if (!weaponId.startsWith("id")) {
                plugin.getLogger().warning("无效的武器ID: " + weaponId);
                return false;
            }

            // 检查武器是否已存在于奖池
            String weaponPath = "luckyBox.weapons." + weaponId;
            if (config.isSet(weaponPath)) {
                plugin.getLogger().info("武器 " + weaponId + " 已存在于奖池，将更新其概率。");
            }

            // 设置武器概率
            config.set(weaponPath + ".probability", probability);

            // 保存配置
            config.save(configFile);

            plugin.getLogger().info("成功添加/更新武器 " + weaponId + " 到全局奖池，概率: " + probability + "%");

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存全局奖池配置时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 修改幸运箱开启条件
     *
     * @param gameName 游戏名称
     * @param boxName 箱子名称
     * @param paramType 参数类型 (openRound/openCost)
     * @param value 参数值
     * @return 是否修改成功
     */
    public boolean changeLuckyBoxOpenCondition(String gameName, String boxName, String paramType, int value) {
        return setLuckyBoxOpenCondition(gameName, boxName, paramType, value);
    }

    /**
     * 修改幸运箱类型
     *
     * @param gameName 游戏名称
     * @param boxName 箱子名称
     * @param newType 新类型 (local/random)
     * @return 是否修改成功
     */
    public boolean changeLuckyBoxType(String gameName, String boxName, String newType) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);
            String boxPath = "luckyBoxes." + boxName;

            if (!gameConfig.isSet(boxPath)) {
                plugin.getLogger().warning("幸运箱 " + boxName + " 不存在于游戏 " + gameName + " 中");
                return false;
            }

            // 检查类型是否有效
            if (!newType.equals("local") && !newType.equals("random")) {
                plugin.getLogger().warning("无效的箱子类型: " + newType);
                return false;
            }

            // 设置新类型
            gameConfig.set(boxPath + ".type", newType);

            // 保存配置
            gameConfig.save(gameFile);

            plugin.getLogger().info("成功修改幸运箱 " + boxName + " 的类型为: " + newType);

            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存幸运箱类型时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 清理所有游戏的幸运箱和全息图 在插件停用时调用此方法
     */
    public void onDisable() {
        plugin.getLogger().info("正在清理所有幸运箱资源...");

        // 取消所有抽奖任务
        clearAllDrawings();

        // 清理所有游戏的幸运箱全息图
        for (String gameName : new HashSet<>(luckyBoxHolograms.keySet())) {
            clearLuckyBoxes(gameName);
        }

        // 清理所有抽奖临时全息图（额外检查）
        try {
            // 查找并清理所有以lucky_box_开头的全息图
            if (plugin.isDecentHologramsAvailable()) {
                // 使用固定前缀模式查找全息图
                String prefix = "lucky_box";

                // 尝试查找并删除以指定前缀开头的全息图
                // 先检查通用的ID模式
                for (int i = 0; i < 100; i++) {
                    String possibleId = prefix + "_drawing_" + i;
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                        plugin.getLogger().info("已清理临时抽奖全息图: " + possibleId);
                    }
                }

                // 检查特定游戏的ID模式
                for (String gameName : gameManager.getGameNames()) {
                    String gamePrefix = prefix + "_" + gameName;
                    for (int i = 0; i < 20; i++) {
                        String possibleId = gamePrefix + "_" + i;
                        if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                            eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                            plugin.getLogger().info("已清理游戏 " + gameName + " 的临时抽奖全息图: " + possibleId);
                        }
                    }
                }

                // 检查所有离线玩家可能的全息图
                for (Player player : Bukkit.getOnlinePlayers()) {
                    String playerId = player.getUniqueId().toString();
                    String possibleId = "lucky_box_drawing_" + playerId;
                    if (eu.decentsoftware.holograms.api.DHAPI.getHologram(possibleId) != null) {
                        eu.decentsoftware.holograms.api.DHAPI.removeHologram(possibleId);
                        plugin.getLogger().info("已清理玩家 " + player.getName() + " 的临时抽奖全息图: " + possibleId);
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("清理临时抽奖全息图时出错: " + e.getMessage());
        }

        plugin.getLogger().info("幸运箱资源清理完成");
    }

    /**
     * 处理玩家与幸运箱全息图的交互
     *
     * @param player 玩家
     * @param hologramId 全息图ID
     * @return 是否成功处理
     */
    public boolean handleHologramClick(Player player, String hologramId) {
        // 检查是否有待领取的奖励
        if (!pendingRewards.containsKey(hologramId)) {
            return false;
        }

        // 获取武器ID和相关游戏信息
        String weaponId = pendingRewards.get(hologramId);

        // 尝试从全息图ID中提取游戏名称和箱子ID
        // 全息图ID格式为: lucky_box_drawing_<UUID>
        String gameName = null;
        String boxId = null;

        // 查找对应的箱子信息
        for (Map.Entry<String, String> entry : pendingRewards.entrySet()) {
            if (entry.getValue().equals(weaponId) && entry.getKey().contains("_reward")) {
                // key格式为: gameName_boxId_reward
                String[] parts = entry.getKey().split("_");
                if (parts.length >= 3) {
                    gameName = parts[0];
                    boxId = parts[1];
                    break;
                }
            }
        }

        if (gameName == null || boxId == null) {
            // 无法找到对应游戏和箱子信息，尝试使用旧方法处理
            plugin.getLogger().warning("无法找到全息图对应的游戏和箱子信息，尝试使用旧方法处理");
            return handleHologramClickLegacy(player, hologramId, weaponId);
        }

        // 使用新的奖励领取系统
        if (handleRewardClaim(player, weaponId, gameName, boxId)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 通过全息图交互获取了奖励: " + weaponId);

            // 尝试移除全息图
            try {
                eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
            } catch (Exception e) {
                plugin.getLogger().warning("移除全息图失败: " + e.getMessage());
            }

            return true;
        }

        return true;
    }

    /**
     * 旧的全息图点击处理方法（备用）
     */
    private boolean handleHologramClickLegacy(Player player, String hologramId, String weaponId) {
        String weaponName = getWeaponName(weaponId);

        // 检查玩家武器槽位情况
        int emptySlot = getAvailableWeaponSlot(player, weaponId);

        if (emptySlot == -1) {
            // 没有空槽位也没有手持武器，无法获取新武器
            player.sendMessage(ChatColor.RED + "你的武器槽位已满! 请先清理背包或手持一把武器来替换.");
            // 播放错误音效
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return true;
        }

        if (emptySlot == -3) {
            // 玩家身上有相同武器但没有手持，需要先切换
            player.sendMessage(ChatColor.RED + "你的背包中已有 " + ChatColor.GOLD + weaponName + ChatColor.RED + "！");
            player.sendMessage(ChatColor.YELLOW + "请先手持该武器，然后再获取奖励以补充弹药。");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return true;
        }

        boolean success = false;

        if (emptySlot == -2) {
            // 手持相同武器，补充弹药
            int heldSlot = player.getInventory().getHeldItemSlot();
            if (heldSlot >= 1 && heldSlot <= 4) { // 只允许在槽位2-5（索引1-4）
                // 调用Shoot插件的补充弹药方法（会验证武器匹配）
                success = shootHelper.replenishPlayerAmmo(player, weaponId);
                if (success) {
                    player.sendMessage(ChatColor.GREEN + "已为你的 " + ChatColor.GOLD + weaponName + ChatColor.GREEN + " 补充弹药！");
                } else {
                    // 如果补充弹药失败（可能是武器不匹配），尝试替换武器
                    plugin.getLogger().warning("弹药补充失败，尝试替换武器");
                    player.getInventory().setItem(heldSlot, null);
                    success = shootHelper.giveGunToPlayer(player, weaponId);
                    if (success) {
                        player.sendMessage(ChatColor.GREEN + "已替换手持武器为: " + ChatColor.GOLD + weaponName);
                    } else {
                        player.sendMessage(ChatColor.RED + "给予武器失败，请稍后再试！");
                    }
                }
            } else {
                player.sendMessage(ChatColor.RED + "请手持一把可替换的武器（槽位2-5）.");
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                return true;
            }
        } else if (emptySlot == -4) {
            // 手持其他武器，替换为新武器
            int heldSlot = player.getInventory().getHeldItemSlot();
            if (heldSlot >= 1 && heldSlot <= 4) { // 只允许在槽位2-5（索引1-4）
                // 清除手持的武器，替换为新武器
                player.getInventory().setItem(heldSlot, null);
                success = shootHelper.giveGunToPlayer(player, weaponId);
                if (success) {
                    player.sendMessage(ChatColor.GREEN + "已替换手持武器为: " + ChatColor.GOLD + weaponName);
                }
            } else {
                player.sendMessage(ChatColor.RED + "请手持一把可替换的武器（槽位2-5）.");
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                return true;
            }
        } else {
            // 有空槽位，直接给予武器
            success = shootHelper.giveGunToPlayer(player, weaponId);
            if (success) {
                player.sendMessage(ChatColor.GREEN + "成功获得武器: " + ChatColor.GOLD + weaponName);
            }
        }

        if (success) {
            player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 0.8f, 1.2f);

            // 移除奖励记录
            pendingRewards.remove(hologramId);

            // 尝试移除全息图
            try {
                eu.decentsoftware.holograms.api.DHAPI.removeHologram(hologramId);
            } catch (Exception e) {
                plugin.getLogger().warning("移除全息图失败: " + e.getMessage());
            }

            return true;
        } else {
            player.sendMessage(ChatColor.RED + "给予武器失败，请稍后再试!");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            return true;
        }
    }

    /**
     * 获取可用的武器槽位
     *
     * @param player 玩家
     * @param weaponId 要获取的武器ID
     * @return 可用的槽位索引，-1表示没有可用槽位，-2表示手持相同武器，-3表示有相同武器但没有手持，-4表示手持其他武器可替换
     */
    private int getAvailableWeaponSlot(Player player, String weaponId) {
        String targetWeaponName = getWeaponName(weaponId);

        plugin.getLogger().info("【槽位检查】开始检查玩家 " + player.getName() + " 的武器槽位, 目标武器: " + weaponId + " (" + targetWeaponName + ")");

        // 检查玩家背包中是否已有相同的武器
        boolean hasSameWeapon = false;
        int sameWeaponSlot = -1;

        for (int slot = 1; slot <= 4; slot++) {
            ItemStack item = player.getInventory().getItem(slot);
            if (item != null && isWeapon(item)) {
                String itemName = item.hasItemMeta() && item.getItemMeta().hasDisplayName()
                    ? ChatColor.stripColor(item.getItemMeta().getDisplayName()) : item.getType().name();
                plugin.getLogger().info("【槽位检查】槽位 " + (slot + 1) + " 有武器: " + itemName);

                // 检查是否为相同的武器
                if (isSameWeapon(item, weaponId, targetWeaponName)) {
                    plugin.getLogger().info("【槽位检查】找到相同武器在槽位 " + (slot + 1));
                    hasSameWeapon = true;
                    sameWeaponSlot = slot;
                    break;
                }
            } else {
                plugin.getLogger().info("【槽位检查】槽位 " + (slot + 1) + " 为空或不是武器");
            }
        }

        // 如果有相同武器，检查是否手持
        if (hasSameWeapon) {
            int heldSlot = player.getInventory().getHeldItemSlot();
            if (heldSlot == sameWeaponSlot) {
                // 手持相同武器，可以补充弹药
                plugin.getLogger().info("玩家手持相同武器 " + targetWeaponName + "，可以补充弹药");
                return -2;
            } else {
                // 有相同武器但没有手持，需要先切换
                plugin.getLogger().info("玩家有相同武器 " + targetWeaponName + " 但没有手持，需要先切换");
                return -3;
            }
        }

        // 检查当前手持的物品是否为武器（可替换）
        int heldSlot = player.getInventory().getHeldItemSlot();
        if (heldSlot >= 1 && heldSlot <= 4) { // 只考虑槽位2-5（索引1-4）
            ItemStack heldItem = player.getInventory().getItemInMainHand();
            if (heldItem != null && isWeapon(heldItem)) {
                plugin.getLogger().info("玩家手持其他武器，可以替换");
                return -4; // 新的特殊值，表示需要替换手持的其他武器
            }
        }

        // 检查槽位2-5（索引1-4）是否有空位
        for (int slot = 1; slot <= 4; slot++) {
            ItemStack item = player.getInventory().getItem(slot);
            if (item == null || item.getType() == Material.AIR) {
                plugin.getLogger().info("找到空武器槽位: " + slot);
                return slot; // 返回空槽位索引
            }
        }

        // 没有空槽位，返回-1
        plugin.getLogger().info("没有可用的武器槽位");
        return -1;
    }

    /**
     * 注册DecentHolograms点击监听器 由于不能直接访问DecentHolograms的事件，我们在onEnable中动态注册
     */
    public void registerDecentHologramsListener() {
        // 玩家交互事件已经通过实现Listener接口注册
        plugin.getLogger().info("幸运箱点击监听器已注册");
    }

    /**
     * 处理玩家交互事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        // 只处理右键点击空气或方块的事件
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Player player = event.getPlayer();

        // 如果是箱子方块，检查是否是奖励箱
        if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            Block clickedBlock = event.getClickedBlock();
            if (clickedBlock != null && (clickedBlock.getType() == Material.CHEST || clickedBlock.getType() == Material.TRAPPED_CHEST)) {
                // 检查是否为奖励箱的点击
                Location blockLocation = clickedBlock.getLocation();

                // 查找该箱子所属的游戏
                for (String gameName : gameManager.getGameNames()) {
                    // 检查游戏配置是否存在（意味着游戏已加载）
                    if (gameManager.getGameConfig(gameName) != null) {
                        // 检查是否有奖励待领取
                        if (handleLuckyBoxInteraction(player, blockLocation, gameName)) {
                            event.setCancelled(true);
                            plugin.getLogger().info("玩家 " + player.getName() + " 成功与箱子交互");
                            return;
                        }
                    }
                }
            }
        }

        // 检查玩家附近是否有幸运箱全息图
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection().normalize();

        // 在玩家前方6格范围内检查全息图
        String closestHologramId = null;
        double minDistance = 6.0; // 最大检测距离

        // 遍历待领取奖励映射
        for (Map.Entry<String, String> entry : pendingRewards.entrySet()) {
            String hologramId = entry.getKey();

            // 跳过非全息图ID的键
            if (!hologramId.startsWith("lucky_box_drawing_")) {
                continue;
            }

            // 获取全息图位置
            Location hologramLocation = null;
            try {
                eu.decentsoftware.holograms.api.holograms.Hologram hologram
                        = eu.decentsoftware.holograms.api.DHAPI.getHologram(hologramId);
                if (hologram != null) {
                    hologramLocation = hologram.getLocation();
                    // 缓存位置信息
                    hologramLocations.put(hologramId, hologramLocation);
                }
            } catch (Exception e) {
                // 尝试从缓存获取位置
                hologramLocation = hologramLocations.get(hologramId);
            }

            // 如果无法获取位置，跳过
            if (hologramLocation == null) {
                continue;
            }

            // 检查玩家与全息图的距离
            if (hologramLocation.getWorld().equals(player.getWorld())) {
                double distance = hologramLocation.distance(eyeLocation);
                if (distance < minDistance) {
                    // 检查玩家是否面向全息图
                    Vector toHologram = hologramLocation.clone().subtract(eyeLocation).toVector().normalize();
                    double dotProduct = direction.dot(toHologram);

                    // 使用更宽松的视角判断（cos 45度约为0.7071）
                    if (dotProduct > 0.7071) {
                        closestHologramId = hologramId;
                        minDistance = distance;
                    }
                }
            }
        }

        // 如果找到了可交互的全息图，处理点击
        if (closestHologramId != null) {
            if (handleHologramClick(player, closestHologramId)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 成功与全息图交互");
                event.setCancelled(true);
            }
        }
    }

    /**
     * 为DecentHolograms提供的点击处理器
     */
    @EventHandler
    public void onPlayerInteractEntity(org.bukkit.event.player.PlayerInteractEntityEvent event) {
        // 检查是否点击了ArmorStand
        if (event.getRightClicked() instanceof org.bukkit.entity.ArmorStand) {
            Player player = event.getPlayer();

            // 尝试获取最近的全息图ID
            org.bukkit.entity.Entity entity = event.getRightClicked();
            Location clickedLocation = entity.getLocation();

            // 找到最近的幸运箱全息图
            String closestHologramId = null;
            double minDistance = 2.0; // 最大检测距离

            for (String hologramId : pendingRewards.keySet()) {
                try {
                    eu.decentsoftware.holograms.api.holograms.Hologram hologram
                            = eu.decentsoftware.holograms.api.DHAPI.getHologram(hologramId);
                    if (hologram != null && hologram.getLocation().getWorld().equals(clickedLocation.getWorld())) {
                        double distance = hologram.getLocation().distance(clickedLocation);
                        if (distance < minDistance) {
                            minDistance = distance;
                            closestHologramId = hologramId;
                        }
                    }
                } catch (Exception e) {
                    // 尝试从缓存获取位置
                    Location hologramLocation = hologramLocations.get(hologramId);
                    if (hologramLocation != null && hologramLocation.getWorld().equals(clickedLocation.getWorld())) {
                        double distance = hologramLocation.distance(clickedLocation);
                        if (distance < minDistance) {
                            minDistance = distance;
                            closestHologramId = hologramId;
                        }
                    }
                }
            }

            // 如果找到了全息图，处理点击
            if (closestHologramId != null) {
                if (handleHologramClick(player, closestHologramId)) {
                    event.setCancelled(true); // 取消原事件
                }
            }
        }
    }

    /**
     * 查找离玩家最近的幸运箱
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 最近的幸运箱ID，如果没有找到则返回null
     */
    public String findClosestLuckyBox(Player player, String gameName) {
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig == null) {
            return null;
        }

        ConfigurationSection luckyBoxesSection = gameConfig.getConfigurationSection("luckyBoxes");
        if (luckyBoxesSection == null) {
            return null;
        }

        Location playerLocation = player.getLocation();
        String closestBoxId = null;
        double minDistance = Double.MAX_VALUE;

        // 遍历所有幸运箱，找到最近的一个
        for (String boxId : luckyBoxesSection.getKeys(false)) {
            ConfigurationSection boxSection = luckyBoxesSection.getConfigurationSection(boxId);
            if (boxSection == null) {
                continue;
            }

            String worldName = boxSection.getString("world");
            double x = boxSection.getDouble("x");
            double y = boxSection.getDouble("y");
            double z = boxSection.getDouble("z");

            // 检查世界是否匹配
            if (worldName == null || !worldName.equals(playerLocation.getWorld().getName())) {
                continue;
            }

            Location boxLocation = new Location(playerLocation.getWorld(), x, y, z);
            double distance = playerLocation.distance(boxLocation);

            // 只考虑100格范围内的箱子
            if (distance < 100 && distance < minDistance) {
                minDistance = distance;
                closestBoxId = boxId;
            }
        }

        return closestBoxId;
    }

    /**
     * 移除指定的幸运箱
     *
     * @param gameName 游戏名称
     * @param boxId 箱子ID
     * @return 是否移除成功
     */
    public boolean removeLuckyBox(String gameName, String boxId) {
        try {
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
                return false;
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);
            String boxPath = "luckyBoxes." + boxId;

            if (!gameConfig.isSet(boxPath)) {
                plugin.getLogger().warning("幸运箱 '" + boxId + "' 不存在于游戏 '" + gameName + "' 中");
                return false;
            }

            // 获取箱子位置信息
            String worldName = gameConfig.getString(boxPath + ".world");
            double x = gameConfig.getDouble(boxPath + ".x");
            double y = gameConfig.getDouble(boxPath + ".y");
            double z = gameConfig.getDouble(boxPath + ".z");

            // 移除箱子方块（如果存在）
            if (worldName != null) {
                World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    Location boxLocation = new Location(world, x, y, z);
                    Block block = boxLocation.getBlock();
                    if (block.getType() == Material.CHEST || block.getType() == Material.TRAPPED_CHEST) {
                        block.setType(Material.AIR);
                        plugin.getLogger().info("已移除幸运箱方块: " + boxLocation);
                    }
                }
            }

            // 移除悬浮文字
            String hologramKey = gameName + "_" + boxId;
            ArmorStand hologram = luckyBoxHolograms.get(hologramKey);
            if (hologram != null && !hologram.isDead()) {
                hologram.remove();
                plugin.getLogger().info("已移除幸运箱悬浮文字: " + hologramKey);
            }
            luckyBoxHolograms.remove(hologramKey);

            // 从配置文件中移除
            gameConfig.set(boxPath, null);
            gameConfig.save(gameFile);

            // 清理相关的待领取奖励
            String rewardKey = gameName + "_" + boxId + "_reward";
            pendingRewards.remove(rewardKey);

            plugin.getLogger().info("成功移除幸运箱 '" + boxId + "' 从游戏 '" + gameName + "'");
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("移除幸运箱时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
