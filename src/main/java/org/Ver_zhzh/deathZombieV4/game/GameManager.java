package org.Ver_zhzh.deathZombieV4.game;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

public class GameManager {

    private final DeathZombieV4 plugin;
    private final File gameFolder;
    private final Map<String, File> gameFiles;
    // 存储游戏启用状态: <游戏名, 是否启用>
    private final Map<String, Boolean> gameEnabledStatus;

    public GameManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameFolder = new File(plugin.getDataFolder(), "game");
        this.gameFiles = new HashMap<>();
        this.gameEnabledStatus = new HashMap<>();

        if (!gameFolder.exists()) {
            gameFolder.mkdirs();
        }

        loadGames();
    }

    private void loadGames() {
        if (gameFolder.exists() && gameFolder.isDirectory()) {
            File[] files = gameFolder.listFiles((dir, name) -> name.endsWith(".yml"));
            if (files != null) {
                for (File file : files) {
                    String gameName = file.getName().replace(".yml", "");
                    gameFiles.put(gameName, file);

                    // 从配置文件中加载游戏启用状态
                    FileConfiguration config = YamlConfiguration.loadConfiguration(file);
                    boolean enabled = config.getBoolean("enabled", false);
                    gameEnabledStatus.put(gameName, enabled);

                    if (enabled) {
                        plugin.getLogger().info("已加载启用的游戏: " + gameName);
                    }
                }

                int enabledCount = 0;
                for (Boolean enabled : gameEnabledStatus.values()) {
                    if (enabled) {
                        enabledCount++;
                    }
                }
                plugin.getLogger().info("共加载了 " + gameFiles.size() + " 个游戏，其中 " + enabledCount + " 个游戏已启用");
            }
        }
    }

    public boolean gameExists(String gameName) {
        // 先检查内存中是否存在
        if (gameFiles.containsKey(gameName)) {
            return true;
        }

        // 如果内存中不存在，尝试检查文件系统
        File gameFile = new File(gameFolder, gameName + ".yml");
        if (gameFile.exists()) {
            // 文件存在，加载到内存
            try {
                FileConfiguration config = new YamlConfiguration();
                config.load(gameFile);
                gameFiles.put(gameName, gameFile);
                boolean enabled = config.getBoolean("enabled", false);
                gameEnabledStatus.put(gameName, enabled);
                plugin.getLogger().info("加载到游戏文件: " + gameName);
                return true;
            } catch (Exception e) {
                plugin.getLogger().warning("加载游戏文件失败: " + gameName + ", 错误: " + e.getMessage());
                return false;
            }
        }

        return false;
    }

    public boolean createGame(String gameName) {
        if (gameExists(gameName)) {
            return false;
        }

        File gameFile = new File(gameFolder, gameName + ".yml");
        FileConfiguration config = new YamlConfiguration();

        try {
            config.set("name", gameName);
            config.set("maxPlayers", 10); // 默认最大玩家数
            config.save(gameFile);
            gameFiles.put(gameName, gameFile);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean setSpawnLocation(String gameName, Location location) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        config.set("spawn.world", location.getWorld().getName());
        config.set("spawn.x", location.getX());
        config.set("spawn.y", location.getY());
        config.set("spawn.z", location.getZ());
        config.set("spawn.yaw", location.getYaw());
        config.set("spawn.pitch", location.getPitch());

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean setMaxPlayers(String gameName, int maxPlayers) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        config.set("maxPlayers", maxPlayers);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean addWindowsRegion(String gameName, String windowName, Region region) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 使用窗户名称作为配置节点
        String windowPath = "windows." + windowName;

        // 保存窗户区域信息
        config.set(windowPath + ".world", region.getWorld().getName());
        config.set(windowPath + ".minX", region.getMinX());
        config.set(windowPath + ".minY", region.getMinY());
        config.set(windowPath + ".minZ", region.getMinZ());
        config.set(windowPath + ".maxX", region.getMaxX());
        config.set(windowPath + ".maxY", region.getMaxY());
        config.set(windowPath + ".maxZ", region.getMaxZ());

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 添加窗户区域（旧方法，保留向后兼容性）
     *
     * @param gameName 游戏名称
     * @param region 窗户区域
     * @return 是否添加成功
     */
    public boolean addWindowsRegion(String gameName, Region region) {
        // 使用默认窗户名称
        String windowName = "window" + System.currentTimeMillis();
        return addWindowsRegion(gameName, windowName, region);
    }

    /**
     * 设置游戏的总回合数
     *
     * @param gameName 游戏名称
     * @param rounds 回合数
     * @return 操作是否成功
     */
    public boolean setRounds(String gameName, int rounds) {
        if (!gameExists(gameName)) {
            return false;
        }

        if (rounds < 1) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        config.set("rounds", rounds);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置游戏特定回合的僵尸数量
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param count 僵尸数量
     * @return 操作是否成功
     */
    public boolean setZombieCount(String gameName, int round, int count) {
        if (!gameExists(gameName)) {
            return false;
        }

        if (round < 1 || count < 1) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 检查是否设置了总回合数
        if (!config.contains("rounds")) {
            return false;
        }

        int totalRounds = config.getInt("rounds");
        if (round > totalRounds) {
            return false;
        }

        String path = "zombieCount.round" + round;
        config.set(path, count);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置游戏门的状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param lockStatus 锁定状态 ("lock" 或 "unlock")
     * @param unlockPrice 解锁价格 (仅在lockStatus为"lock"时有效)
     * @return 是否设置成功
     */
    public boolean setDoor(String gameName, String doorName, String lockStatus, int unlockPrice) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        String doorPath = "doors." + doorName.replace(".", "_");
        boolean isLocked = "lock".equalsIgnoreCase(lockStatus);

        config.set(doorPath + ".locked", isLocked);

        // 如果门是锁定的，才设置解锁价格
        if (isLocked) {
            config.set(doorPath + ".unlockPrice", unlockPrice);
        } else {
            config.set(doorPath + ".unlockPrice", null); // 移除价格设置
        }

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置游戏门的状态（包含区域信息）
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param lockStatus 锁定状态 ("lock" 或 "unlock")
     * @param unlockPrice 解锁价格 (仅在lockStatus为"lock"时有效)
     * @param region 门区域
     * @return 是否设置成功
     */
    public boolean setDoor(String gameName, String doorName, String lockStatus, int unlockPrice, Region region) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        String doorPath = "doors." + doorName.replace(".", "_");
        boolean isLocked = "lock".equalsIgnoreCase(lockStatus);

        config.set(doorPath + ".locked", isLocked);

        // 如果门是锁定的，才设置解锁价格
        if (isLocked) {
            config.set(doorPath + ".unlockPrice", unlockPrice);
        } else {
            config.set(doorPath + ".unlockPrice", null); // 移除价格设置
        }

        // 保存区域信息
        if (region != null) {
            config.set(doorPath + ".region.world", region.getWorld().getName());
            config.set(doorPath + ".region.minX", region.getMinX());
            config.set(doorPath + ".region.minY", region.getMinY());
            config.set(doorPath + ".region.minZ", region.getMinZ());
            config.set(doorPath + ".region.maxX", region.getMaxX());
            config.set(doorPath + ".region.maxY", region.getMaxY());
            config.set(doorPath + ".region.maxZ", region.getMaxZ());

            // 保存NPC生成位置（使用区域中心点，并向上偏移一格）
            double npcX = (region.getMinX() + region.getMaxX()) / 2;
            double npcY = region.getMinY() + 1; // 向上偏移一格
            double npcZ = (region.getMinZ() + region.getMaxZ()) / 2;

            config.set(doorPath + ".npcLocation.world", region.getWorld().getName());
            config.set(doorPath + ".npcLocation.x", npcX);
            config.set(doorPath + ".npcLocation.y", npcY);
            config.set(doorPath + ".npcLocation.z", npcZ);
        }

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取游戏中的所有门
     *
     * @param gameName 游戏名称
     * @return 门信息的Map，如果游戏不存在则返回null
     */
    public Map<String, Map<String, Object>> getDoors(String gameName) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法获取门信息: 游戏 '" + gameName + "' 不存在");
            return null;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        Map<String, Map<String, Object>> doors = new HashMap<>();

        if (config.isConfigurationSection("doors")) {
            ConfigurationSection doorsSection = config.getConfigurationSection("doors");

            for (String doorName : doorsSection.getKeys(false)) {
                Map<String, Object> doorInfo = new HashMap<>();

                // 输出调试信息
                plugin.getLogger().info("正在读取门 " + doorName + " 的信息");

                doorInfo.put("locked", doorsSection.getBoolean(doorName + ".locked", true));
                doorInfo.put("unlockPrice", doorsSection.getInt(doorName + ".unlockPrice", 0));
                // 添加InGameOpening字段，默认为false
                doorInfo.put("InGameOpening", doorsSection.getBoolean(doorName + ".InGameOpening", false));

                // 读取区域信息
                if (doorsSection.isConfigurationSection(doorName + ".region")) {
                    ConfigurationSection regionSection = doorsSection.getConfigurationSection(doorName + ".region");
                    Map<String, Object> regionData = new HashMap<>();

                    for (String key : regionSection.getKeys(false)) {
                        regionData.put(key, regionSection.get(key));
                    }

                    doorInfo.put("region", regionData);
                    plugin.getLogger().info("读取到门 " + doorName + " 的区域信息");
                } else {
                    plugin.getLogger().warning("门 " + doorName + " 没有区域信息");
                }

                // 读取NPC位置信息
                if (doorsSection.isConfigurationSection(doorName + ".npcLocation")) {
                    ConfigurationSection npcSection = doorsSection.getConfigurationSection(doorName + ".npcLocation");
                    Map<String, Object> npcData = new HashMap<>();

                    for (String key : npcSection.getKeys(false)) {
                        npcData.put(key, npcSection.get(key));
                    }

                    doorInfo.put("npcLocation", npcData);
                    plugin.getLogger().info("读取到门 " + doorName + " 的NPC位置信息: " + npcData);
                } else {
                    plugin.getLogger().info("门 " + doorName + " 没有NPC位置信息，将使用计算位置");
                }

                doors.put(doorName, doorInfo);
            }
        } else {
            plugin.getLogger().warning("游戏 '" + gameName + "' 没有定义任何门");
        }

        return doors;
    }

    /**
     * 设置门的游戏内开启状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param inGameOpening 游戏内开启状态
     * @return 是否设置成功
     */
    public boolean setDoorInGameOpening(String gameName, String doorName, boolean inGameOpening) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        String doorPath = "doors." + doorName.replace(".", "_");

        // 检查门是否存在
        if (!config.isSet(doorPath)) {
            return false;
        }

        config.set(doorPath + ".InGameOpening", inGameOpening);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取门的游戏内开启状态
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 游戏内开启状态，如果门不存在则返回false
     */
    public boolean getDoorInGameOpening(String gameName, String doorName) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        String doorPath = "doors." + doorName.replace(".", "_");

        return config.getBoolean(doorPath + ".InGameOpening", false);
    }

    /**
     * 删除游戏中的一个门
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 是否删除成功
     */
    public boolean removeDoor(String gameName, String doorName) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        String doorPath = "doors." + doorName.replace(".", "_");

        if (!config.isSet(doorPath)) {
            return false; // 门不存在
        }

        config.set(doorPath, null); // 移除门设置

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查游戏配置是否完整
     *
     * @param gameName 游戏名称
     * @return 配置检查结果的Map，键为检查项，值为是否通过检查
     */
    public Map<String, Boolean> checkGameConfig(String gameName) {
        Map<String, Boolean> results = new HashMap<>();

        if (!gameExists(gameName)) {
            results.put("gameExists", false);
            return results;
        }

        results.put("gameExists", true);

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 检查出生点
        results.put("spawnPoint", config.contains("spawn"));

        // 检查最大玩家数
        results.put("maxPlayers", config.contains("maxPlayers"));

        // 检查窗户区域
        ConfigurationSection windowsSection = config.getConfigurationSection("windows");
        results.put("windows", config.contains("windows") && windowsSection != null && !windowsSection.getKeys(false).isEmpty());

        // 检查回合数
        results.put("rounds", config.contains("rounds"));

        // 检查僵尸数量配置
        boolean allRoundsConfigured = true;
        if (config.contains("rounds")) {
            int totalRounds = config.getInt("rounds");
            for (int i = 1; i <= totalRounds; i++) {
                String path = "zombieCount.round" + i;
                if (!config.contains(path)) {
                    allRoundsConfigured = false;
                    break;
                }
            }
        } else {
            allRoundsConfigured = false;
        }

        results.put("zombieCount", allRoundsConfigured);

        // 检查门设置（可选配置）
        results.put("doors", config.contains("doors"));

        // 检查僵尸生成点
        ConfigurationSection zombieSpawnsSection = config.getConfigurationSection("zombieSpawns");
        results.put("zombieSpawns", config.contains("zombieSpawns") && zombieSpawnsSection != null && !zombieSpawnsSection.getKeys(false).isEmpty());

        // 检查购买点（可选配置）
        ConfigurationSection buyPointsSection = config.getConfigurationSection("buyPoints");
        results.put("buyPoints", config.contains("buyPoints") && buyPointsSection != null && !buyPointsSection.getKeys(false).isEmpty());

        // 检查幸运箱（可选配置）
        ConfigurationSection luckyBoxesSection = config.getConfigurationSection("luckyBoxes");
        results.put("luckyBoxes", config.contains("luckyBoxes") && luckyBoxesSection != null && !luckyBoxesSection.getKeys(false).isEmpty());

        // 检查电源按钮（可选配置）
        boolean hasPowerButton = config.contains("power_button") || config.contains("powerButton");
        results.put("powerButton", hasPowerButton);

        // 检查回合模式配置（可选配置）
        ConfigurationSection roundModesSection = config.getConfigurationSection("roundModes");
        results.put("roundModes", config.contains("roundModes") && roundModesSection != null && !roundModesSection.getKeys(false).isEmpty());

        // 检查初始装备（可选配置）
        ConfigurationSection initialEquipmentSection = config.getConfigurationSection("initialEquipment");
        results.put("initialEquipment", config.contains("initialEquipment") && initialEquipmentSection != null && !initialEquipmentSection.getKeys(false).isEmpty());

        // 检查护甲装备（可选配置）
        ConfigurationSection armorEquipmentSection = config.getConfigurationSection("armorEquipment");
        results.put("armorEquipment", config.contains("armorEquipment") && armorEquipmentSection != null && !armorEquipmentSection.getKeys(false).isEmpty());

        // 检查所有必需配置是否完整（僵尸生成点现在是必需的）
        boolean allConfigComplete = results.get("spawnPoint")
                && results.get("maxPlayers")
                && results.get("windows")
                && results.get("rounds")
                && results.get("zombieCount")
                && results.get("zombieSpawns");

        results.put("complete", allConfigComplete);

        return results;
    }

    /**
     * 获取游戏配置
     *
     * @param gameName 游戏名称
     * @return 配置文件对象，如果游戏不存在则返回null
     */
    public FileConfiguration getGameConfig(String gameName) {
        if (!gameExists(gameName)) {
            return null;
        }

        return YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
    }

    /**
     * 获取游戏文件
     *
     * @param gameName 游戏名称
     * @return 游戏文件对象，如果游戏不存在则返回null
     */
    public File getGameFile(String gameName) {
        if (!gameExists(gameName)) {
            return null;
        }

        return gameFiles.get(gameName);
    }

    /**
     * 获取所有游戏名称
     *
     * @return 游戏名称列表
     */
    public List<String> getGameNames() {
        return new ArrayList<>(gameFiles.keySet());
    }

    /**
     * 加载所有游戏配置 用于WebUI预加载
     */
    public void loadAllGames() {
        if (!gameFolder.exists() || !gameFolder.isDirectory()) {
            plugin.getLogger().warning("游戏目录不存在或不是目录（路径：" + gameFolder.getAbsolutePath() + "），无法加载游戏");
            return;
        }

        int count = 0;
        File[] files = gameFolder.listFiles((dir, name) -> name.endsWith(".yml"));

        if (files == null || files.length == 0) {
            plugin.getLogger().info("游戏目录为空，没有游戏可加载（路径：" + gameFolder.getAbsolutePath() + "）");
            return;
        }

        plugin.getLogger().info("正在加载游戏目录：" + gameFolder.getAbsolutePath() + "，发现 " + files.length + " 个游戏文件");

        for (File file : files) {
            String gameName = file.getName().replace(".yml", "");
            if (gameName.equals("Template")) {
                continue; // 跳过模板文件
            }

            try {
                FileConfiguration config = new YamlConfiguration();
                config.load(file);

                // 更新游戏文件映射
                gameFiles.put(gameName, file);

                plugin.getLogger().info("已加载游戏配置: " + gameName);
                count++;
            } catch (Exception e) {
                plugin.getLogger().warning("加载游戏 " + gameName + " 失败: " + e.getMessage());
            }
        }

        plugin.getLogger().info("共加载了 " + count + " 个游戏配置");
    }

    /**
     * 设置僵尸生成点
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @param zombieType 僵尸类型(id|idn|idc)
     * @param location 生成点位置
     * @return 操作是否成功
     */
    public boolean setZombieSpawn(String gameName, String spawnName, String zombieType, Location location) {
        if (!gameExists(gameName)) {
            return false;
        }

        // 验证僵尸类型
        if (!zombieType.equals("id") && !zombieType.equals("idn") && !zombieType.equals("idc")) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");

        // 设置生成点信息
        config.set(spawnPath + ".type", zombieType);
        config.set(spawnPath + ".world", location.getWorld().getName());
        config.set(spawnPath + ".x", location.getX());
        config.set(spawnPath + ".y", location.getY());
        config.set(spawnPath + ".z", location.getZ());
        config.set(spawnPath + ".enabled", false); // 默认禁用

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置门与僵尸生成点的关联
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param spawnName 僵尸生成点名称
     * @return 操作是否成功
     */
    public boolean setDoorZombieSpawn(String gameName, String doorName, String spawnName) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        String doorPath = "doors." + doorName.replace(".", "_");
        String spawnPath = "zombieSpawns." + spawnName;

        // 检查门和生成点是否存在
        if (!config.isSet(doorPath) || !config.isSet(spawnPath)) {
            return false;
        }

        // 获取当前关联的生成点列表，如果不存在则创建新列表
        List<String> linkedSpawns;
        if (config.isList(doorPath + ".linkedSpawns")) {
            linkedSpawns = config.getStringList(doorPath + ".linkedSpawns");
        } else {
            linkedSpawns = new ArrayList<>();
            // 如果存在旧的单一关联，将其添加到新列表中
            String oldLinkedSpawn = config.getString(doorPath + ".linkedSpawn");
            if (oldLinkedSpawn != null && !oldLinkedSpawn.isEmpty()) {
                linkedSpawns.add(oldLinkedSpawn);
                // 移除旧的单一关联配置
                config.set(doorPath + ".linkedSpawn", null);
            }
        }

        // 检查是否已经关联了该生成点
        if (!linkedSpawns.contains(spawnName)) {
            linkedSpawns.add(spawnName);
            plugin.getLogger().info("已将生成点 '" + spawnName + "' 添加到门 '" + doorName + "' 的关联列表中");
        } else {
            plugin.getLogger().info("生成点 '" + spawnName + "' 已经与门 '" + doorName + "' 关联");
        }

        // 保存关联列表
        config.set(doorPath + ".linkedSpawns", linkedSpawns);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取游戏中的所有僵尸生成点
     *
     * @param gameName 游戏名称
     * @return 生成点信息的Map，如果游戏不存在则返回null
     */
    public Map<String, Map<String, Object>> getZombieSpawns(String gameName) {
        if (!gameExists(gameName)) {
            return null;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        Map<String, Map<String, Object>> spawns = new HashMap<>();

        if (config.isConfigurationSection("zombieSpawns")) {
            ConfigurationSection spawnsSection = config.getConfigurationSection("zombieSpawns");

            for (String configSpawnName : spawnsSection.getKeys(false)) {
                Map<String, Object> spawnInfo = new HashMap<>();
                spawnInfo.put("type", spawnsSection.getString(configSpawnName + ".type"));
                spawnInfo.put("world", spawnsSection.getString(configSpawnName + ".world"));
                spawnInfo.put("x", spawnsSection.getDouble(configSpawnName + ".x"));
                spawnInfo.put("y", spawnsSection.getDouble(configSpawnName + ".y"));
                spawnInfo.put("z", spawnsSection.getDouble(configSpawnName + ".z"));
                spawnInfo.put("enabled", spawnsSection.getBoolean(configSpawnName + ".enabled", false));

                // 将配置文件中的键名转换回原始生成点名称（将下划线转换回点号）
                String originalSpawnName = configSpawnName.replace("_", ".");
                spawns.put(originalSpawnName, spawnInfo);
            }
        }

        return spawns;
    }

    /**
     * 设置僵尸生成点的启用状态
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @param enabled 是否启用
     * @return 是否设置成功
     */
    public boolean setZombieSpawnEnabled(String gameName, String spawnName, boolean enabled) {
        if (!gameExists(gameName)) {
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 检查生成点是否存在
        String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");
        if (!config.isSet(spawnPath)) {
            plugin.getLogger().warning("生成点 " + spawnName + " 在游戏 " + gameName + " 中不存在，路径: " + spawnPath);

            // 输出所有可用的生成点以便调试
            if (config.isConfigurationSection("zombieSpawns")) {
                ConfigurationSection spawnsSection = config.getConfigurationSection("zombieSpawns");
                plugin.getLogger().info("可用的生成点: " + String.join(", ", spawnsSection.getKeys(false)));
            }
            return false;
        }

        config.set(spawnPath + ".enabled", enabled);

        try {
            config.save(gameFiles.get(gameName));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置游戏特定回合的怪物生成模式
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @param spawnName 生成点名称
     * @param monsterType 怪物类型 (zombie/entity/npc)
     * @param monsterId 怪物ID
     * @param count 生成数量
     * @return 是否设置成功
     */
    public boolean setRoundMode(String gameName, int round, String spawnName, String monsterType, String monsterId, String count) {
        if (!gameExists(gameName)) {
            return false;
        }

        // 检查回合数是否有效
        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        if (!config.contains("rounds")) {
            return false;
        }

        int totalRounds = config.getInt("rounds");
        if (round < 1 || round > totalRounds) {
            return false;
        }

        // 检查生成点是否存在
        String spawnPath = "zombieSpawns." + spawnName.replace(".", "_");
        if (!config.isSet(spawnPath)) {
            return false;
        }

        // 验证怪物ID格式
        if (!isValidMonsterId(monsterId)) {
            return false;
        }

        // 验证数量
        int spawnCount;
        boolean isRandom = false;

        if (count.equalsIgnoreCase("random")) {
            isRandom = true;
            spawnCount = -1; // 标记为随机
        } else {
            try {
                spawnCount = Integer.parseInt(count);
                if (spawnCount < 1) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        // 提取类型和编号信息
        String type = "id"; // 默认类型
        String numberStr = "1"; // 默认编号

        // 从monsterId中提取类型和编号（优先使用monsterId的信息）
        // 注意：必须先检查更具体的前缀（idc、idn），然后再检查通用前缀（id）
        if (monsterId.startsWith("idc")) {
            type = "idc";
            numberStr = monsterId.substring(3);
        } else if (monsterId.startsWith("idn")) {
            type = "idn";
            numberStr = monsterId.substring(3);
        } else if (monsterId.startsWith("id")) {
            type = "id";
            numberStr = monsterId.substring(2);
        } else {
            // 纯数字ID，根据monsterType确定类型
            numberStr = monsterId;
            if ("zombie".equalsIgnoreCase(monsterType)) {
                type = "id";
            } else if ("entity".equalsIgnoreCase(monsterType)) {
                type = "idc";
            } else if ("npc".equalsIgnoreCase(monsterType)) {
                type = "idn";
            }
        }

        // 保存配置 - 使用"刷怪逻辑X"格式与WebUI保持一致
        String roundModePath = "roundModes.round" + round + "." + spawnName.replace(".", "_");

        // 检查该生成点下已有多少个刷怪逻辑
        ConfigurationSection spawnSection = config.getConfigurationSection(roundModePath);
        int logicCount = 1;
        if (spawnSection != null) {
            // 计算现有的刷怪逻辑数量
            for (String key : spawnSection.getKeys(false)) {
                if (key.startsWith("刷怪逻辑")) {
                    logicCount++;
                }
            }
        }

        // 创建新的刷怪逻辑节点
        String logicPath = roundModePath + ".刷怪逻辑" + logicCount;
        config.set(logicPath + ".monsterType", monsterType);
        config.set(logicPath + ".monsterId", monsterId);
        config.set(logicPath + ".count", isRandom ? "random" : String.valueOf(spawnCount));

        // 保存新增的类型和编号信息
        config.set(logicPath + ".type", type);
        config.set(logicPath + ".number", numberStr);

        try {
            config.save(gameFiles.get(gameName));
            plugin.getLogger().info("成功保存刷怪逻辑到: " + logicPath);
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存刷怪逻辑失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 验证怪物ID是否有效
     *
     * @param monsterId 怪物ID
     * @return 是否有效
     */
    private boolean isValidMonsterId(String monsterId) {
        // 支持多种格式:
        // 1. id1, id2, ..., idc1, idc2, ..., idn1, idn2, ...
        // 2. 纯数字: 1, 2, 3, ...
        return monsterId.matches("^(id\\d+|idc\\d+|idn\\d+|\\d+)$");
    }

    /**
     * 获取指定回合的怪物生成模式
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @return 生成模式配置映射，如果不存在则返回null
     */
    public Map<String, Map<String, Object>> getRoundModes(String gameName, int round) {
        ConfigurationSection gameConfig = getGameConfig(gameName);
        if (gameConfig == null) {
            return null;
        }

        String path = "roundModes.round" + round;
        ConfigurationSection roundSection = gameConfig.getConfigurationSection(path);
        if (roundSection == null) {
            // 尝试获取默认配置
            roundSection = gameConfig.getConfigurationSection("roundModes.default");
            if (roundSection == null) {
                return null;
            }
        }

        Map<String, Map<String, Object>> modes = new HashMap<>();

        for (String spawnName : roundSection.getKeys(false)) {
            ConfigurationSection spawnSection = roundSection.getConfigurationSection(spawnName);
            if (spawnSection != null) {
                // 检查是否有嵌套的刷怪逻辑
                boolean hasNestedLogic = false;
                for (String key : spawnSection.getKeys(false)) {
                    if (key.startsWith("刷怪逻辑") && spawnSection.isConfigurationSection(key)) {
                        hasNestedLogic = true;
                        break;
                    }
                }

                if (hasNestedLogic) {
                    // 处理嵌套的刷怪逻辑
                    for (String logicKey : spawnSection.getKeys(false)) {
                        if (logicKey.startsWith("刷怪逻辑") && spawnSection.isConfigurationSection(logicKey)) {
                            ConfigurationSection logicSection = spawnSection.getConfigurationSection(logicKey);
                            Map<String, Object> logicConfig = new HashMap<>();

                            // 读取刷怪逻辑配置
                            for (String key : logicSection.getKeys(false)) {
                                logicConfig.put(key, logicSection.get(key));
                            }

                            // 默认启用
                            if (!logicConfig.containsKey("enabled")) {
                                logicConfig.put("enabled", true);
                            }

                            // 使用组合键作为唯一标识符
                            String combinedKey = spawnName + "_" + logicKey;
                            modes.put(combinedKey, logicConfig);
                        }
                    }
                } else {
                    // 没有嵌套逻辑，按原来的方式处理
                    Map<String, Object> spawnConfig = new HashMap<>();

                    // 读取生成点配置
                    for (String key : spawnSection.getKeys(false)) {
                        spawnConfig.put(key, spawnSection.get(key));
                    }

                    // 从zombieSpawns配置中读取enabled状态，而不是默认设置为true
                    if (!spawnConfig.containsKey("enabled")) {
                        // 获取生成点的实际enabled状态
                        Map<String, Map<String, Object>> zombieSpawns = getZombieSpawns(gameName);
                        boolean actualEnabled = false; // 默认禁用

                        if (zombieSpawns != null && zombieSpawns.containsKey(spawnName)) {
                            Map<String, Object> spawnInfo = zombieSpawns.get(spawnName);
                            actualEnabled = (Boolean) spawnInfo.getOrDefault("enabled", false);
                        }

                        spawnConfig.put("enabled", actualEnabled);
                        plugin.getLogger().info("从zombieSpawns读取生成点 " + spawnName + " 的enabled状态: " + actualEnabled);
                    }

                    modes.put(spawnName, spawnConfig);
                }
            }
        }

        return modes;
    }

    /**
     * 获取僵尸生成位置
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @return 生成位置，如果不存在则返回null
     */
    public Location getZombieSpawnLocation(String gameName, String spawnName) {
        ConfigurationSection gameConfig = getGameConfig(gameName);
        if (gameConfig == null) {
            return null;
        }

        String path = "zombieSpawns." + spawnName;
        ConfigurationSection spawnSection = gameConfig.getConfigurationSection(path);
        if (spawnSection == null) {
            return null;
        }

        String worldName = spawnSection.getString("world");
        if (worldName == null) {
            return null;
        }

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            return null;
        }

        double x = spawnSection.getDouble("x");
        double y = spawnSection.getDouble("y");
        double z = spawnSection.getDouble("z");
        float yaw = (float) spawnSection.getDouble("yaw", 0.0);
        float pitch = (float) spawnSection.getDouble("pitch", 0.0);

        return new Location(world, x, y, z, yaw, pitch);
    }

    /**
     * 获取僵尸生成点附属位置
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @param attachmentId 附属ID
     * @param location 位置
     * @return 是否成功设置
     */
    public boolean setZombieSpawnAttachment(String gameName, String spawnName, String attachmentId, Location location) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法设置附属生成点位置: 游戏 '" + gameName + "' 不存在");
            return false;
        }

        if (location == null) {
            plugin.getLogger().warning("无法设置附属生成点位置: 位置为null");
            return false;
        }

        File gameFile = gameFiles.get(gameName);
        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFile);

        // 检查是否存在主生成点
        String mainSpawnPath = "zombieSpawns." + spawnName;
        if (!config.contains(mainSpawnPath)) {
            plugin.getLogger().warning("无法设置附属生成点位置: 主生成点 '" + spawnName + "' 不存在");
            return false;
        }

        // 设置附属生成点路径：zombieSpawns.<生成点名称>.attachments.<附属ID>
        String attachmentPath = mainSpawnPath + ".attachments." + attachmentId;

        // 保存附属生成点位置
        config.set(attachmentPath + ".world", location.getWorld().getName());
        config.set(attachmentPath + ".x", location.getX());
        config.set(attachmentPath + ".y", location.getY());
        config.set(attachmentPath + ".z", location.getZ());

        try {
            config.save(gameFile);
            plugin.getLogger().info("已设置 '" + gameName + "' 的生成点 '" + spawnName + "' 的附属位置 '" + attachmentId + "'");
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存游戏配置时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取僵尸生成点附属位置
     *
     * @param gameName 游戏名称
     * @param spawnName 生成点名称
     * @param attachmentId 附属ID
     * @return 附属生成点位置，如果不存在则返回null
     */
    public Location getZombieSpawnAttachmentLocation(String gameName, String spawnName, String attachmentId) {
        if (!gameExists(gameName)) {
            return null;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        String attachmentPath = "zombieSpawns." + spawnName + ".attachments." + attachmentId;

        // 检查附属生成点是否存在
        if (!config.contains(attachmentPath)) {
            return null;
        }

        String worldName = config.getString(attachmentPath + ".world");
        double x = config.getDouble(attachmentPath + ".x");
        double y = config.getDouble(attachmentPath + ".y");
        double z = config.getDouble(attachmentPath + ".z");

        if (worldName == null || plugin.getServer().getWorld(worldName) == null) {
            return null;
        }

        return new Location(plugin.getServer().getWorld(worldName), x, y, z);
    }

    /**
     * 获取僵尸附属生成点的位置
     *
     * @param gameName 游戏名称
     * @param spawnName 主出生点名称
     * @param attachmentId 附属ID
     * @return 附属生成点位置
     */
    public Location getZombieSpawnAttachment(String gameName, String spawnName, String attachmentId) {
        // 直接调用已有的方法
        return getZombieSpawnAttachmentLocation(gameName, spawnName, attachmentId);
    }

    /**
     * 设置游戏启用状态
     *
     * @param gameName 游戏名称
     * @param enabled 是否启用
     * @return 操作是否成功
     */
    public boolean setGameEnabled(String gameName, boolean enabled) {
        if (!gameExists(gameName)) {
            return false;
        }

        if (enabled) {
            // 如果要启用游戏，确保游戏配置完整
            Map<String, Boolean> configStatus = checkGameConfig(gameName);
            boolean allConfigured = true;
            for (Boolean status : configStatus.values()) {
                if (!status) {
                    allConfigured = false;
                    break;
                }
            }

            if (!allConfigured) {
                return false;
            }
        }

        // 更新内存中的状态
        gameEnabledStatus.put(gameName, enabled);

        // 将状态保存到游戏配置文件
        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        config.set("enabled", enabled);
        try {
            config.save(gameFiles.get(gameName));
            plugin.getLogger().info("游戏 " + gameName + " 的启用状态已更新为: " + enabled);
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("保存游戏 " + gameName + " 的启用状态时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取游戏启用状态
     *
     * @param gameName 游戏名称
     * @return 游戏是否启用，如果游戏不存在则返回false
     */
    public boolean isGameEnabled(String gameName) {
        return gameEnabledStatus.getOrDefault(gameName, false);
    }

    /**
     * 获取所有启用的游戏
     *
     * @return 启用的游戏名称列表
     */
    public List<String> getEnabledGames() {
        List<String> enabledGames = new ArrayList<>();
        for (Map.Entry<String, Boolean> entry : gameEnabledStatus.entrySet()) {
            if (entry.getValue()) {
                enabledGames.add(entry.getKey());
            }
        }
        return enabledGames;
    }

    /**
     * 获取人数最多的已启用游戏
     *
     * @return 人数最多的游戏名称，如果没有启用的游戏则返回null
     */
    public String getMostPopulatedGame() {
        List<String> enabledGames = getEnabledGames();
        if (enabledGames.isEmpty()) {
            return null;
        }

        String mostPopulatedGame = null;
        int maxPlayers = -1;

        GameSessionManager sessionManager = plugin.getGameSessionManager();
        for (String gameName : enabledGames) {
            int playerCount = sessionManager.getPlayerCount(gameName);
            if (playerCount > maxPlayers) {
                maxPlayers = playerCount;
                mostPopulatedGame = gameName;
            }
        }

        return mostPopulatedGame;
    }

    /**
     * 获取所有已定义的游戏名称
     *
     * @return 游戏名称集合
     */
    public Set<String> getAllGames() {
        return new HashSet<>(gameFiles.keySet());
    }

    /**
     * 设置游戏的最小玩家数量
     *
     * @param gameName 游戏名称
     * @param minPlayers 最小玩家数量
     * @return 是否成功设置
     */
    public boolean setMinPlayers(String gameName, int minPlayers) {
        if (!gameExists(gameName)) {
            return false;
        }

        File gameFile = gameFiles.get(gameName);
        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 确保设置的最小玩家数不会大于最大玩家数
        int maxPlayers = gameConfig.getInt("maxPlayers", 10);
        if (minPlayers > maxPlayers) {
            minPlayers = maxPlayers;
        }

        if (minPlayers < 1) {
            minPlayers = 1; // 最小值为1
        }

        gameConfig.set("minPlayers", minPlayers);

        try {
            gameConfig.save(gameFile);
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存游戏 '" + gameName + "' 的配置文件", e);
            return false;
        }
    }

    /**
     * 获取游戏的最小玩家数量
     *
     * @param gameName 游戏名称
     * @return 最小玩家数量，如果游戏不存在则返回1
     */
    public int getMinPlayers(String gameName) {
        if (!gameExists(gameName)) {
            return 1;
        }

        FileConfiguration gameConfig = getGameConfig(gameName);
        if (gameConfig == null) {
            return 1;
        }

        return gameConfig.getInt("minPlayers", 1);
    }

    /**
     * 获取指定怪物出生点的坐标。
     */
    /**
     * 保存游戏配置
     *
     * @param gameName 游戏名称
     * @return 是否保存成功
     */
    public boolean saveGameConfig(String gameName) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法保存游戏配置: 游戏 '" + gameName + "' 不存在");
            return false;
        }

        File gameFile = gameFiles.get(gameName);
        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        try {
            gameConfig.save(gameFile);
            plugin.getLogger().info("成功保存游戏 '" + gameName + "' 的配置文件");
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存游戏 '" + gameName + "' 的配置文件", e);
            return false;
        }
    }

    /**
     * 设置游戏等待时间
     *
     * @param gameName 游戏名称
     * @param waitTime 等待时间（秒）
     * @return 是否设置成功
     */
    public boolean setWaitTime(String gameName, int waitTime) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法设置等待时间: 游戏 '" + gameName + "' 不存在");
            return false;
        }

        if (waitTime < 5) {
            plugin.getLogger().warning("等待时间不能小于5秒");
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 设置等待时间
        config.set("waitTime", waitTime);

        try {
            config.save(gameFiles.get(gameName));
            plugin.getLogger().info("成功设置游戏 '" + gameName + "' 的等待时间为 " + waitTime + " 秒");
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存游戏 '" + gameName + "' 的等待时间", e);
            return false;
        }
    }

    /**
     * 获取游戏等待时间
     *
     * @param gameName 游戏名称
     * @return 等待时间（秒），如果未设置则返回默认值（10秒）
     */
    public int getWaitTime(String gameName) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法获取等待时间: 游戏 '" + gameName + "' 不存在");
            return 10; // 默认值
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 获取等待时间，如果未设置则使用默认值
        return config.getInt("waitTime", 10);
    }

    /**
     * 设置游戏的buff效果
     *
     * @param gameName 游戏名称
     * @param buffId buff的ID
     * @return 是否设置成功
     */
    public boolean setBuff(String gameName, String buffId) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法设置buff: 游戏 '" + gameName + "' 不存在");
            return false;
        }

        // 验证buffId是否有效
        if (!isValidBuffId(buffId)) {
            plugin.getLogger().warning("无效的buff ID: " + buffId);
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 设置buff
        config.set("buffs." + buffId, true);

        try {
            config.save(gameFiles.get(gameName));
            plugin.getLogger().info("成功为游戏 '" + gameName + "' 设置buff: " + buffId);
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存游戏 '" + gameName + "' 的buff设置", e);
            return false;
        }
    }

    /**
     * 移除游戏的buff效果
     *
     * @param gameName 游戏名称
     * @param buffId buff的ID
     * @return 是否移除成功
     */
    public boolean removeBuff(String gameName, String buffId) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法移除buff: 游戏 '" + gameName + "' 不存在");
            return false;
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));

        // 移除buff
        config.set("buffs." + buffId, null);

        try {
            config.save(gameFiles.get(gameName));
            plugin.getLogger().info("成功从游戏 '" + gameName + "' 移除buff: " + buffId);
            return true;
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存游戏 '" + gameName + "' 的buff设置", e);
            return false;
        }
    }

    /**
     * 获取游戏的所有buff
     *
     * @param gameName 游戏名称
     * @return buff ID的列表，如果游戏不存在则返回空列表
     */
    public List<String> getBuffs(String gameName) {
        if (!gameExists(gameName)) {
            plugin.getLogger().warning("无法获取buff: 游戏 '" + gameName + "' 不存在");
            return new ArrayList<>();
        }

        FileConfiguration config = YamlConfiguration.loadConfiguration(gameFiles.get(gameName));
        List<String> buffs = new ArrayList<>();

        if (config.isConfigurationSection("buffs")) {
            ConfigurationSection buffsSection = config.getConfigurationSection("buffs");
            for (String buffId : buffsSection.getKeys(false)) {
                if (buffsSection.getBoolean(buffId, false)) {
                    buffs.add(buffId);
                }
            }
        }

        return buffs;
    }

    /**
     * 验证buff ID是否有效
     *
     * @param buffId buff的ID
     * @return 是否有效
     */
    private boolean isValidBuffId(String buffId) {
        // 当前支持的buff ID列表
        List<String> validBuffs = Arrays.asList(
                "speed1", "speed2", "KeepFood",
                "health1", "health2", "health3", "health+", "health++", "health+++",
                "jump1"
        );
        return validBuffs.contains(buffId);
    }
}
