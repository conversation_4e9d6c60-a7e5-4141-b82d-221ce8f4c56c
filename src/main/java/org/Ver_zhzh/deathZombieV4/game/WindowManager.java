package org.Ver_zhzh.deathZombieV4.game;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.Ver_zhzh.deathZombieV4.utils.MessageManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Blaze;
import org.bukkit.entity.CaveSpider;
import org.bukkit.entity.Creeper;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Endermite;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.IronGolem;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.PigZombie;
import org.bukkit.entity.Player;
import org.bukkit.entity.Skeleton;
import org.bukkit.entity.Zombie;
import org.bukkit.scheduler.BukkitTask;

/**
 * 管理游戏中的窗户区域和状态
 */
public class WindowManager {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final ShootPluginHelper shootPluginHelper;
    private final MessageManager messageManager;

    // 存储每个游戏的窗户区域: <游戏名, <窗户名, 区域>>
    private final Map<String, Map<String, Region>> windowRegions = new HashMap<>();

    // 存储每个游戏的窗户方块状态: <游戏名, <窗户名, <坐标字符串, 是否完好>>>
    private final Map<String, Map<String, Map<String, Boolean>>> windowBlocks = new HashMap<>();

    // 存储每个游戏的窗户检查任务: <游戏名, 任务>
    private final Map<String, BukkitTask> windowCheckTasks = new HashMap<>();

    // 存储玩家修复窗户的冷却时间: <玩家UUID, <窗户名, 上次修复时间>>
    private final Map<UUID, Map<String, Long>> playerRepairCooldowns = new HashMap<>();

    // 存储窗户破坏的冷却时间: <游戏名, <窗户名, 上次破坏时间>>
    private final Map<String, Map<String, Long>> windowBreakCooldowns = new HashMap<>();

    // 修复冷却时间（毫秒）
    private static final long REPAIR_COOLDOWN = 1000; // 1秒

    // 窗户破坏冷却时间（毫秒）
    private static final long BREAK_COOLDOWN = 2000; // 2秒，降低冷却时间使僵尸更频繁破坏窗户

    // 窗户方块材质
    private static final Material WINDOW_MATERIAL = Material.OAK_FENCE;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public WindowManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.shootPluginHelper = plugin.getShootPluginHelper();
        this.messageManager = plugin.getMessageManager();
    }

    /**
     * 注册窗户区域
     *
     * @param gameName 游戏名称
     * @param windowName 窗户名称
     * @param region 窗户区域
     */
    public void registerWindowRegion(String gameName, String windowName, Region region) {
        if (!windowRegions.containsKey(gameName)) {
            windowRegions.put(gameName, new HashMap<>());
        }
        windowRegions.get(gameName).put(windowName, region);
        plugin.getLogger().info("已注册游戏 " + gameName + " 的窗户 " + windowName);
    }

    /**
     * 初始化游戏的所有窗户
     *
     * @param gameName 游戏名称
     */
    public void initializeWindows(String gameName) {
        plugin.getLogger().info("初始化游戏 " + gameName + " 的所有窗户");

        // 获取游戏配置中的窗户信息
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            plugin.getLogger().warning("无法获取游戏 " + gameName + " 的配置");
            return;
        }

        ConfigurationSection windowsSection = config.getConfigurationSection("windows");
        if (windowsSection == null) {
            plugin.getLogger().info("游戏 " + gameName + " 没有配置窗户");
            return;
        }

        // 初始化窗户区域和方块状态
        if (!windowRegions.containsKey(gameName)) {
            windowRegions.put(gameName, new HashMap<>());
        }

        if (!windowBlocks.containsKey(gameName)) {
            windowBlocks.put(gameName, new HashMap<>());
        }

        // 遍历所有窗户配置
        for (String windowName : windowsSection.getKeys(false)) {
            ConfigurationSection windowSection = windowsSection.getConfigurationSection(windowName);
            if (windowSection == null) {
                continue;
            }

            // 获取窗户区域
            String worldName = windowSection.getString("world");
            int minX = windowSection.getInt("minX");
            int minY = windowSection.getInt("minY");
            int minZ = windowSection.getInt("minZ");
            int maxX = windowSection.getInt("maxX");
            int maxY = windowSection.getInt("maxY");
            int maxZ = windowSection.getInt("maxZ");

            World world = Bukkit.getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("找不到世界 " + worldName + " 用于窗户 " + windowName);
                continue;
            }

            // 创建区域对象
            Region region = new Region(world, minX, minY, minZ, maxX, maxY, maxZ);
            windowRegions.get(gameName).put(windowName, region);

            // 初始化窗户方块状态
            windowBlocks.get(gameName).put(windowName, new HashMap<>());

            plugin.getLogger().info("已加载游戏 " + gameName + " 的窗户 " + windowName);
        }
    }

    /**
     * 填充窗户区域的方块
     *
     * @param gameName 游戏名称
     */
    public void fillWindowBlocks(String gameName) {
        if (!windowRegions.containsKey(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有注册窗户区域");
            return;
        }

        plugin.getLogger().info("开始填充游戏 " + gameName + " 的窗户方块");

        // 为每个窗户填充方块
        for (Map.Entry<String, Region> entry : windowRegions.get(gameName).entrySet()) {
            String windowName = entry.getKey();
            Region region = entry.getValue();

            // 确保窗户方块状态映射已初始化
            if (!windowBlocks.containsKey(gameName)) {
                windowBlocks.put(gameName, new HashMap<>());
            }
            if (!windowBlocks.get(gameName).containsKey(windowName)) {
                windowBlocks.get(gameName).put(windowName, new HashMap<>());
            }

            // 创建一个延迟任务，在主线程中填充方块
            Bukkit.getScheduler().runTask(plugin, () -> {
                int blocksPlaced = 0;

                for (int x = (int) region.getMinX(); x <= (int) region.getMaxX(); x++) {
                    for (int y = (int) region.getMinY(); y <= (int) region.getMaxY(); y++) {
                        for (int z = (int) region.getMinZ(); z <= (int) region.getMaxZ(); z++) {
                            Block block = region.getWorld().getBlockAt(x, y, z);

                            // 只在空气方块位置放置窗户方块
                            if (block.getType() == Material.AIR) {
                                block.setType(WINDOW_MATERIAL);
                                blocksPlaced++;

                                // 记录方块状态为完好
                                String blockKey = x + "," + y + "," + z;
                                windowBlocks.get(gameName).get(windowName).put(blockKey, true);
                            }
                        }
                    }
                }

                plugin.getLogger().info("已在游戏 " + gameName + " 的窗户 " + windowName + " 中放置 " + blocksPlaced + " 个方块");
            });
        }
    }

    /**
     * 移除窗户区域的所有方块
     *
     * @param gameName 游戏名称
     */
    public void removeAllWindowBlocks(String gameName) {
        if (!windowRegions.containsKey(gameName)) {
            return;
        }

        plugin.getLogger().info("移除游戏 " + gameName + " 的所有窗户方块");

        // 为每个窗户移除方块
        for (Map.Entry<String, Region> entry : windowRegions.get(gameName).entrySet()) {
            String windowName = entry.getKey();
            Region region = entry.getValue();

            // 创建一个延迟任务，在主线程中移除方块
            Bukkit.getScheduler().runTask(plugin, () -> {
                for (int x = (int) region.getMinX(); x <= (int) region.getMaxX(); x++) {
                    for (int y = (int) region.getMinY(); y <= (int) region.getMaxY(); y++) {
                        for (int z = (int) region.getMinZ(); z <= (int) region.getMaxZ(); z++) {
                            Block block = region.getWorld().getBlockAt(x, y, z);

                            // 只移除窗户方块
                            if (block.getType() == WINDOW_MATERIAL) {
                                block.setType(Material.AIR);
                            }
                        }
                    }
                }
            });

            // 清除窗户方块状态
            if (windowBlocks.containsKey(gameName) && windowBlocks.get(gameName).containsKey(windowName)) {
                windowBlocks.get(gameName).get(windowName).clear();
            }
        }
    }

    /**
     * 启动窗户检查任务
     *
     * @param gameName 游戏名称
     */
    public void startWindowCheckTask(String gameName) {
        // 取消之前的任务
        stopWindowCheckTask(gameName);

        plugin.getLogger().info("启动游戏 " + gameName + " 的窗户检查任务");

        // 创建新任务，每1秒检查一次 - 提高检查频率
        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> checkWindows(gameName), 20L, 20L);
        windowCheckTasks.put(gameName, task);
    }

    /**
     * 停止窗户检查任务
     *
     * @param gameName 游戏名称
     */
    public void stopWindowCheckTask(String gameName) {
        if (windowCheckTasks.containsKey(gameName)) {
            windowCheckTasks.get(gameName).cancel();
            windowCheckTasks.remove(gameName);
            plugin.getLogger().info("停止游戏 " + gameName + " 的窗户检查任务");
        }
    }

    /**
     * 检查窗户状态
     *
     * @param gameName 游戏名称
     */
    private void checkWindows(String gameName) {
        if (!windowRegions.containsKey(gameName) || !windowBlocks.containsKey(gameName)) {
            return;
        }

        // 检查游戏状态，只有在游戏运行时才进行窗户破坏检查
        if (plugin.getGameSessionManager().getGameState(gameName) != org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
            return;
        }

        // 遍历所有窗户
        for (String windowName : windowRegions.get(gameName).keySet()) {
            Region region = windowRegions.get(gameName).get(windowName);
            Map<String, Boolean> blockStates = windowBlocks.get(gameName).get(windowName);

            // 检查是否有僵尸或其他怪物在窗户附近
            boolean monsterNearby = false;
            List<Location> monsterLocations = new ArrayList<>();

            // 获取窗户中心位置
            double centerX = (region.getMinX() + region.getMaxX()) / 2;
            double centerY = (region.getMinY() + region.getMaxY()) / 2;
            double centerZ = (region.getMinZ() + region.getMaxZ()) / 2;
            Location centerLocation = new Location(region.getWorld(), centerX, centerY, centerZ);

            // 检查15格范围内的实体 - 增加检测范围
            for (Entity entity : centerLocation.getWorld().getNearbyEntities(centerLocation, 15, 15, 15)) {
                // 首先检查实体是否属于当前游戏
                if (!isGameEntity(entity, gameName)) {
                    continue;
                }

                // 扩大检测实体类型范围，特别加强idc和idn实体的检测
                if (entity instanceof Zombie
                        || entity instanceof PigZombie
                        || entity instanceof Creeper
                        || entity instanceof Skeleton
                        || entity instanceof org.bukkit.entity.WitherSkeleton // 添加凋零骷髅支持
                        || entity instanceof org.bukkit.entity.Stray // 添加流浪者支持
                        || entity instanceof org.bukkit.entity.Husk // 添加尸壳支持
                        || entity instanceof org.bukkit.entity.Drowned // 添加溺尸支持
                        || entity instanceof org.bukkit.entity.ZombieVillager // 添加僵尸村民支持
                        || entity instanceof Blaze
                        || entity instanceof CaveSpider
                        || entity instanceof Endermite
                        || entity instanceof IronGolem
                        || entity instanceof EnderDragon
                        || entity instanceof org.bukkit.entity.Vindicator // 添加卫道士支持
                        || entity instanceof org.bukkit.entity.Evoker // 添加唤魔者支持
                        || entity instanceof org.bukkit.entity.Ravager // 添加劫掠兽支持
                        || entity instanceof org.bukkit.entity.ZombieHorse // 添加僵尸马支持
                        || entity instanceof org.bukkit.entity.MagmaCube // 添加岩浆怪支持
                        || entity instanceof org.bukkit.entity.Shulker // 添加潜影贝支持
                        || entity instanceof org.bukkit.entity.Snowman // 添加雪傀儡支持
                        || entity instanceof org.bukkit.entity.Wither // 添加凋零支持
                        || entity instanceof org.bukkit.entity.Pillager // 添加掠夺者支持
                        || entity instanceof org.bukkit.entity.Piglin // 添加猪灵支持
                        || isWardenEntity(entity) // 检查是否是Warden实体（如果服务器支持）
                        || entity.hasMetadata("gameEntity")
                        || entity.hasMetadata("customZombieType")
                        || entity.hasMetadata("NPC")
                        || entity.hasMetadata("idcZombieEntity") // 特别检查idc实体标记
                        || entity.hasMetadata("mutationKing") // 检查异变之王标记
                        || entity.hasMetadata("mutantIronGolem") // 检查变异铁傅儡标记
                        || entity.hasMetadata("soulGuardian") // 检查灵魂坚守者标记
                        || entity.hasMetadata("mutantZombie04") // 检查变异僵尸04标记
                        || (entity instanceof LivingEntity && !(entity instanceof Player)
                        && entity.getCustomName() != null
                        && (entity.getCustomName().contains("感染") || entity.getCustomName().contains("僵尸")
                        || entity.getCustomName().contains("变异")
                        || entity.getCustomName().contains("id")
                        || entity.getCustomName().contains("idc")
                        || entity.getCustomName().contains("idn")
                        || entity.getCustomName().contains("zombie")
                        || entity.getCustomName().contains("灵魂")
                        || entity.getCustomName().contains("异变")
                        || entity.getCustomName().contains("灾厄")
                        || entity.getCustomName().contains("凋零")
                        || entity.getCustomName().contains("暗影")
                        || entity.getCustomName().contains("鲜血")
                        || entity.getCustomName().startsWith("游戏_")))) {

                    // 计算实体到窗户的距离
                    double distance = entity.getLocation().distance(centerLocation);
                    if (distance <= 8) { // 增加到8格范围
                        monsterNearby = true;
                        monsterLocations.add(entity.getLocation());
                        // 添加调试信息，只在debug模式下输出
                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("检测到游戏实体 " + entity.getType() + (entity.getCustomName() != null ? " (" + entity.getCustomName() + ")" : "") + " 在窗户 " + windowName + " 附近，距离: " + distance);
                        }
                    }
                }
            }

            // 如果有怪物在附近，随机破坏一个完好的窗户方块
            if (monsterNearby && !blockStates.isEmpty()) {
                // 检查破坏冷却时间
                if (!windowBreakCooldowns.containsKey(gameName)) {
                    windowBreakCooldowns.put(gameName, new HashMap<>());
                }

                long currentTime = System.currentTimeMillis();
                long lastBreakTime = windowBreakCooldowns.get(gameName).getOrDefault(windowName, 0L);

                // 如果冷却时间未到，跳过此窗户
                if (currentTime - lastBreakTime < BREAK_COOLDOWN) {
                    continue;
                }

                // 获取所有完好的方块
                List<String> intactBlocks = new ArrayList<>();
                for (Map.Entry<String, Boolean> entry : blockStates.entrySet()) {
                    if (entry.getValue()) {
                        intactBlocks.add(entry.getKey());
                    }
                }

                // 如果有完好的方块，随机选择一个破坏
                if (!intactBlocks.isEmpty()) {
                    // 选择最接近怪物的方块
                    String blockToBreak = getClosestBlockToMonsters(intactBlocks, monsterLocations, region.getWorld());

                    // 解析坐标
                    String[] coords = blockToBreak.split(",");
                    int x = Integer.parseInt(coords[0]);
                    int y = Integer.parseInt(coords[1]);
                    int z = Integer.parseInt(coords[2]);

                    // 移除方块
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        Block block = region.getWorld().getBlockAt(x, y, z);
                        if (block.getType() == WINDOW_MATERIAL) {
                            block.setType(Material.AIR);

                            // 更新方块状态
                            blockStates.put(blockToBreak, false);

                            // 播放破坏音效 - 更真实的僵尸破窗音效
                            block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR, 1.0f, 0.8f);
                            block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.BLOCK_GLASS_BREAK, 0.8f, 1.2f);
                        }
                    });

                    // 更新破坏冷却时间
                    windowBreakCooldowns.get(gameName).put(windowName, currentTime);
                }
            }
        }
    }

    /**
     * 获取最接近怪物的方块
     *
     * @param blockKeys 方块坐标键列表
     * @param monsterLocations 怪物位置列表
     * @param world 世界
     * @return 最接近的方块坐标键
     */
    private String getClosestBlockToMonsters(List<String> blockKeys, List<Location> monsterLocations, World world) {
        if (blockKeys.isEmpty() || monsterLocations.isEmpty()) {
            // 如果列表为空，随机选择一个方块
            return blockKeys.get(new Random().nextInt(blockKeys.size()));
        }

        String closestBlock = null;
        double minDistance = Double.MAX_VALUE;

        for (String blockKey : blockKeys) {
            String[] coords = blockKey.split(",");
            int x = Integer.parseInt(coords[0]);
            int y = Integer.parseInt(coords[1]);
            int z = Integer.parseInt(coords[2]);
            Location blockLoc = new Location(world, x, y, z);

            for (Location monsterLoc : monsterLocations) {
                double distance = blockLoc.distance(monsterLoc);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestBlock = blockKey;
                }
            }
        }

        return closestBlock != null ? closestBlock : blockKeys.get(new Random().nextInt(blockKeys.size()));
    }

    /**
     * 处理玩家修复窗户
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    public void handlePlayerRepairWindow(Player player, String gameName) {
        if (!windowRegions.containsKey(gameName)) {
            return;
        }

        // 检查玩家是否在蹲下状态
        if (!player.isSneaking()) {
            return;
        }

        // 获取玩家位置
        Location playerLoc = player.getLocation();

        // 检查玩家是否在窗户附近
        String nearestWindow = null;
        double minDistance = Double.MAX_VALUE;

        for (Map.Entry<String, Region> entry : windowRegions.get(gameName).entrySet()) {
            String windowName = entry.getKey();
            Region region = entry.getValue();

            // 计算窗户中心位置
            double centerX = (region.getMinX() + region.getMaxX()) / 2;
            double centerY = (region.getMinY() + region.getMaxY()) / 2;
            double centerZ = (region.getMinZ() + region.getMaxZ()) / 2;
            Location centerLocation = new Location(region.getWorld(), centerX, centerY, centerZ);

            // 计算距离
            double distance = playerLoc.distance(centerLocation);
            if (distance <= 5 && distance < minDistance) { // 5格范围内
                minDistance = distance;
                nearestWindow = windowName;
            }
        }

        // 如果玩家不在任何窗户附近
        if (nearestWindow == null) {
            return;
        }

        // 检查冷却时间
        UUID playerId = player.getUniqueId();
        if (!playerRepairCooldowns.containsKey(playerId)) {
            playerRepairCooldowns.put(playerId, new HashMap<>());
        }

        long currentTime = System.currentTimeMillis();
        long lastRepairTime = playerRepairCooldowns.get(playerId).getOrDefault(nearestWindow, 0L);
        if (currentTime - lastRepairTime < REPAIR_COOLDOWN) {
            return; // 冷却中
        }

        // 检查附近是否有怪物 - 使用与checkWindows相同的检测逻辑
        boolean monsterNearby = false;
        for (Entity entity : player.getNearbyEntities(8, 8, 8)) { // 增加检测范围
            // 首先检查实体是否属于当前游戏
            if (!isGameEntity(entity, gameName)) {
                continue;
            }

            // 扩大检测实体类型范围，特别加强idc和idn实体的检测
            if (entity instanceof Zombie
                    || entity instanceof PigZombie
                    || entity instanceof Creeper
                    || entity instanceof Skeleton
                    || entity instanceof org.bukkit.entity.WitherSkeleton // 添加凋零骷髅支持
                    || entity instanceof org.bukkit.entity.Stray // 添加流浪者支持
                    || entity instanceof org.bukkit.entity.Husk // 添加尸壳支持
                    || entity instanceof org.bukkit.entity.Drowned // 添加溺尸支持
                    || entity instanceof org.bukkit.entity.ZombieVillager // 添加僵尸村民支持
                    || entity instanceof Blaze
                    || entity instanceof CaveSpider
                    || entity instanceof Endermite
                    || entity instanceof IronGolem
                    || entity instanceof EnderDragon
                    || entity instanceof org.bukkit.entity.Vindicator // 添加卫道士支持
                    || entity instanceof org.bukkit.entity.Evoker // 添加唤魔者支持
                    || entity instanceof org.bukkit.entity.Ravager // 添加劫掠兽支持
                    || entity instanceof org.bukkit.entity.ZombieHorse // 添加僵尸马支持
                    || entity instanceof org.bukkit.entity.MagmaCube // 添加岩浆怪支持
                    || entity instanceof org.bukkit.entity.Shulker // 添加潜影贝支持
                    || entity instanceof org.bukkit.entity.Snowman // 添加雪傀儡支持
                    || entity instanceof org.bukkit.entity.Wither // 添加凋零支持
                    || entity instanceof org.bukkit.entity.Pillager // 添加掠夺者支持
                    || entity instanceof org.bukkit.entity.Piglin // 添加猪灵支持
                    || isWardenEntity(entity) // 检查是否是Warden实体（如果服务器支持）
                    || entity.hasMetadata("gameEntity")
                    || entity.hasMetadata("customZombieType")
                    || entity.hasMetadata("NPC")
                    || entity.hasMetadata("idcZombieEntity") // 特别检查idc实体标记
                    || entity.hasMetadata("mutationKing") // 检查异变之王标记
                    || entity.hasMetadata("mutantIronGolem") // 检查变异铁傅儡标记
                    || entity.hasMetadata("soulGuardian") // 检查灵魂坚守者标记
                    || entity.hasMetadata("mutantZombie04") // 检查变异僵尸04标记
                    || (entity instanceof LivingEntity && !(entity instanceof Player)
                    && entity.getCustomName() != null
                    && (entity.getCustomName().contains("感染") || entity.getCustomName().contains("僵尸")
                    || entity.getCustomName().contains("变异")
                    || entity.getCustomName().contains("id")
                    || entity.getCustomName().contains("idc")
                    || entity.getCustomName().contains("idn")
                    || entity.getCustomName().contains("zombie")
                    || entity.getCustomName().contains("灵魂")
                    || entity.getCustomName().contains("异变")
                    || entity.getCustomName().contains("灾厄")
                    || entity.getCustomName().contains("凋零")
                    || entity.getCustomName().contains("暗影")
                    || entity.getCustomName().contains("鲜血")
                    || entity.getCustomName().startsWith("游戏_")))) {
                monsterNearby = true;
                break;
            }
        }

        // 如果附近有怪物，不能修复
        if (monsterNearby) {
            messageManager.sendWindowMonstersNearbyMessage(player);
            return;
        }

        // 获取窗户方块状态
        Map<String, Boolean> blockStates = windowBlocks.get(gameName).get(nearestWindow);
        if (blockStates == null || blockStates.isEmpty()) {
            return;
        }

        // 获取所有损坏的方块
        List<String> brokenBlocks = new ArrayList<>();
        for (Map.Entry<String, Boolean> entry : blockStates.entrySet()) {
            if (!entry.getValue()) {
                brokenBlocks.add(entry.getKey());
            }
        }

        // 如果没有损坏的方块
        if (brokenBlocks.isEmpty()) {
            messageManager.sendWindowAlreadyRepairedMessage(player);
            return;
        }

        // 随机选择一个损坏的方块修复
        String blockToRepair = brokenBlocks.get(new Random().nextInt(brokenBlocks.size()));
        String[] coords = blockToRepair.split(",");
        int x = Integer.parseInt(coords[0]);
        int y = Integer.parseInt(coords[1]);
        int z = Integer.parseInt(coords[2]);

        // 修复方块
        Bukkit.getScheduler().runTask(plugin, () -> {
            Block block = player.getWorld().getBlockAt(x, y, z);
            block.setType(WINDOW_MATERIAL);

            // 更新方块状态
            blockStates.put(blockToRepair, true);

            // 播放修复音效 - 更真实的修复窗户音效
            block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.BLOCK_WOOD_PLACE, 1.0f, 1.0f);
            block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_ATTACK_STRONG, 0.5f, 0.8f);
            block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.ITEM_ARMOR_EQUIP_GENERIC, 0.7f, 1.2f);

            // 检查窗户是否完全修复
            boolean fullyRepaired = true;
            for (Boolean state : blockStates.values()) {
                if (!state) {
                    fullyRepaired = false;
                    break;
                }
            }

            // 如果窗户完全修复，播放特殊音效并通知玩家
            if (fullyRepaired) {
                block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
                block.getWorld().playSound(block.getLocation(), org.bukkit.Sound.BLOCK_ANVIL_USE, 0.7f, 1.5f);
                messageManager.sendWindowFullyRepairedMessage(player);
            }

            // 从配置文件获取窗户修复奖励金额
            double windowRepairReward = plugin.getConfig().getDouble("game.money.window_repair", 5.0);

            // 给玩家奖励
            shootPluginHelper.addPlayerMoney(player, windowRepairReward);
            messageManager.sendWindowRepairMessage(player, (int) windowRepairReward);

            // 记录统计数据
            plugin.getPlayerInteractionManager().addPlayerWindowsRepaired(player, gameName, 1);
            plugin.getPlayerInteractionManager().addPlayerMoneyEarned(player, gameName, windowRepairReward);
        });

        // 更新冷却时间
        playerRepairCooldowns.get(playerId).put(nearestWindow, currentTime);
    }

    /**
     * 清理游戏资源
     *
     * @param gameName 游戏名称
     */
    public void cleanupGame(String gameName) {
        // 停止窗户检查任务
        stopWindowCheckTask(gameName);

        // 移除窗户方块
        removeAllWindowBlocks(gameName);

        // 清除窗户数据
        windowRegions.remove(gameName);
        windowBlocks.remove(gameName);
        windowBreakCooldowns.remove(gameName);

        // 清除玩家修复冷却时间
        for (Map<String, Long> cooldowns : playerRepairCooldowns.values()) {
            cooldowns.keySet().removeIf(windowName -> windowName.startsWith(gameName + "."));
        }
    }

    /**
     * 检查实体是否属于指定游戏
     *
     * @param entity 实体
     * @param gameName 游戏名称
     * @return 是否属于游戏
     */
    private boolean isGameEntity(Entity entity, String gameName) {
        // 检查实体是否有游戏会话标记
        if (entity.hasMetadata("gameSession")) {
            String entityGameName = entity.getMetadata("gameSession").get(0).asString();
            return gameName.equals(entityGameName);
        }

        // 检查实体名称是否包含游戏标识
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            if (customName.startsWith("游戏_" + gameName + "_")) {
                return true;
            }
        }

        // 特别检查idc类型实体 - 这些实体有idcZombieEntity标记但可能没有gameSession标记
        if (entity.hasMetadata("idcZombieEntity")) {
            // 对于idc实体，我们认为它们属于当前正在运行的游戏
            // 这是因为idc实体通常是在游戏过程中生成的特殊实体
            if (plugin.getGameSessionManager().getGameState(gameName) == org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
                return true;
            }
        }

        // 检查其他游戏实体标记
        if (entity.hasMetadata("gameEntity") || entity.hasMetadata("customZombieType") || entity.hasMetadata("NPC")) {
            // 对于有游戏实体标记的实体，也认为它们属于当前正在运行的游戏
            if (plugin.getGameSessionManager().getGameState(gameName) == org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
                return true;
            }
        }

        // 检查特殊变异实体标记
        if (entity.hasMetadata("mutationKing") || entity.hasMetadata("mutantIronGolem")
            || entity.hasMetadata("soulGuardian") || entity.hasMetadata("mutantZombie04")
            || entity.hasMetadata("isMutantCreeper")) {
            // 对于变异实体，也认为它们属于当前正在运行的游戏
            if (plugin.getGameSessionManager().getGameState(gameName) == org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
                return true;
            }
        }

        // 如果实体没有明确的游戏标识，则不认为是游戏实体
        return false;
    }

    /**
     * 检查实体是否是Warden类型（如果服务器支持）
     *
     * @param entity 实体
     * @return 是否是Warden实体
     */
    private boolean isWardenEntity(Entity entity) {
        try {
            // 尝试检查是否是Warden实体类型
            EntityType wardenType = EntityType.valueOf("WARDEN");
            return entity.getType() == wardenType;
        } catch (IllegalArgumentException e) {
            // 服务器不支持Warden实体类型，返回false
            return false;
        }
    }
}
