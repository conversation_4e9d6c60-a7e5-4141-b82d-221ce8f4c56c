// 简单的技能配置测试代码
// 用于验证idc9_critical_aura技能的默认参数获取

public class SkillConfigTest {
    
    public static void testCriticalAuraSkill() {
        String skillId = "idc9_critical_aura";
        
        // 模拟技能ID转换逻辑
        String baseSkillId = skillId;
        if (skillId.startsWith("idc") && skillId.contains("_")) {
            String[] parts = skillId.split("_", 2);
            if (parts.length > 1) {
                baseSkillId = parts[1];
            }
        }
        
        System.out.println("原始技能ID: " + skillId);
        System.out.println("转换后技能ID: " + baseSkillId);
        
        // 期望的结果
        System.out.println("期望的转换结果: critical_aura");
        
        // 期望的默认参数
        System.out.println("期望的默认参数:");
        System.out.println("- crit_damage: 8.0");
        System.out.println("- crit_range: 5.0");
        System.out.println("- crit_interval: 60");
        System.out.println("- crit_chance: 0.3");
    }
    
    public static void main(String[] args) {
        testCriticalAuraSkill();
    }
}

/*
测试结果分析：
1. idc9_critical_aura -> critical_aura (转换正确)
2. critical_aura模板应该存在于skillParameterTemplates中
3. 如果getSkillDefaultParameters返回空，说明：
   - critical_aura模板没有正确初始化
   - 或者模板初始化时出现了问题

解决方案：
1. 检查addIDCSkillTemplates方法是否被正确调用
2. 检查critical_aura模板的初始化代码
3. 添加更多调试日志来跟踪模板初始化过程
*/
